#include "MemoryAllocator.h"

// ==================== 静态成员初始化 ====================
size_t MemoryAllocator::s_totalAllocations = 0;
size_t MemoryAllocator::s_totalDeallocations = 0;
size_t MemoryAllocator::s_currentAllocatedSize = 0;
size_t MemoryAllocator::s_peakMemoryUsage = 0;
size_t MemoryAllocator::s_psramUsage = 0;
size_t MemoryAllocator::s_heapUsage = 0;

MemoryAllocator::MemoryPool* MemoryAllocator::s_memoryPools = nullptr;
size_t MemoryAllocator::s_poolCount = 0;

#ifdef DEBUG_MODE
bool MemoryAllocator::s_debugMode = false;
MemoryAllocator::AllocationInfo* MemoryAllocator::s_allocations = nullptr;
size_t MemoryAllocator::s_allocationCount = 0;
size_t MemoryAllocator::s_maxAllocations = 100;
#endif

// ==================== 初始化方法 ====================
bool MemoryAllocator::initialize() {
    Serial.println("🧠 Initializing MemoryAllocator...");
    
    // 初始化内存池
    if (!initializeMemoryPools()) {
        Serial.println("❌ Failed to initialize memory pools");
        return false;
    }
    
#ifdef DEBUG_MODE
    // 初始化调试分配跟踪
    if (!s_allocations) {
        s_allocations = (AllocationInfo*)malloc(s_maxAllocations * sizeof(AllocationInfo));
        if (!s_allocations) {
            Serial.println("❌ Failed to initialize allocation tracking");
            return false;
        }
        s_allocationCount = 0;
    }
#endif
    
    Serial.println("✅ MemoryAllocator initialized successfully");
    return true;
}

// ==================== 核心内存分配接口 ====================
void* MemoryAllocator::smartAlloc(size_t size) {
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    bool isPSRAM = false;
    
    // 策略选择
    if (shouldUsePSRAM(size)) {
        // 尝试使用PSRAM
        ptr = ps_malloc(size);
        if (ptr) {
            isPSRAM = true;
            s_psramUsage += size;
        }
    }
    
    // 如果PSRAM分配失败或不适用，使用普通RAM
    if (!ptr) {
        ptr = malloc(size);
        if (ptr) {
            s_heapUsage += size;
        }
    }
    
    // 更新统计信息
    if (ptr) {
        s_totalAllocations++;
        s_currentAllocatedSize += size;
        if (s_currentAllocatedSize > s_peakMemoryUsage) {
            s_peakMemoryUsage = s_currentAllocatedSize;
        }
        
        // 调试模式下记录分配信息
        #ifdef DEBUG_MODE
        if (s_debugMode) {
            trackAllocation(ptr, size, isPSRAM);
        }
        #endif
    }
    
    return ptr;
}

void MemoryAllocator::smartFree(void* ptr) {
    if (!ptr) {
        return;
    }
    
    // 查找分配信息以更新统计
    #ifdef DEBUG_MODE
    if (s_debugMode) {
        AllocationInfo* info = findAllocationInfo(ptr);
        if (info) {
            if (info->isPSRAM) {
                s_psramUsage -= info->size;
            } else {
                s_heapUsage -= info->size;
            }
            s_currentAllocatedSize -= info->size;
            trackDeallocation(ptr);
        }
    }
    #endif
    
    s_totalDeallocations++;
    
    // 释放内存（free函数可以处理PSRAM和普通RAM）
    free(ptr);
}

void* MemoryAllocator::smartRealloc(void* ptr, size_t newSize) {
    if (!ptr) {
        return smartAlloc(newSize);
    }
    
    if (newSize == 0) {
        smartFree(ptr);
        return nullptr;
    }
    
    // 简化实现：分配新内存，复制数据，释放旧内存
    void* newPtr = smartAlloc(newSize);
    if (newPtr && ptr) {
        // 这里应该知道原始大小，但为了简化，我们假设复制较小的大小
        // 在实际实现中，应该跟踪原始分配大小
        memcpy(newPtr, ptr, newSize);  // 注意：这可能不安全
        smartFree(ptr);
    }
    
    return newPtr;
}

void* MemoryAllocator::smartCalloc(size_t count, size_t size) {
    size_t totalSize = count * size;
    void* ptr = smartAlloc(totalSize);
    if (ptr) {
        memset(ptr, 0, totalSize);
    }
    return ptr;
}

// ==================== 专用分配器 ====================
void* MemoryAllocator::allocFromPSRAM(size_t size) {
    if (!PSRAMManager::isPSRAMAvailable()) {
        return nullptr;
    }
    
    void* ptr = ps_malloc(size);
    if (ptr) {
        s_totalAllocations++;
        s_psramUsage += size;
        s_currentAllocatedSize += size;
        if (s_currentAllocatedSize > s_peakMemoryUsage) {
            s_peakMemoryUsage = s_currentAllocatedSize;
        }
        
        #ifdef DEBUG_MODE
        if (s_debugMode) {
            trackAllocation(ptr, size, true);
        }
        #endif
    }
    
    return ptr;
}

void* MemoryAllocator::allocFromHeap(size_t size) {
    void* ptr = malloc(size);
    if (ptr) {
        s_totalAllocations++;
        s_heapUsage += size;
        s_currentAllocatedSize += size;
        if (s_currentAllocatedSize > s_peakMemoryUsage) {
            s_peakMemoryUsage = s_currentAllocatedSize;
        }
        
        #ifdef DEBUG_MODE
        if (s_debugMode) {
            trackAllocation(ptr, size, false);
        }
        #endif
    }
    
    return ptr;
}

void* MemoryAllocator::allocDMAMemory(size_t size) {
    // DMA内存必须从普通RAM分配
    return allocFromHeap(size);
}

// ==================== 内存池管理 ====================
bool MemoryAllocator::initializeMemoryPools() {
    // 简化实现：暂时不实现内存池
    s_memoryPools = nullptr;
    s_poolCount = 0;
    return true;
}

void* MemoryAllocator::allocFromPool(size_t size) {
    // 简化实现：直接使用智能分配
    return smartAlloc(size);
}

void MemoryAllocator::freeToPool(void* ptr, size_t size) {
    // 简化实现：直接释放
    smartFree(ptr);
}

void MemoryAllocator::cleanupMemoryPools() {
    // 简化实现：无需清理
}

// ==================== 内存健康检查 ====================
bool MemoryAllocator::checkMemoryLeaks() {
    return s_totalAllocations == s_totalDeallocations;
}

float MemoryAllocator::getFragmentationLevel() {
    // 简化实现：返回估算值
    size_t totalFree = ESP.getFreeHeap() + PSRAMManager::getFreePSRAM();
    size_t largestBlock = ESP.getMaxAllocHeap();
    
    if (totalFree == 0) return 0.0f;
    return 1.0f - (float)largestBlock / totalFree;
}

bool MemoryAllocator::checkMemoryIntegrity() {
    // 简化实现：假设内存总是完整的
    return true;
}

// ==================== 内存清理 ====================
void MemoryAllocator::performCleanup() {
    // 执行内存清理操作
    cleanupMemoryPools();
    
    #ifdef DEBUG_MODE
    if (s_debugMode) {
        // 清理过期的分配记录
        // 简化实现：暂不处理
    }
    #endif
}

void MemoryAllocator::forceGarbageCollection() {
    // ESP32没有垃圾回收，这里是占位符
    performCleanup();
}

void MemoryAllocator::defragmentMemory() {
    // ESP32无法直接整理内存碎片，这里是占位符
}

// ==================== 调试功能 ====================
void MemoryAllocator::printMemoryStats() {
    Serial.println("📊 Memory Allocator Statistics:");
    Serial.printf("   Total Allocations: %zu\n", s_totalAllocations);
    Serial.printf("   Total Deallocations: %zu\n", s_totalDeallocations);
    Serial.printf("   Current Allocated: %zu bytes\n", s_currentAllocatedSize);
    Serial.printf("   Peak Usage: %zu bytes\n", s_peakMemoryUsage);
    Serial.printf("   PSRAM Usage: %zu bytes\n", s_psramUsage);
    Serial.printf("   Heap Usage: %zu bytes\n", s_heapUsage);
    Serial.printf("   Memory Leaks: %s\n", checkMemoryLeaks() ? "None" : "Detected");
    Serial.printf("   Fragmentation: %.1f%%\n", getFragmentationLevel() * 100);
}

void MemoryAllocator::printAllocationDetails() {
    printMemoryStats();
    
    #ifdef DEBUG_MODE
    if (s_debugMode && s_allocations) {
        Serial.printf("   Active Allocations: %zu\n", s_allocationCount);
        for (size_t i = 0; i < s_allocationCount; i++) {
            Serial.printf("     [%zu] %p: %zu bytes (%s)\n", 
                         i, s_allocations[i].ptr, s_allocations[i].size,
                         s_allocations[i].isPSRAM ? "PSRAM" : "Heap");
        }
    }
    #endif
}

String MemoryAllocator::generateMemoryReport() {
    String report = "Memory Allocator Report:\n";
    report += "Total Allocations: " + String(s_totalAllocations) + "\n";
    report += "Total Deallocations: " + String(s_totalDeallocations) + "\n";
    report += "Current Allocated: " + String(s_currentAllocatedSize) + " bytes\n";
    report += "Peak Usage: " + String(s_peakMemoryUsage) + " bytes\n";
    report += "PSRAM Usage: " + String(s_psramUsage) + " bytes\n";
    report += "Heap Usage: " + String(s_heapUsage) + " bytes\n";
    report += "Memory Leaks: " + String(checkMemoryLeaks() ? "None" : "Detected") + "\n";
    report += "Fragmentation: " + String(getFragmentationLevel() * 100, 1) + "%\n";
    
    return report;
}

// ==================== 私有方法实现 ====================
bool MemoryAllocator::shouldUsePSRAM(size_t size) {
    return PSRAMManager::isPSRAMAvailable() && size >= PSRAM_THRESHOLD;
}

int MemoryAllocator::getBestAllocationStrategy(size_t size) {
    if (isMemoryUnderPressure()) {
        // 内存紧张时优先使用PSRAM
        if (PSRAMManager::isPSRAMAvailable()) {
            return 1; // PSRAM
        }
    }

    if (size >= LARGE_ALLOCATION_THRESHOLD && PSRAMManager::isPSRAMAvailable()) {
        return 1; // PSRAM
    } else if (size <= SMALL_ALLOCATION_THRESHOLD) {
        return 2; // Pool (如果实现了)
    }

    return 0; // Heap
}

bool MemoryAllocator::isMemoryUnderPressure() {
    size_t freeHeap = ESP.getFreeHeap();
    size_t freePSRAM = PSRAMManager::getFreePSRAM();

    return freeHeap < MIN_FREE_HEAP || freePSRAM < MIN_FREE_PSRAM;
}

#ifdef DEBUG_MODE
void MemoryAllocator::trackAllocation(void* ptr, size_t size, bool isPSRAM) {
    if (!s_allocations || s_allocationCount >= s_maxAllocations) {
        return;
    }

    AllocationInfo& info = s_allocations[s_allocationCount];
    info.ptr = ptr;
    info.size = size;
    info.isPSRAM = isPSRAM;
    info.timestamp = millis();
    info.file = nullptr;
    info.line = 0;

    s_allocationCount++;
}

void MemoryAllocator::trackDeallocation(void* ptr) {
    if (!s_allocations) {
        return;
    }

    // 查找并移除分配记录
    for (size_t i = 0; i < s_allocationCount; i++) {
        if (s_allocations[i].ptr == ptr) {
            // 移动后续元素
            for (size_t j = i; j < s_allocationCount - 1; j++) {
                s_allocations[j] = s_allocations[j + 1];
            }
            s_allocationCount--;
            break;
        }
    }
}

MemoryAllocator::AllocationInfo* MemoryAllocator::findAllocationInfo(void* ptr) {
    if (!s_allocations) {
        return nullptr;
    }

    for (size_t i = 0; i < s_allocationCount; i++) {
        if (s_allocations[i].ptr == ptr) {
            return &s_allocations[i];
        }
    }

    return nullptr;
}

void MemoryAllocator::enableDebugMode() {
    s_debugMode = true;
    Serial.println("🐛 Memory debug mode enabled");
}

void MemoryAllocator::disableDebugMode() {
    s_debugMode = false;
    Serial.println("🐛 Memory debug mode disabled");
}

void MemoryAllocator::printAllocationTrace() {
    if (!s_debugMode || !s_allocations) {
        Serial.println("Debug mode not enabled or no allocation tracking");
        return;
    }

    Serial.printf("📋 Allocation Trace (%zu active allocations):\n", s_allocationCount);
    for (size_t i = 0; i < s_allocationCount; i++) {
        const AllocationInfo& info = s_allocations[i];
        Serial.printf("  [%zu] %p: %zu bytes (%s) at %lu ms\n",
                     i, info.ptr, info.size,
                     info.isPSRAM ? "PSRAM" : "Heap",
                     info.timestamp);
    }
}
#endif

// ==================== 内联函数的具体实现 ====================
size_t MemoryAllocator::getTotalAllocations() {
    return s_totalAllocations;
}

size_t MemoryAllocator::getTotalDeallocations() {
    return s_totalDeallocations;
}

size_t MemoryAllocator::getCurrentAllocatedSize() {
    return s_currentAllocatedSize;
}

size_t MemoryAllocator::getPeakMemoryUsage() {
    return s_peakMemoryUsage;
}

size_t MemoryAllocator::getPSRAMUsage() {
    return s_psramUsage;
}

size_t MemoryAllocator::getHeapUsage() {
    return s_heapUsage;
}

size_t MemoryAllocator::getAvailableMemory() {
    size_t heapFree = ESP.getFreeHeap();
    size_t psramFree = PSRAMManager::getFreePSRAM();
    return heapFree + psramFree;
}
