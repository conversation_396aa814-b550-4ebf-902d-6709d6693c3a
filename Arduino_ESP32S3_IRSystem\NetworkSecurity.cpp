#include "NetworkSecurity.h"

// ==================== 构造函数和析构函数 ====================
NetworkSecurity::NetworkSecurity()
    : m_initialized(false)
    , m_totalRequests(0)
    , m_blockedRequests(0)
    , m_rateLimitedRequests(0)
    , m_invalidTokenRequests(0)
    , m_corsViolations(0)
{
    Serial.println("🔒 NetworkSecurity created");
}

NetworkSecurity::~NetworkSecurity() {
    shutdown();
    Serial.println("🔒 NetworkSecurity destroyed");
}

// ==================== 系统生命周期 ====================
bool NetworkSecurity::initialize() {
    if (m_initialized) {
        Serial.println("⚠️ NetworkSecurity already initialized");
        return true;
    }
    
    Serial.println("🔒 Initializing NetworkSecurity...");
    
    // 清空所有映射表
    m_sessions.clear();
    m_rateLimits.clear();
    m_blockedIPs.clear();
    
    // 重置统计
    m_totalRequests = 0;
    m_blockedRequests = 0;
    m_rateLimitedRequests = 0;
    m_invalidTokenRequests = 0;
    m_corsViolations = 0;
    
    m_initialized = true;
    Serial.println("✅ NetworkSecurity initialization completed");
    return true;
}

void NetworkSecurity::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("🔒 Shutting down NetworkSecurity...");
    
    // 清空所有数据
    m_sessions.clear();
    m_rateLimits.clear();
    m_blockedIPs.clear();
    
    m_initialized = false;
    Serial.println("✅ NetworkSecurity shutdown completed");
}

bool NetworkSecurity::isHealthy() const {
    return m_initialized;
}

// ==================== 认证功能 ====================
bool NetworkSecurity::validateAPIKey(const String& apiKey) {
    if (!m_initialized || apiKey.isEmpty()) {
        return false;
    }
    
    // 简化实现：接受任何非空API密钥
    // 实际实现应该验证预设的API密钥
    return apiKey.length() >= 8;
}

String NetworkSecurity::generateSessionToken(const String& clientId) {
    if (!m_initialized || clientId.isEmpty()) {
        return "";
    }
    
    String token = generateRandomToken(32);
    
    SessionInfo session;
    session.clientId = clientId;
    session.createdTime = millis();
    session.lastAccessTime = millis();
    session.isValid = true;
    
    m_sessions[token] = session;
    
    Serial.printf("🔒 Session token generated for client: %s\n", clientId.c_str());
    return token;
}

bool NetworkSecurity::validateSessionToken(const String& token) {
    if (!m_initialized || token.isEmpty()) {
        return false;
    }
    
    auto it = m_sessions.find(token);
    if (it == m_sessions.end()) {
        m_invalidTokenRequests++;
        return false;
    }
    
    SessionInfo& session = it->second;
    
    // 检查会话是否过期
    if (isSessionExpired(session)) {
        m_sessions.erase(it);
        m_invalidTokenRequests++;
        return false;
    }
    
    // 更新最后访问时间
    session.lastAccessTime = millis();
    return session.isValid;
}

bool NetworkSecurity::revokeSessionToken(const String& token) {
    if (!m_initialized) {
        return false;
    }
    
    auto it = m_sessions.find(token);
    if (it != m_sessions.end()) {
        m_sessions.erase(it);
        Serial.printf("🔒 Session token revoked: %s\n", token.c_str());
        return true;
    }
    
    return false;
}

int NetworkSecurity::cleanupExpiredTokens() {
    if (!m_initialized) {
        return 0;
    }
    
    int cleanedCount = 0;
    auto it = m_sessions.begin();
    
    while (it != m_sessions.end()) {
        if (isSessionExpired(it->second)) {
            it = m_sessions.erase(it);
            cleanedCount++;
        } else {
            ++it;
        }
    }
    
    if (cleanedCount > 0) {
        Serial.printf("🔒 Cleaned up %d expired tokens\n", cleanedCount);
    }
    
    return cleanedCount;
}

// ==================== 速率限制 ====================
bool NetworkSecurity::checkRateLimit(const String& clientIP, const String& endpoint) {
    if (!m_initialized) {
        return false; // 不限制
    }
    
    m_totalRequests++;
    
    String key = clientIP + ":" + endpoint;
    unsigned long currentTime = millis();
    
    auto it = m_rateLimits.find(key);
    if (it == m_rateLimits.end()) {
        // 首次请求，创建记录
        RateLimitInfo info;
        info.windowStart = currentTime;
        info.requestCount = 1;
        info.isBlocked = false;
        info.blockUntil = 0;
        
        m_rateLimits[key] = info;
        return false; // 不限制
    }
    
    RateLimitInfo& info = it->second;
    
    // 检查是否在阻止期间
    if (info.isBlocked && currentTime < info.blockUntil) {
        m_rateLimitedRequests++;
        return true; // 限制
    }
    
    // 检查是否需要重置窗口
    if (shouldResetRateLimitWindow(info)) {
        info.windowStart = currentTime;
        info.requestCount = 1;
        info.isBlocked = false;
        return false; // 不限制
    }
    
    // 增加请求计数
    info.requestCount++;
    
    // 检查是否超过限制（简化：每分钟最多100个请求）
    if (info.requestCount > 100) {
        info.isBlocked = true;
        info.blockUntil = currentTime + 60000; // 阻止1分钟
        m_rateLimitedRequests++;
        
        Serial.printf("🔒 Rate limit exceeded for %s\n", clientIP.c_str());
        return true; // 限制
    }
    
    return false; // 不限制
}

void NetworkSecurity::recordRequest(const String& clientIP, const String& endpoint) {
    // 请求已在checkRateLimit中记录
}

void NetworkSecurity::resetRateLimit(const String& clientIP) {
    if (!m_initialized) {
        return;
    }
    
    // 移除该IP的所有速率限制记录
    auto it = m_rateLimits.begin();
    while (it != m_rateLimits.end()) {
        if (it->first.startsWith(clientIP + ":")) {
            it = m_rateLimits.erase(it);
        } else {
            ++it;
        }
    }
    
    Serial.printf("🔒 Rate limit reset for %s\n", clientIP.c_str());
}

DynamicJsonDocument NetworkSecurity::getRateLimitStatus(const String& clientIP) {
    DynamicJsonDocument status(512);
    
    status["client_ip"] = clientIP;
    status["timestamp"] = millis();
    
    JsonArray limits = status.createNestedArray("limits");
    
    for (const auto& pair : m_rateLimits) {
        if (pair.first.startsWith(clientIP + ":")) {
            JsonObject limit = limits.createNestedObject();
            limit["endpoint"] = pair.first.substring(clientIP.length() + 1);
            limit["request_count"] = pair.second.requestCount;
            limit["is_blocked"] = pair.second.isBlocked;
            limit["block_until"] = pair.second.blockUntil;
        }
    }
    
    return status;
}

// ==================== CORS处理 ====================
bool NetworkSecurity::validateCORSRequest(const String& origin, const String& method) {
    if (!m_initialized) {
        return true; // 允许所有请求
    }
    
    // 简化实现：允许所有来源和方法
    // 实际实现应该检查允许的来源列表
    return true;
}

std::map<String, String> NetworkSecurity::getCORSHeaders(const String& origin) {
    std::map<String, String> headers;
    
    headers["Access-Control-Allow-Origin"] = "*";
    headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS";
    headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-API-Key";
    headers["Access-Control-Max-Age"] = "86400";
    
    return headers;
}

// ==================== IP过滤 ====================
bool NetworkSecurity::isIPBlocked(const String& clientIP) {
    if (!m_initialized) {
        return false;
    }
    
    auto it = m_blockedIPs.find(clientIP);
    if (it == m_blockedIPs.end()) {
        return false;
    }
    
    const BlockedIPInfo& info = it->second;
    
    // 检查阻止是否过期
    if (!info.isPermanent && isIPBlockExpired(info)) {
        m_blockedIPs.erase(it);
        return false;
    }
    
    m_blockedRequests++;
    return true;
}

void NetworkSecurity::blockIP(const String& clientIP, const String& reason, unsigned long duration) {
    if (!m_initialized) {
        return;
    }
    
    BlockedIPInfo info;
    info.reason = reason;
    info.blockedTime = millis();
    info.blockDuration = duration;
    info.isPermanent = (duration == 0);
    
    m_blockedIPs[clientIP] = info;
    
    Serial.printf("🔒 IP blocked: %s (reason: %s)\n", clientIP.c_str(), reason.c_str());
}

bool NetworkSecurity::unblockIP(const String& clientIP) {
    if (!m_initialized) {
        return false;
    }
    
    auto it = m_blockedIPs.find(clientIP);
    if (it != m_blockedIPs.end()) {
        m_blockedIPs.erase(it);
        Serial.printf("🔒 IP unblocked: %s\n", clientIP.c_str());
        return true;
    }
    
    return false;
}

DynamicJsonDocument NetworkSecurity::getBlockedIPs() {
    DynamicJsonDocument blocked(512);
    
    blocked["timestamp"] = millis();
    blocked["count"] = m_blockedIPs.size();
    
    JsonArray ips = blocked.createNestedArray("blocked_ips");
    
    for (const auto& pair : m_blockedIPs) {
        JsonObject ipInfo = ips.createNestedObject();
        ipInfo["ip"] = pair.first;
        ipInfo["reason"] = pair.second.reason;
        ipInfo["blocked_time"] = pair.second.blockedTime;
        ipInfo["is_permanent"] = pair.second.isPermanent;
        
        if (!pair.second.isPermanent) {
            ipInfo["expires_at"] = pair.second.blockedTime + pair.second.blockDuration;
        }
    }
    
    return blocked;
}

// ==================== 安全统计 ====================
DynamicJsonDocument NetworkSecurity::getSecurityStats() {
    DynamicJsonDocument stats(512);
    
    stats["total_requests"] = m_totalRequests;
    stats["blocked_requests"] = m_blockedRequests;
    stats["rate_limited_requests"] = m_rateLimitedRequests;
    stats["invalid_token_requests"] = m_invalidTokenRequests;
    stats["cors_violations"] = m_corsViolations;
    stats["active_sessions"] = m_sessions.size();
    stats["blocked_ips"] = m_blockedIPs.size();
    stats["rate_limit_entries"] = m_rateLimits.size();
    
    if (m_totalRequests > 0) {
        stats["block_rate"] = (float)m_blockedRequests / m_totalRequests * 100;
    } else {
        stats["block_rate"] = 0.0f;
    }
    
    return stats;
}

void NetworkSecurity::resetSecurityStats() {
    m_totalRequests = 0;
    m_blockedRequests = 0;
    m_rateLimitedRequests = 0;
    m_invalidTokenRequests = 0;
    m_corsViolations = 0;
    
    Serial.println("🔒 Security statistics reset");
}

void NetworkSecurity::logSecurityEvent(const String& eventType, const String& clientIP, const String& details) {
    Serial.printf("🔒 Security Event [%s] from %s: %s\n", 
                 eventType.c_str(), clientIP.c_str(), details.c_str());
    
    updateSecurityStats(eventType);
}

// ==================== 私有方法实现 ====================
String NetworkSecurity::generateRandomToken(int length) {
    const char charset[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    String token = "";

    for (int i = 0; i < length; i++) {
        token += charset[random(0, sizeof(charset) - 1)];
    }

    return token;
}

String NetworkSecurity::calculateTokenHash(const String& token) {
    // 简化实现：返回token的简单哈希
    unsigned long hash = 0;
    for (char c : token) {
        hash = hash * 31 + c;
    }
    return String(hash, HEX);
}

bool NetworkSecurity::isSessionExpired(const SessionInfo& session) {
    unsigned long currentTime = millis();
    unsigned long sessionAge = currentTime - session.createdTime;
    unsigned long lastAccess = currentTime - session.lastAccessTime;

    // 会话最长24小时，最后访问超过1小时则过期
    return (sessionAge > 86400000) || (lastAccess > 3600000);
}

bool NetworkSecurity::shouldResetRateLimitWindow(const RateLimitInfo& rateLimitInfo) {
    unsigned long currentTime = millis();
    unsigned long windowAge = currentTime - rateLimitInfo.windowStart;

    // 窗口时间为1分钟
    return windowAge > 60000;
}

bool NetworkSecurity::isIPBlockExpired(const BlockedIPInfo& blockedInfo) {
    if (blockedInfo.isPermanent) {
        return false;
    }

    unsigned long currentTime = millis();
    return currentTime > (blockedInfo.blockedTime + blockedInfo.blockDuration);
}

void NetworkSecurity::performCleanup() {
    // 清理过期的会话
    cleanupExpiredTokens();

    // 清理过期的IP阻止
    auto it = m_blockedIPs.begin();
    while (it != m_blockedIPs.end()) {
        if (!it->second.isPermanent && isIPBlockExpired(it->second)) {
            Serial.printf("🔒 Removing expired IP block: %s\n", it->first.c_str());
            it = m_blockedIPs.erase(it);
        } else {
            ++it;
        }
    }

    // 清理过期的速率限制记录
    auto rateLimitIt = m_rateLimits.begin();
    while (rateLimitIt != m_rateLimits.end()) {
        if (shouldResetRateLimitWindow(rateLimitIt->second)) {
            rateLimitIt = m_rateLimits.erase(rateLimitIt);
        } else {
            ++rateLimitIt;
        }
    }
}

void NetworkSecurity::updateSecurityStats(const String& eventType) {
    if (eventType == "blocked") {
        m_blockedRequests++;
    } else if (eventType == "rate_limited") {
        m_rateLimitedRequests++;
    } else if (eventType == "invalid_token") {
        m_invalidTokenRequests++;
    } else if (eventType == "cors_violation") {
        m_corsViolations++;
    }
}
