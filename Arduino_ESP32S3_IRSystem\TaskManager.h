#ifndef TASK_MANAGER_H
#define TASK_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include "system-config.h"
#include "DataStructures.h"

// Forward declarations
class DataManager;
class IRController;

/**
 * 任务管理器类
 * 
 * 负责任务的创建、执行、调度和监控
 * 
 * 核心功能：
 * - 任务创建和管理
 * - 任务执行调度
 * - 任务状态监控
 * - 定时任务处理
 */
class TaskManager {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     */
    TaskManager();
    
    /**
     * 析构函数
     */
    ~TaskManager();
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化任务管理器
     * @return bool 初始化是否成功
     */
    bool initialize();
    
    /**
     * 关闭任务管理器
     */
    void shutdown();
    
    /**
     * 检查健康状态
     * @return bool 是否健康
     */
    bool isHealthy() const;
    
    /**
     * 设置依赖组件
     * @param dataManager 数据管理器
     * @param irController 红外控制器
     */
    void setDependencies(DataManager* dataManager, IRController* irController);
    
    // ==================== 任务执行 ====================
    
    /**
     * 执行单个信号
     * @param signalId 信号ID
     * @return bool 执行是否成功
     */
    bool executeSingleSignal(const String& signalId);
    
    /**
     * 执行所有信号
     * @param interval 信号间隔（毫秒）
     * @return bool 执行是否成功
     */
    bool executeAllSignals(int interval = 100);
    
    /**
     * 执行选定信号
     * @param signalIds 信号ID列表
     * @param interval 信号间隔（毫秒）
     * @return bool 执行是否成功
     */
    bool executeSelectedSignals(const std::vector<String>& signalIds, int interval = 100);
    
    /**
     * 执行任务
     * @param taskId 任务ID
     * @return bool 执行是否成功
     */
    bool executeTask(const String& taskId);
    
    /**
     * 停止当前执行
     */
    void stopExecution();
    
    /**
     * 暂停当前执行
     */
    void pauseExecution();
    
    /**
     * 恢复执行
     */
    void resumeExecution();
    
    // ==================== 任务管理 ====================
    
    /**
     * 创建任务
     * @param taskData 任务数据
     * @return String 任务ID
     */
    String createTask(const DynamicJsonDocument& taskData);
    
    /**
     * 更新任务
     * @param taskId 任务ID
     * @param taskData 任务数据
     * @return bool 更新是否成功
     */
    bool updateTask(const String& taskId, const DynamicJsonDocument& taskData);
    
    /**
     * 删除任务
     * @param taskId 任务ID
     * @return bool 删除是否成功
     */
    bool deleteTask(const String& taskId);
    
    /**
     * 获取任务状态
     * @param taskId 任务ID
     * @return DynamicJsonDocument 任务状态
     */
    DynamicJsonDocument getTaskStatus(const String& taskId);
    
    /**
     * 获取所有任务状态
     * @return DynamicJsonDocument 所有任务状态
     */
    DynamicJsonDocument getAllTasksStatus();
    
    // ==================== 定时任务 ====================
    
    /**
     * 添加定时任务
     * @param timerData 定时器数据
     * @return String 定时器ID
     */
    String addScheduledTask(const DynamicJsonDocument& timerData);
    
    /**
     * 移除定时任务
     * @param timerId 定时器ID
     * @return bool 移除是否成功
     */
    bool removeScheduledTask(const String& timerId);
    
    /**
     * 检查定时任务
     */
    void checkScheduledTasks();
    
    /**
     * 获取定时任务状态
     * @return DynamicJsonDocument 定时任务状态
     */
    DynamicJsonDocument getScheduledTasksStatus();
    
    // ==================== 任务队列 ====================
    
    /**
     * 添加任务到队列
     * @param taskId 任务ID
     * @param priority 优先级
     * @return bool 添加是否成功
     */
    bool addTaskToQueue(const String& taskId, int priority = 0);
    
    /**
     * 从队列移除任务
     * @param taskId 任务ID
     * @return bool 移除是否成功
     */
    bool removeTaskFromQueue(const String& taskId);
    
    /**
     * 清空任务队列
     */
    void clearTaskQueue();
    
    /**
     * 获取队列状态
     * @return DynamicJsonDocument 队列状态
     */
    DynamicJsonDocument getQueueStatus();
    
    /**
     * 处理任务队列
     */
    void processTaskQueue();
    
    // ==================== 状态监控 ====================
    
    /**
     * 获取执行状态
     * @return DynamicJsonDocument 执行状态
     */
    DynamicJsonDocument getExecutionStatus();
    
    /**
     * 获取统计信息
     * @return DynamicJsonDocument 统计信息
     */
    DynamicJsonDocument getStatistics();
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    /**
     * 检查是否正在执行
     * @return bool 是否正在执行
     */
    bool isExecuting() const;
    
    /**
     * 检查是否已暂停
     * @return bool 是否已暂停
     */
    bool isPaused() const;
    
    // ==================== 循环处理 ====================
    
    /**
     * 主循环处理
     * 应在主循环中调用
     */
    void loop();
    
    /**
     * 更新任务状态
     */
    void updateTaskStates();
    
    /**
     * 清理完成的任务
     */
    void cleanupCompletedTasks();

private:
    // ==================== 私有成员变量 ====================
    
    bool m_initialized;                 // 是否已初始化
    bool m_isExecuting;                 // 是否正在执行
    bool m_isPaused;                    // 是否已暂停
    
    // 依赖组件
    DataManager* m_dataManager;
    IRController* m_irController;
    
    // 当前执行状态
    String m_currentTaskId;             // 当前任务ID
    TaskStatus m_currentStatus;         // 当前状态
    int m_currentSignalIndex;           // 当前信号索引
    int m_currentLoop;                  // 当前循环次数
    unsigned long m_lastExecutionTime;  // 最后执行时间
    unsigned long m_executionStartTime; // 执行开始时间
    
    // 任务队列
    struct QueuedTask {
        String taskId;
        int priority;
        unsigned long queueTime;
    };
    std::vector<QueuedTask> m_taskQueue;
    
    // 定时任务
    std::vector<String> m_scheduledTasks;
    unsigned long m_lastScheduleCheck;
    
    // 统计信息
    unsigned long m_totalExecutions;    // 总执行次数
    unsigned long m_successfulExecutions; // 成功执行次数
    unsigned long m_failedExecutions;   // 失败执行次数
    unsigned long m_totalSignalsSent;   // 总发送信号数
    
    // ==================== 私有方法 ====================
    
    /**
     * 执行任务内部实现
     * @param task 任务数据
     * @return bool 执行是否成功
     */
    bool executeTaskInternal(TaskData* task);
    
    /**
     * 发送信号
     * @param signalId 信号ID
     * @return bool 发送是否成功
     */
    bool sendSignal(const String& signalId);
    
    /**
     * 更新任务进度
     * @param taskId 任务ID
     * @param progress 进度信息
     */
    void updateTaskProgress(const String& taskId, const DynamicJsonDocument& progress);
    
    /**
     * 检查任务超时
     * @param taskId 任务ID
     * @return bool 是否超时
     */
    bool checkTaskTimeout(const String& taskId);
    
    /**
     * 处理任务错误
     * @param taskId 任务ID
     * @param error 错误信息
     */
    void handleTaskError(const String& taskId, const String& error);
    
    /**
     * 记录执行日志
     * @param message 日志信息
     */
    void logExecution(const String& message);
    
    /**
     * 验证任务数据
     * @param taskData 任务数据
     * @return bool 数据是否有效
     */
    bool validateTaskData(const DynamicJsonDocument& taskData);
    
    /**
     * 排序任务队列
     */
    void sortTaskQueue();
};

#endif // TASK_MANAGER_H
