/**
 * R1系统 - 定时设置模块
 * 实现定时任务创建、管理、调度等功能
 */

class TimerSettings extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'TimerSettings');

    // 定时器状态 - 基于旧系统分析的完整状态管理
    this.timerState = {
      isTimerEnabled: false,        // 定时器总开关
      isSignalSelectionMode: false, // 是否在信号选择模式
      nextExecutionTime: 0,         // 下次执行时间
      lastExecutionTime: 0,         // 上次执行时间
      executionCount: 0,            // 执行次数
      currentTaskId: ''             // 当前任务ID
    };

    // 定时器设置
    this.timerSettings = {
      startTime: '09:00',           // 开始时间
      endTime: '18:00',             // 结束时间
      selectedSignalIds: [],        // 选中的信号ID
      isDaily: true,                // 是否每日重复
      intervalMinutes: 60,          // 执行间隔（分钟）
      isEnabled: false              // 是否启用
    };

    // 定时任务管理 - 使用统一定时器管理器
    this.timerTasks = [];
    this.activeTasks = []; // 激活的任务队列
    this.timerManager = window.UnifiedTimerManager;
    this.currentTimer = null;
    this.editingTaskId = null;
    this.nextTaskId = 1;


  }

  async setupEventListeners() {
    // 监听系统级事件
    this.eventBus.on('system.refresh', () => {
      this.refresh();
    });



    // 监听信号选择相关事件
    this.eventBus.on('signal.selection.completed', (data) => {
      this.handleSignalSelectionCompleted(data);
    });

    this.eventBus.on('signal.selection.cancelled', (data) => {
      this.handleSignalSelectionCancelled(data);
    });

    this.eventBus.on('signal.selection.changed', (data) => {
      this.handleSignalSelectionChanged(data);
    });

    // 监听任务执行相关事件 - 事件驱动架构
    this.eventBus.on('timer.task.execution.started', (data) => {
      this.onTaskExecutionStarted(data);
    });

    this.eventBus.on('timer.task.execution.completed', (data) => {
      this.onTaskExecutionCompleted(data);
    });

    this.eventBus.on('timer.task.execution.failed', (data) => {
      this.onTaskExecutionFailed(data);
    });

    // 监听控制模块的任务执行请求
    this.eventBus.on('timer.task.due', (data) => {
      this.onTaskDue(data);
    });

    // 监听定时任务信息请求
    this.eventBus.on('timer.task.info.request', (data) => {
      this.handleTaskInfoRequest(data);
    });

    console.log('TimerSettings: 事件监听器设置完成');
  }

  async setupUI() {
    // 临时显示开发中提示
    this.renderTimerPanel();
  }

  async loadModuleData() {
    // 待重新实现
    console.log('TimerSettings: 数据加载待重新实现');
  }

  /**
   * 渲染定时面板 - 基于旧系统分析的合规实现
   */
  renderTimerPanel() {
    const container = $('.timer-content');
    if (!container) return;

    container.innerHTML = `
      <div class="timer-panel-content">
        <!-- 定时器总开关 -->
        <div class="timer-section">
          <div class="timer-header">
            <h3>定时器设置</h3>
            <div class="timer-master-switch">
              <label class="switch-group">
                <input type="checkbox" id="timerMasterSwitch">
                <span class="switch-slider"></span>
                <span class="switch-label">定时器总开关</span>
              </label>
            </div>
          </div>
        </div>

        <!-- 基础设置区域 -->
        <div class="timer-section" id="timerContent">
          <h4>基础设置</h4>
          <div class="timer-basic-settings">
            <div class="time-group">
              <label for="timerStartTime">开始时间:</label>
              <input type="time" id="timerStartTime" step="1" class="time-input">
            </div>
            <div class="time-group">
              <label for="timerEndTime">结束时间:</label>
              <input type="time" id="timerEndTime" step="1" class="time-input">
            </div>
            <div class="interval-group">
              <label for="timerInterval">执行间隔:</label>
              <select id="timerInterval" class="interval-select">
                <option value="0" selected>从不（仅执行一次）</option>
                <option value="15">15分钟</option>
                <option value="30">30分钟</option>
                <option value="60">1小时</option>
                <option value="120">2小时</option>
                <option value="180">3小时</option>
                <option value="360">6小时</option>
                <option value="720">12小时</option>
                <option value="custom">自定义间隔</option>
              </select>
            </div>
            <div class="interval-group custom-interval-group" id="customIntervalGroup" style="display: none;">
              <label for="customInterval">自定义间隔（分钟）:</label>
              <input type="number" id="customInterval" min="1" max="43200" value="60" class="interval-select">
            </div>
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" id="timerDaily" checked>
                <span>每日重复</span>
              </label>
            </div>
          </div>
        </div>

        <!-- 信号选择区域 -->
        <div class="timer-section">
          <h4>信号选择</h4>
          <div class="signal-selection">
            <div class="selected-signals-display" id="selectedSignalsDisplay">
              <p class="hint-text">未选择信号（将发射全部信号）</p>
            </div>
            <div class="signal-actions">
              <button class="timer-btn secondary-btn" id="selectSignalsBtn">选择信号</button>
              <button class="timer-btn secondary-btn" id="clearSignalsBtn" disabled>清除选择</button>
            </div>
          </div>
        </div>

        <!-- 任务操作区域 -->
        <div class="timer-section">
          <h4>任务操作</h4>
          <div class="timer-controls">
            <button class="timer-btn primary-btn" id="saveTaskBtn">保存定时任务</button>
            <button class="timer-btn primary-btn" id="updateTaskBtn" style="display: none;">更新定时任务</button>
            <button class="timer-btn secondary-btn" id="cancelEditBtn" style="display: none;">取消编辑</button>
            <button class="timer-btn secondary-btn" id="testTimerBtn">测试执行</button>
          </div>
        </div>

        <!-- 保存的任务区域 -->
        <div class="timer-section">
          <h4>保存的定时任务</h4>
          <div class="saved-tasks" id="savedTasks">
            <p class="hint-text">暂无保存的定时任务</p>
          </div>
        </div>
      </div>
    `;

    this.bindTimerEvents();
  }

  /**
   * 绑定定时器事件 - 合规实现
   */
  bindTimerEvents() {
    // 缓存DOM元素
    this.elements = {
      // 主开关
      masterSwitch: $('#timerMasterSwitch'),
      timerContent: $('#timerContent'),

      // 基础设置
      startTimeInput: $('#timerStartTime'),
      endTimeInput: $('#timerEndTime'),
      intervalSelect: $('#timerInterval'),
      customIntervalGroup: $('#customIntervalGroup'),
      customIntervalInput: $('#customInterval'),
      dailyCheckbox: $('#timerDaily'),

      // 信号选择
      selectedSignalsDisplay: $('#selectedSignalsDisplay'),
      selectSignalsBtn: $('#selectSignalsBtn'),
      clearSignalsBtn: $('#clearSignalsBtn'),

      // 任务操作
      saveTaskBtn: $('#saveTaskBtn'),
      updateTaskBtn: $('#updateTaskBtn'),
      cancelEditBtn: $('#cancelEditBtn'),
      testTimerBtn: $('#testTimerBtn'),

      // 任务列表
      savedTasks: $('#savedTasks')
    };

    // 绑定主开关事件
    if (this.elements.masterSwitch) {
      this.elements.masterSwitch.addEventListener('change', (e) => {
        this.toggleMasterSwitch(e.target.checked);
      });
    }

    // 绑定任务操作事件
    if (this.elements.saveTaskBtn) {
      this.elements.saveTaskBtn.addEventListener('click', () => this.saveTask());
    }
    if (this.elements.updateTaskBtn) {
      this.elements.updateTaskBtn.addEventListener('click', () => this.updateTask());
    }
    if (this.elements.cancelEditBtn) {
      this.elements.cancelEditBtn.addEventListener('click', () => this.cancelEdit());
    }
    if (this.elements.testTimerBtn) {
      this.elements.testTimerBtn.addEventListener('click', () => this.createTestTimerTask());
    }

    // 绑定信号选择事件
    if (this.elements.selectSignalsBtn) {
      this.elements.selectSignalsBtn.addEventListener('click', () => this.selectSignals());
    }
    if (this.elements.clearSignalsBtn) {
      this.elements.clearSignalsBtn.addEventListener('click', () => this.clearSignalSelection());
    }

    // 绑定间隔选择事件
    if (this.elements.intervalSelect) {
      this.elements.intervalSelect.addEventListener('change', (e) => this.handleIntervalChange(e.target.value));
    }

    // 绑定时间选择器事件
    if (this.elements.startTimeInput) {
      this.elements.startTimeInput.addEventListener('focus', () => {
        this.setCurrentTimeForPicker(this.elements.startTimeInput);
      });
    }
    if (this.elements.endTimeInput) {
      this.elements.endTimeInput.addEventListener('focus', () => {
        this.setCurrentTimeForPicker(this.elements.endTimeInput);
      });
    }



    // 设置初始状态
    this.setInitialState();

    console.log('TimerSettings: 事件绑定完成');
  }

  // ==================== 核心功能方法 - 基于旧系统分析 ====================

  /**
   * 切换主开关
   */
  toggleMasterSwitch(enabled) {
    try {
      // 更新主开关状态
      if (this.elements.masterSwitch) {
        this.elements.masterSwitch.checked = enabled;
      }

      // 更新所有控件的可用状态
      this.updateAllControlsState(enabled);

      // 更新状态
      this.timerState.isTimerEnabled = enabled;
      this.timerSettings.isEnabled = enabled;

      console.log('TimerSettings: 主开关状态:', enabled);

      // 发布事件
      this.emitEvent('timer.master-switch.changed', {
        enabled: enabled
      });

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '切换主开关';

    } catch (error) {
      this.performance.errorCount++;
      console.error('TimerSettings: 切换主开关失败:', error);
      this.handleError(error, '主开关控制');
    }
  }

  /**
   * 更新所有控件的状态
   */
  updateAllControlsState(enabled) {
    try {
      // 更新整个内容区域的视觉状态
      if (this.elements.timerContent) {
        if (enabled) {
          this.elements.timerContent.style.opacity = '1';
          this.elements.timerContent.style.pointerEvents = 'auto';
          this.elements.timerContent.classList.remove('disabled');
        } else {
          this.elements.timerContent.style.opacity = '0.5';
          this.elements.timerContent.style.pointerEvents = 'none';
          this.elements.timerContent.classList.add('disabled');
        }
      }

      // 禁用/启用所有输入控件
      const inputControls = [
        this.elements.startTimeInput,
        this.elements.endTimeInput,
        this.elements.intervalSelect,
        this.elements.dailyCheckbox
      ];

      inputControls.forEach(control => {
        if (control) {
          control.disabled = !enabled;
        }
      });

      // 禁用/启用所有按钮
      const buttonControls = [
        this.elements.selectSignalsBtn,
        this.elements.clearSignalsBtn,
        this.elements.saveTaskBtn,
        this.elements.updateTaskBtn,
        this.elements.cancelEditBtn,
        this.elements.testTimerBtn
      ];

      buttonControls.forEach(button => {
        if (button) {
          button.disabled = !enabled;
          if (enabled) {
            button.classList.remove('disabled');
          } else {
            button.classList.add('disabled');
          }
        }
      });

      // 更新任务列表中的按钮状态
      this.updateTaskListButtonsState(enabled);

      // 更新整个定时器面板的视觉状态
      this.updateTimerPanelVisualState(enabled);

    } catch (error) {
      console.error('TimerSettings: 更新控件状态失败:', error);
    }
  }

  /**
   * 更新任务列表中的按钮状态
   */
  updateTaskListButtonsState(enabled) {
    try {
      // 更新任务列表中的所有按钮
      const taskButtons = $$('#savedTasks .task-btn');
      taskButtons.forEach(button => {
        button.disabled = !enabled;
        if (enabled) {
          button.classList.remove('disabled');
        } else {
          button.classList.add('disabled');
        }
      });

      // 更新任务项的整体视觉状态
      const taskItems = $$('#savedTasks .task-item');
      taskItems.forEach(item => {
        if (enabled) {
          item.classList.remove('disabled');
          item.style.opacity = '1';
        } else {
          item.classList.add('disabled');
          item.style.opacity = '0.5';
        }
      });

    } catch (error) {
      console.error('TimerSettings: 更新任务按钮状态失败:', error);
    }
  }

  /**
   * 更新定时器面板的整体视觉状态
   */
  updateTimerPanelVisualState(enabled) {
    try {
      // 获取所有定时器区域
      const timerSections = $$('.timer-section');

      timerSections.forEach(section => {
        // 跳过包含主开关的区域
        if (section.querySelector('#timerMasterSwitch')) {
          return;
        }

        if (enabled) {
          section.classList.remove('disabled');
          section.style.opacity = '1';
          section.style.pointerEvents = 'auto';
        } else {
          section.classList.add('disabled');
          section.style.opacity = '0.5';
          section.style.pointerEvents = 'none';
        }
      });

      // 更新信号选择显示区域
      const signalDisplay = this.elements.selectedSignalsDisplay;
      if (signalDisplay) {
        if (enabled) {
          signalDisplay.style.opacity = '1';
        } else {
          signalDisplay.style.opacity = '0.5';
        }
      }

    } catch (error) {
      console.error('TimerSettings: 更新面板视觉状态失败:', error);
    }
  }

  /**
   * 保存定时任务
   */
  saveTask() {
    try {
      // 检查定时器是否启用
      if (!this.timerState.isTimerEnabled) {
        this.handleError(new Error('请先启用定时器总开关'), '任务保存');
        return;
      }

      // 获取表单数据
      const startTime = this.elements.startTimeInput?.value || '09:00:00';
      const endTime = this.elements.endTimeInput?.value || '18:00:00';
      const interval = this.getIntervalValue();
      const isDaily = this.elements.dailyCheckbox?.checked || false;

      console.log('TimerSettings: 保存新任务设置', {
        startTime, endTime, interval, isDaily
      });

      // 创建新任务对象
      const task = {
        id: this.nextTaskId++,
        name: `${isDaily ? '每日' : '单次'}定时任务`,
        startTime,
        endTime,
        interval,
        isDaily,
        selectedSignalIds: [...this.timerSettings.selectedSignalIds],
        createdAt: new Date().toISOString(),
        status: 'saved'
      };

      // 添加到任务列表
      this.timerTasks.push(task);

      // 更新UI显示
      this.updateSavedTasksDisplay();

      // 重置表单和状态
      this.resetAfterTaskOperation();

      console.log('TimerSettings: 新任务保存成功，ID:', task.id);

      // 发布事件
      this.emitEvent('timer.task.saved', {
        task: task
      });

      this.handleSuccess('定时任务保存成功', '任务管理');

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '保存定时任务';

    } catch (error) {
      this.performance.errorCount++;
      console.error('TimerSettings: 保存任务失败:', error);
      this.handleError(error, '任务保存');
    }
  }

  /**
   * 更新定时任务
   */
  updateTask() {
    try {
      if (!this.editingTaskId) {
        console.error('TimerSettings: 没有正在编辑的任务');
        return;
      }

      // 查找要更新的任务
      const taskIndex = this.timerTasks.findIndex(t => t.id === this.editingTaskId);
      if (taskIndex === -1) {
        console.error('TimerSettings: 任务不存在', this.editingTaskId);
        return;
      }

      // 获取表单数据
      const startTime = this.elements.startTimeInput?.value || '09:00:00';
      const endTime = this.elements.endTimeInput?.value || '18:00:00';
      const interval = this.getIntervalValue();
      const isDaily = this.elements.dailyCheckbox?.checked || false;

      console.log('TimerSettings: 更新任务设置:', {
        taskId: this.editingTaskId,
        startTime, endTime, interval, isDaily
      });

      // 更新任务数据
      const updatedTask = {
        ...this.timerTasks[taskIndex],
        name: `${isDaily ? '每日' : '单次'}定时任务`,
        startTime,
        endTime,
        interval,
        isDaily,
        selectedSignalIds: [...this.timerSettings.selectedSignalIds],
        updatedAt: new Date().toISOString()
      };

      // 替换任务
      this.timerTasks[taskIndex] = updatedTask;

      // 退出编辑模式
      this.exitEditMode();

      // 更新UI显示
      this.updateSavedTasksDisplay();

      // 重置状态
      this.resetAfterTaskOperation();

      console.log('TimerSettings: 任务更新成功，ID:', this.editingTaskId);

      // 发布事件
      this.emitEvent('timer.task.updated', {
        task: updatedTask
      });

      this.handleSuccess('定时任务更新成功', '任务管理');

    } catch (error) {
      console.error('TimerSettings: 更新任务失败:', error);
      this.handleError(error, '任务更新');
    }
  }

  /**
   * 取消编辑
   */
  cancelEdit() {
    try {
      console.log('TimerSettings: 取消编辑任务:', this.editingTaskId);

      // 退出编辑模式
      this.exitEditMode();

      // 重置表单
      this.resetForm();

      // 重置状态
      this.resetAfterTaskOperation();

      console.log('TimerSettings: 编辑已取消');

    } catch (error) {
      console.error('TimerSettings: 取消编辑失败:', error);
      this.handleError(error, '取消编辑');
    }
  }

  /**
   * 测试执行
   */
  testExecution() {
    try {
      // 检查定时器是否启用
      if (!this.timerState.isTimerEnabled) {
        this.handleError(new Error('请先启用定时器总开关'), '测试执行');
        return;
      }

      console.log('TimerSettings: 执行测试');

      // 更新执行状态
      const now = new Date();
      const nextTime = new Date(now.getTime() + 60 * 60 * 1000); // 1小时后

      if (this.elements.nextExecution) {
        this.elements.nextExecution.textContent = nextTime.toLocaleTimeString();
      }
      if (this.elements.lastExecution) {
        this.elements.lastExecution.textContent = now.toLocaleTimeString();
      }
      if (this.elements.executionCount) {
        const current = parseInt(this.elements.executionCount.textContent) || 0;
        this.elements.executionCount.textContent = (current + 1).toString();
      }

      // 更新状态
      this.timerState.lastExecutionTime = now.getTime();
      this.timerState.nextExecutionTime = nextTime.getTime();
      this.timerState.executionCount = (this.timerState.executionCount || 0) + 1;

      // 发布事件
      this.emitEvent('timer.test.executed', {
        executionTime: now.getTime(),
        nextTime: nextTime.getTime(),
        count: this.timerState.executionCount
      });

      this.handleSuccess('测试执行完成', '定时器测试');

    } catch (error) {
      console.error('TimerSettings: 测试执行失败:', error);
      this.handleError(error, '测试执行');
    }
  }

  /**
   * 测试定时器管理器
   */
  testTimerManager() {
    try {
      console.log('🧪 TimerSettings: 开始测试定时器管理器');

      if (!this.timerManager) {
        console.error('❌ 定时器管理器未初始化');
        this.handleError(new Error('定时器管理器未初始化'), '定时器测试');
        return;
      }

      // 测试简单定时器
      this.timerManager.addTimer(
        'test_timer_simple',
        () => {
          console.log('✅ 简单定时器测试成功！');
          this.handleSuccess('定时器管理器测试成功', '定时器测试');
        },
        3000,
        false
      );

      console.log('🕐 定时器已设置，3秒后触发');
      this.handleSuccess('定时器测试已启动，3秒后查看结果', '定时器测试');

    } catch (error) {
      console.error('❌ TimerSettings: 测试定时器管理器失败:', error);
      this.handleError(error, '定时器测试');
    }
  }

  /**
   * 创建并激活测试定时任务
   */
  createTestTimerTask() {
    try {
      console.log('🧪 TimerSettings: 创建测试定时任务');

      if (!this.timerState.isTimerEnabled) {
        this.handleError(new Error('请先启用定时器总开关'), '测试任务');
        return;
      }

      // 创建测试任务
      const testTask = {
        id: this.nextTaskId++,
        name: '测试定时任务',
        startTime: new Date().toTimeString().slice(0, 8), // 当前时间
        endTime: new Date(Date.now() + 3600000).toTimeString().slice(0, 8), // 1小时后
        interval: 0,
        isDaily: false,
        selectedSignalIds: [],
        createdAt: new Date().toISOString(),
        status: 'saved'
      };

      // 添加到任务列表
      this.timerTasks.push(testTask);
      console.log('📝 测试任务已创建:', testTask);

      // 立即激活任务
      this.activateTask(testTask.id);

      this.handleSuccess('测试定时任务已创建并激活', '测试任务');

    } catch (error) {
      console.error('❌ TimerSettings: 创建测试任务失败:', error);
      this.handleError(error, '测试任务');
    }
  }

  /**
   * 信号选择功能
   */
  async selectSignals() {
    try {
      // 检查定时器是否启用
      if (!this.timerState.isTimerEnabled) {
        this.handleError(new Error('请先启用定时器总开关'), '信号选择');
        return;
      }

      console.log('TimerSettings: 开始信号选择');

      // 设置信号选择模式
      this.timerState.isSignalSelectionMode = true;

      // 获取信号数据
      const signals = await this.requestSignalsFromManager();

      if (!signals || signals.length === 0) {
        this.handleError(new Error('没有可用的信号'), '信号选择');
        this.timerState.isSignalSelectionMode = false;
        return;
      }

      // 显示信号选择界面
      this.showSignalSelectionModal(signals);

    } catch (error) {
      console.error('TimerSettings: 开始信号选择失败:', error);
      this.timerState.isSignalSelectionMode = false;
      this.handleError(error, '信号选择');
    }
  }

  /**
   * 从信号管理模块请求信号数据
   */
  async requestSignalsFromManager() {
    return new Promise((resolve) => {
      // 使用统一定时器管理器设置超时
      this.timerManager.addTimer(
        'signal_request_timeout',
        () => {
          console.warn('TimerSettings: 获取信号数据超时');
          resolve([]);
        },
        2000,
        false
      );

      this.emitEvent('signal.request.all', {
        source: 'TimerSettings',
        purpose: 'timer-selection',
        callback: (signals) => {
          // 清除超时定时器
          this.timerManager.removeTimer('signal_request_timeout');
          console.log(`TimerSettings: 收到 ${signals?.length || 0} 个信号`);
          resolve(signals || []);
        }
      });
    });
  }

  /**
   * 显示信号选择模态框
   */
  showSignalSelectionModal(signals) {
    try {
      const modalContent = `
        <div class="timer-signal-selection-modal">
          <div class="modal-header">
            <h3>选择定时发射信号</h3>
            <p class="modal-subtitle">选择需要定时发射的信号，不选择任何信号将发射全部信号</p>
          </div>

          <div class="modal-body">
            <div class="signal-search-bar">
              <input type="text" id="signalSearchInput" placeholder="搜索信号名称..." class="search-input">
            </div>

            <div class="signal-list-container">
              <div class="signal-list" id="timerSignalList">
                ${this.renderSignalListItems(signals)}
              </div>
            </div>

            <div class="selection-summary">
              <span id="selectionCount">已选择 ${this.timerSettings.selectedSignalIds.length} 个信号</span>
            </div>
          </div>

          <div class="modal-footer">
            <div class="modal-actions">
              <div class="selection-actions">
                <button class="btn secondary" data-action="select-all">全选</button>
                <button class="btn secondary" data-action="clear-all">清空</button>
              </div>
              <div class="confirm-actions">
                <button class="btn secondary" data-action="cancel">取消</button>
                <button class="btn primary" data-action="confirm">确认选择</button>
              </div>
            </div>
          </div>
        </div>
      `;

      // 通过事件系统显示模态框
      console.log('TimerSettings: 准备发送模态框显示事件');
      console.log('TimerSettings: 模态框内容长度:', modalContent.length);

      // 通过事件系统显示模态框
      this.emitEvent('system.modal.show', {
        content: modalContent,
        title: '信号选择',
        className: 'timer-signal-modal'
      });

      console.log('TimerSettings: 模态框显示事件已发送');

      // 绑定模态框事件
      this.bindSignalModalEvents(signals);

    } catch (error) {
      console.error('TimerSettings: 显示信号选择模态框失败:', error);
      this.handleError(error, '信号选择界面');
    }
  }

  /**
   * 渲染信号列表项 - 背景色选中方案
   */
  renderSignalListItems(signals) {
    return signals.map(signal => {
      const isSelected = this.timerSettings.selectedSignalIds.includes(signal.id);
      return `
        <div class="timer-signal-item ${isSelected ? 'selected' : ''}" data-signal-id="${signal.id}">
          <div class="signal-info-row">
            <span class="signal-name" title="${signal.name || '未命名信号'}">${signal.name || '未命名信号'}</span>
            <span class="signal-code" title="${signal.signalCode || 'N/A'}">${signal.signalCode ? signal.signalCode.substring(0, 16) + '...' : 'N/A'}</span>
            <span class="signal-protocol">${signal.protocol || 'Unknown'}</span>
          </div>
        </div>
      `;
    }).join('');
  }

  /**
   * 获取信号类型名称
   */
  getSignalTypeName(type) {
    const typeNames = {
      'tv': '电视',
      'ac': '空调',
      'light': '灯光',
      'fan': '风扇',
      'audio': '音响',
      'other': '其他'
    };
    return typeNames[type] || '未知';
  }

  /**
   * 绑定信号选择模态框事件
   */
  bindSignalModalEvents(signals) {
    try {
      // 等待模态框渲染完成
      setTimeout(() => {
        // 搜索功能
        const searchInput = $('#signalSearchInput');
        if (searchInput) {
          searchInput.addEventListener('input', (e) => {
            this.filterSignalList(signals, e.target.value);
          });
        }

        // 模态框按钮事件委托
        const modal = $('.timer-signal-selection-modal');
        if (modal) {
          modal.addEventListener('click', (e) => {
            const button = e.target.closest('[data-action]');
            if (!button) return;

            const action = button.dataset.action;
            switch (action) {
              case 'select-all':
                this.selectAllSignals(signals);
                break;
              case 'clear-all':
                this.clearAllSignals();
                break;
              case 'cancel':
                this.cancelSignalSelection();
                break;
              case 'confirm':
                this.confirmSignalSelection();
                break;
            }
          });

          // 信号项点击事件
          modal.addEventListener('click', (e) => {
            const signalItem = e.target.closest('.timer-signal-item');
            if (signalItem) {
              this.toggleSignalSelection(signalItem);
            }
          });
        }
      }, 100);

    } catch (error) {
      console.error('TimerSettings: 绑定模态框事件失败:', error);
    }
  }

  /**
   * 过滤信号列表
   */
  filterSignalList(signals, searchTerm) {
    try {
      const signalList = $('#timerSignalList');
      if (!signalList) return;

      const filteredSignals = signals.filter(signal =>
        signal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (signal.type && signal.type.toLowerCase().includes(searchTerm.toLowerCase()))
      );

      signalList.innerHTML = this.renderSignalListItems(filteredSignals);
    } catch (error) {
      console.error('TimerSettings: 过滤信号列表失败:', error);
    }
  }

  /**
   * 切换信号选中状态
   */
  toggleSignalSelection(signalItem) {
    try {
      const signalId = signalItem.dataset.signalId;
      if (!signalId) return;

      const isSelected = signalItem.classList.contains('selected');

      if (isSelected) {
        // 取消选中
        signalItem.classList.remove('selected');
        const index = this.timerSettings.selectedSignalIds.indexOf(signalId);
        if (index > -1) {
          this.timerSettings.selectedSignalIds.splice(index, 1);
        }
      } else {
        // 选中
        signalItem.classList.add('selected');
        if (!this.timerSettings.selectedSignalIds.includes(signalId)) {
          this.timerSettings.selectedSignalIds.push(signalId);
        }
      }

      this.updateSignalSelection();
    } catch (error) {
      console.error('TimerSettings: 切换信号选中状态失败:', error);
    }
  }

  /**
   * 选择所有信号
   */
  selectAllSignals(signals) {
    try {
      const signalItems = $$('.timer-signal-item');
      signalItems.forEach(item => {
        item.classList.add('selected');
      });

      // 更新选中的信号ID列表
      this.timerSettings.selectedSignalIds = signals.map(signal => signal.id);
      this.updateSignalSelection();
    } catch (error) {
      console.error('TimerSettings: 全选信号失败:', error);
    }
  }

  /**
   * 清空所有选择
   */
  clearAllSignals() {
    try {
      const signalItems = $$('.timer-signal-item');
      signalItems.forEach(item => {
        item.classList.remove('selected');
      });

      // 清空选中的信号ID列表
      this.timerSettings.selectedSignalIds = [];
      this.updateSignalSelection();
    } catch (error) {
      console.error('TimerSettings: 清空选择失败:', error);
    }
  }

  /**
   * 更新信号选择状态
   */
  updateSignalSelection() {
    try {
      const selectedCount = this.timerSettings.selectedSignalIds.length;

      // 更新选择计数显示
      const countElement = $('#selectionCount');
      if (countElement) {
        countElement.textContent = `已选择 ${selectedCount} 个信号`;
      }
    } catch (error) {
      console.error('TimerSettings: 更新选择状态失败:', error);
    }
  }

  /**
   * 取消信号选择
   */
  cancelSignalSelection() {
    try {
      this.timerState.isSignalSelectionMode = false;

      // 关闭模态框
      this.emitEvent('system.modal.hide');

      console.log('TimerSettings: 信号选择已取消');
    } catch (error) {
      console.error('TimerSettings: 取消信号选择失败:', error);
    }
  }

  /**
   * 确认信号选择
   */
  confirmSignalSelection() {
    try {
      // 选中的信号ID已经在toggleSignalSelection中维护了
      const selectedIds = this.timerSettings.selectedSignalIds;

      // 退出选择模式
      this.timerState.isSignalSelectionMode = false;

      // 更新显示
      this.updateSelectedSignalsDisplay();

      // 关闭模态框
      this.emitEvent('system.modal.hide');

      // 发布事件
      this.emitEvent('timer.signal-selection.completed', {
        source: 'TimerSettings',
        selectedSignalIds: selectedIds
      });

      console.log(`TimerSettings: 信号选择完成，选中 ${selectedIds.length} 个信号`);

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '信号选择完成';

    } catch (error) {
      this.performance.errorCount++;
      console.error('TimerSettings: 确认信号选择失败:', error);
      this.handleError(error, '确认选择');
    }
  }

  clearSignalSelection() {
    try {
      // 检查定时器是否启用
      if (!this.timerState.isTimerEnabled) {
        this.handleError(new Error('请先启用定时器总开关'), '清除信号选择');
        return;
      }

      console.log('TimerSettings: 清除信号选择');

      // 清空选中的信号
      this.timerSettings.selectedSignalIds = [];

      // 更新显示
      this.updateSelectedSignalsDisplay();

      // 发布事件
      this.emitEvent('timer.signal-selection.cleared', {
        source: 'TimerSettings'
      });

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '清除信号选择';

    } catch (error) {
      this.performance.errorCount++;
      console.error('TimerSettings: 清除信号选择失败:', error);
      this.handleError(error, '清除选择');
    }
  }

  /**
   * 处理信号选择完成事件
   */
  handleSignalSelectionCompleted(data) {
    try {
      if (data.source !== 'TimerSettings') return;

      console.log('TimerSettings: 信号选择完成', data.selectedSignals);

      // 更新选中的信号
      this.timerSettings.selectedSignalIds = data.selectedSignals.map(s => s.id);

      // 退出信号选择模式
      this.timerState.isSignalSelectionMode = false;

      // 更新显示
      this.updateSelectedSignalsDisplay();

    } catch (error) {
      console.error('TimerSettings: 处理信号选择完成失败:', error);
    }
  }

  handleSignalSelectionCancelled(data) {
    try {
      if (data.source !== 'TimerSettings') return;

      console.log('TimerSettings: 信号选择已取消');

      // 退出信号选择模式
      this.timerState.isSignalSelectionMode = false;

    } catch (error) {
      console.error('TimerSettings: 处理信号选择取消失败:', error);
    }
  }

  handleSignalSelectionChanged(data) {
    try {
      if (data.source !== 'TimerSettings') return;

      console.log('TimerSettings: 信号选择变化', data.selectedSignals);

      // 实时更新选中的信号
      this.timerSettings.selectedSignalIds = data.selectedSignals.map(s => s.id);

      // 更新显示
      this.updateSelectedSignalsDisplay();

    } catch (error) {
      console.error('TimerSettings: 处理信号选择变化失败:', error);
    }
  }

  /**
   * 任务管理方法
   */
  editTask(taskId) {
    try {
      // 检查定时器是否启用
      if (!this.timerState.isTimerEnabled) {
        this.handleError(new Error('请先启用定时器总开关'), '编辑任务');
        return;
      }

      const task = this.timerTasks.find(t => t.id === taskId);
      if (!task) {
        console.error('TimerSettings: 任务不存在', taskId);
        return;
      }

      console.log('TimerSettings: 编辑任务', taskId);

      // 进入编辑模式
      this.enterEditMode(taskId);

      // 填充表单数据
      this.fillFormWithTask(task);

      // 更新信号选择
      this.timerSettings.selectedSignalIds = [...task.selectedSignalIds];
      this.updateSelectedSignalsDisplay();

    } catch (error) {
      console.error('TimerSettings: 编辑任务失败:', error);
      this.handleError(error, '编辑任务');
    }
  }

  deleteTask(taskId) {
    try {
      // 检查定时器是否启用
      if (!this.timerState.isTimerEnabled) {
        this.handleError(new Error('请先启用定时器总开关'), '删除任务');
        return;
      }

      const taskIndex = this.timerTasks.findIndex(t => t.id === taskId);
      if (taskIndex === -1) {
        console.error('TimerSettings: 任务不存在', taskId);
        return;
      }

      console.log('TimerSettings: 删除任务', taskId);

      // 检查任务是否处于激活状态
      const wasActive = this.activeTasks.some(activeTask => activeTask.id === taskId);

      // 清理定时器（如果存在）
      const timerKey = `timer_task_${taskId}`;
      if (this.timerManager.hasTimer(timerKey)) {
        this.timerManager.removeTimer(timerKey);
        console.log('TimerSettings: 已清理任务定时器:', taskId);
      }

      // 从激活任务队列中移除（如果存在）
      if (wasActive) {
        this.activeTasks = this.activeTasks.filter(activeTask => activeTask.id !== taskId);
        console.log('TimerSettings: 同时从激活队列中移除任务', taskId);
      }

      // 删除任务
      this.timerTasks.splice(taskIndex, 1);

      // 如果正在编辑这个任务，退出编辑模式
      if (this.editingTaskId === taskId) {
        this.exitEditMode();
        this.resetForm();
        this.resetAfterTaskOperation();
      }

      // 更新UI显示
      this.updateSavedTasksDisplay();

      // 发布事件
      this.emitEvent('timer.task.deleted', {
        taskId: taskId,
        wasActive: wasActive,
        activeTasksCount: this.activeTasks.length
      });

      const statusText = wasActive ?
        `定时任务删除成功（剩余${this.activeTasks.length}个激活任务）` :
        '定时任务删除成功';

      this.handleSuccess(statusText, '任务管理');

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '删除定时任务';

    } catch (error) {
      this.performance.errorCount++;
      console.error('TimerSettings: 删除任务失败:', error);
      this.handleError(error, '删除任务');
    }
  }

  activateTask(taskId) {
    try {
      // 检查定时器是否启用
      if (!this.timerState.isTimerEnabled) {
        this.handleError(new Error('请先启用定时器总开关'), '激活任务');
        return;
      }

      const task = this.timerTasks.find(t => t.id === taskId);
      if (!task) {
        console.error('TimerSettings: 任务不存在', taskId);
        return;
      }

      // 检查任务是否已经激活
      const isAlreadyActive = this.activeTasks.some(activeTask => activeTask.id === taskId);
      if (isAlreadyActive) {
        this.handleError(new Error('任务已经处于激活状态'), '激活任务');
        return;
      }

      console.log('🚀 TimerSettings: 开始激活任务', taskId, task.name);

      // 计算下次执行时间
      const nextTime = this.calculateNextExecutionTime(task);

      // 创建激活任务对象
      const activeTask = {
        ...task,
        nextExecutionTime: nextTime,
        activatedAt: Date.now()
      };

      // 添加到激活任务队列
      this.activeTasks.push(activeTask);
      console.log('📝 TimerSettings: 任务已添加到激活队列，当前激活任务数:', this.activeTasks.length);

      // 按时间排序激活任务队列
      this.sortActiveTasksByTime();

      // 发布队列更新事件 - 让监控和状态模块知道
      this.emitEvent('timer.task.queue.updated', {
        activeTasksCount: this.activeTasks.length,
        nextTask: this.activeTasks[0]
      });

      // 设置原生定时器 - 符合R1架构标准
      console.log('⏰ TimerSettings: 开始调度任务执行...');
      this.scheduleTaskExecution(activeTask);

      // 更新UI显示
      this.updateSavedTasksDisplay();

      // 发布事件
      this.emitEvent('timer.task.activated', {
        task: activeTask,
        nextExecutionTime: nextTime,
        activeTasksCount: this.activeTasks.length
      });

      this.handleSuccess(`定时任务已激活（共${this.activeTasks.length}个激活任务）`, '任务管理');

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '激活定时任务';

    } catch (error) {
      this.performance.errorCount++;
      console.error('TimerSettings: 激活任务失败:', error);
      this.handleError(error, '激活任务');
    }
  }

  /**
   * 停用任务
   */
  deactivateTask(taskId) {
    try {
      console.log('TimerSettings: 停用任务', taskId);

      // 清理定时器
      const timerKey = `timer_task_${taskId}`;
      if (this.timerManager.hasTimer(timerKey)) {
        this.timerManager.removeTimer(timerKey);
        console.log('TimerSettings: 已清理任务定时器:', taskId);
      }

      // 从激活任务队列中移除
      const initialCount = this.activeTasks.length;
      this.activeTasks = this.activeTasks.filter(activeTask => activeTask.id !== taskId);

      if (this.activeTasks.length === initialCount) {
        this.handleError(new Error('任务未处于激活状态'), '停用任务');
        return;
      }

      // 更新UI显示
      this.updateSavedTasksDisplay();

      // 发布队列更新事件 - 让监控和状态模块知道
      this.emitEvent('timer.task.queue.updated', {
        activeTasksCount: this.activeTasks.length,
        nextTask: this.activeTasks[0] || null
      });

      // 发布事件
      this.emitEvent('timer.task.deactivated', {
        taskId: taskId,
        activeTasksCount: this.activeTasks.length
      });

      this.handleSuccess(`定时任务已停用（剩余${this.activeTasks.length}个激活任务）`, '任务管理');

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '停用定时任务';

    } catch (error) {
      this.performance.errorCount++;
      console.error('TimerSettings: 停用任务失败:', error);
      this.handleError(error, '停用任务');
    }
  }

  /**
   * 按时间排序激活任务队列
   */
  sortActiveTasksByTime() {
    try {
      this.activeTasks.sort((a, b) => a.nextExecutionTime - b.nextExecutionTime);
      console.log('TimerSettings: 激活任务队列已按时间排序',
        this.activeTasks.map(task => ({
          id: task.id,
          name: task.name,
          nextTime: new Date(task.nextExecutionTime).toLocaleString()
        }))
      );
    } catch (error) {
      console.error('TimerSettings: 排序激活任务失败:', error);
    }
  }

  /**
   * 调度任务执行 - 符合R1架构标准的事件驱动方式
   */
  scheduleTaskExecution(task) {
    try {
      const now = Date.now();
      const delay = task.nextExecutionTime - now;

      console.log(`🕐 TimerSettings: 调度任务执行 - ${task.name}, 延迟: ${delay}ms`);

      // 检查定时器管理器是否可用
      if (!this.timerManager) {
        console.error('❌ TimerSettings: 定时器管理器未初始化');
        return;
      }

      if (delay <= 0) {
        // 立即执行
        console.log('⚡ TimerSettings: 任务立即执行:', task.name);
        this.triggerTaskExecution(task);
        return;
      }

      // 使用统一定时器管理器
      this.timerManager.addTimer(
        `timer_task_${task.id}`,
        () => {
          console.log('⏰ TimerSettings: 定时器触发，任务到期:', task.name);
          this.triggerTaskExecution(task);
        },
        delay,
        false // 不重复
      );

      console.log(`✅ TimerSettings: 任务 ${task.name} 已调度，${Math.round(delay/1000)}秒后执行`);

    } catch (error) {
      console.error('❌ TimerSettings: 调度任务执行失败:', error);
    }
  }

  /**
   * 触发任务执行 - 事件驱动通知
   */
  triggerTaskExecution(task) {
    try {
      console.log('TimerSettings: 触发任务执行:', task.name);

      // 清理定时器引用
      const timerKey = `timer_task_${task.id}`;
      if (this.timerManager.hasTimer(timerKey)) {
        this.timerManager.removeTimer(timerKey);
      }

      // 发送任务执行请求到控制模块 - 符合R1架构标准
      this.emitEvent('timer.task.execution.request', {
        task: task,
        priority: 'medium', // 定时任务为中等优先级
        requestTime: Date.now(),
        source: 'TimerSettings'
      });

      console.log('TimerSettings: 已向控制模块发送执行请求');

    } catch (error) {
      console.error('TimerSettings: 触发任务执行失败:', error);
    }
  }

  // ==================== 事件驱动的任务执行处理 ====================

  /**
   * 处理任务到期事件 - 事件驱动架构
   */
  onTaskDue(taskData) {
    try {
      const { task } = taskData;
      console.log('TimerSettings: 收到任务到期通知:', task.id, task.name);

      // 通知控制模块执行任务
      this.emitEvent('timer.task.execution.request', {
        task: task,
        requestTime: Date.now()
      });

      console.log('TimerSettings: 已向控制模块发送执行请求');

    } catch (error) {
      console.error('TimerSettings: 处理任务到期失败:', error);
    }
  }

  /**
   * 处理定时任务信息请求 - 符合架构标准的事件通信
   */
  handleTaskInfoRequest(data) {
    try {
      const { taskId, callback } = data;

      // 查找任务
      const task = this.timerTasks.find(t => t.id === taskId);

      // 通过回调返回任务信息
      if (callback && typeof callback === 'function') {
        callback(task || null);
      }

    } catch (error) {
      console.error('TimerSettings: 处理任务信息请求失败:', error);
      if (data.callback && typeof data.callback === 'function') {
        data.callback(null);
      }
    }
  }

  /**
   * 处理任务开始执行事件
   */
  onTaskExecutionStarted(executionData) {
    try {
      const { taskId, taskName, startTime } = executionData;
      console.log('TimerSettings: 任务开始执行:', taskId, taskName);

      // 更新执行状态
      this.timerState.lastExecutionTime = startTime;
      this.timerState.executionCount++;

      // 状态已更新，无需额外UI更新

      // 发布状态更新事件
      this.emitEvent('timer.execution.status.updated', {
        taskId: taskId,
        status: 'executing',
        startTime: startTime
      });

    } catch (error) {
      console.error('TimerSettings: 处理任务开始执行失败:', error);
    }
  }

  /**
   * 处理任务执行完成事件 - 核心逻辑
   */
  onTaskExecutionCompleted(executionData) {
    try {
      const { taskId, executionTime, success } = executionData;
      console.log('TimerSettings: 任务执行完成:', taskId, success ? '成功' : '失败');

      // 查找并更新激活任务的下次执行时间
      const activeTask = this.activeTasks.find(task => task.id === taskId);
      if (activeTask) {
        // 检查任务是否需要重复执行
        if (activeTask.interval > 0 || activeTask.isDaily) {
          // 重新计算该任务的下次执行时间
          activeTask.nextExecutionTime = this.calculateNextExecutionTime(activeTask);

          // 重新排序激活任务队列
          this.sortActiveTasksByTime();

          // 重新调度任务 - 符合R1架构标准
          this.scheduleTaskExecution(activeTask);

          console.log('TimerSettings: 任务已重新调度，下次执行时间:',
            new Date(activeTask.nextExecutionTime).toLocaleString());
        } else {
          // 一次性任务，执行完成后移除
          this.deactivateTask(taskId);
          console.log('TimerSettings: 一次性任务执行完成，已自动停用');
        }
      }

      // 执行状态已在内存中更新

      // 发布队列更新事件
      this.emitEvent('timer.task.queue.updated', {
        activeTasksCount: this.activeTasks.length,
        nextTask: this.activeTasks.length > 0 ? this.activeTasks[0] : null,
        completedTaskId: taskId
      });

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '任务执行完成处理';

    } catch (error) {
      this.performance.errorCount++;
      console.error('TimerSettings: 处理任务执行完成失败:', error);
    }
  }

  /**
   * 处理任务执行失败事件
   */
  onTaskExecutionFailed(executionData) {
    try {
      const { taskId, error, executionTime } = executionData;
      console.error('TimerSettings: 任务执行失败:', taskId, error);

      // 记录错误
      this.performance.errorCount++;

      // 发布错误事件
      this.emitEvent('timer.task.execution.error', {
        taskId: taskId,
        error: error,
        executionTime: executionTime
      });

      // 显示错误通知
      this.handleError(new Error(`任务执行失败: ${error}`), '任务执行');

    } catch (error) {
      console.error('TimerSettings: 处理任务执行失败事件失败:', error);
    }
  }

  /**
   * UI更新方法
   */
  updateSelectedSignalsDisplay() {
    try {
      if (!this.elements.selectedSignalsDisplay) return;

      if (this.timerSettings.selectedSignalIds.length === 0) {
        this.elements.selectedSignalsDisplay.innerHTML = `
          <p class="hint-text">未选择信号（将发射全部信号）</p>
        `;
        // 清除按钮状态取决于主开关状态
        this.elements.clearSignalsBtn.disabled = true;
      } else {
        // 获取信号详细信息来显示
        this.requestSignalsFromManager().then(signals => {
          const selectedSignals = signals.filter(signal =>
            this.timerSettings.selectedSignalIds.includes(signal.id)
          );

          this.elements.selectedSignalsDisplay.innerHTML = `
            <div class="selected-signals-list">
              <p class="selected-count">已选择 ${this.timerSettings.selectedSignalIds.length} 个信号</p>
              <div class="signal-tags">
                ${selectedSignals.map(signal =>
                  `<span class="signal-tag" title="${signal.name}">
                    <span class="tag-name">${signal.name || '未命名信号'}</span>
                    <span class="tag-type">${this.getSignalTypeName(signal.type)}</span>
                  </span>`
                ).join('')}
              </div>
            </div>
          `;
        }).catch(error => {
          // 如果获取信号失败，显示简化版本
          this.elements.selectedSignalsDisplay.innerHTML = `
            <div class="selected-signals-list">
              <p class="selected-count">已选择 ${this.timerSettings.selectedSignalIds.length} 个信号</p>
              <div class="signal-tags">
                ${this.timerSettings.selectedSignalIds.map(id =>
                  `<span class="signal-tag">信号 ${id}</span>`
                ).join('')}
              </div>
            </div>
          `;
        });

        // 清除按钮状态取决于主开关状态
        this.elements.clearSignalsBtn.disabled = !this.timerState.isTimerEnabled;
      }

      // 确保按钮状态与主开关一致
      if (!this.timerState.isTimerEnabled) {
        this.elements.clearSignalsBtn.disabled = true;
        this.elements.clearSignalsBtn.classList.add('disabled');
      } else if (this.timerSettings.selectedSignalIds.length > 0) {
        this.elements.clearSignalsBtn.classList.remove('disabled');
      }

    } catch (error) {
      console.error('TimerSettings: 更新信号显示失败:', error);
    }
  }

  updateSavedTasksDisplay() {
    try {
      if (!this.elements.savedTasks) return;

      if (this.timerTasks.length === 0) {
        this.elements.savedTasks.innerHTML = `
          <p class="hint-text">暂无保存的定时任务</p>
        `;
        return;
      }

      this.elements.savedTasks.innerHTML = `
        <div class="tasks-list">
          ${this.timerTasks.map(task => {
            const isActive = this.activeTasks.some(activeTask => activeTask.id === task.id);
            const intervalText = this.getIntervalDisplayText(task.interval);

            return `
              <div class="task-item ${isActive ? 'active' : ''}" data-task-id="${task.id}">
                <div class="task-info">
                  <div class="task-header">
                    <div class="task-name">${task.name}</div>
                    ${isActive ? '<span class="task-status active">已激活</span>' : '<span class="task-status saved">已保存</span>'}
                  </div>
                  <div class="task-details">
                    <span class="task-time">${task.startTime} - ${task.endTime}</span>
                    <span class="task-interval">${intervalText}</span>
                    <span class="task-repeat">${task.isDaily ? '每日重复' : '单次执行'}</span>
                    <span class="task-signals">${task.selectedSignalIds.length > 0 ?
                      `${task.selectedSignalIds.length}个信号` : '全部信号'}</span>
                  </div>
                </div>
                <div class="task-actions">
                  <button class="task-btn edit-btn" data-action="edit" data-task-id="${task.id}">编辑</button>
                  <button class="task-btn ${isActive ? 'deactivate-btn' : 'activate-btn'}"
                          data-action="${isActive ? 'deactivate' : 'activate'}"
                          data-task-id="${task.id}">
                    ${isActive ? '停用' : '激活'}
                  </button>
                  <button class="task-btn delete-btn" data-action="delete" data-task-id="${task.id}">删除</button>
                </div>
              </div>
            `;
          }).join('')}
        </div>
      `;

      // 更新任务列表按钮状态以反映当前主开关状态
      this.updateTaskListButtonsState(this.timerState.isTimerEnabled);

      // 检查并更新间隔设置状态
      this.updateIntervalSettingState();

      // 绑定任务操作事件委托
      this.bindTaskListEvents();

    } catch (error) {
      console.error('TimerSettings: 更新任务列表失败:', error);
    }
  }

  /**
   * 绑定任务列表事件委托
   */
  bindTaskListEvents() {
    try {
      // 移除之前的事件监听器（如果存在）
      if (this.elements.savedTasks) {
        this.elements.savedTasks.removeEventListener('click', this.taskListClickHandler);

        // 创建事件处理器
        this.taskListClickHandler = (e) => {
          const button = e.target.closest('[data-action]');
          if (!button) return;

          const action = button.dataset.action;
          const taskId = parseInt(button.dataset.taskId);

          if (!taskId) return;

          switch (action) {
            case 'edit':
              this.editTask(taskId);
              break;
            case 'activate':
              this.activateTask(taskId);
              break;
            case 'deactivate':
              this.deactivateTask(taskId);
              break;
            case 'delete':
              this.deleteTask(taskId);
              break;
          }
        };

        // 绑定任务列表点击事件
        this.elements.savedTasks.addEventListener('click', this.taskListClickHandler);
      }
    } catch (error) {
      console.error('TimerSettings: 绑定任务列表事件失败:', error);
    }
  }



  /**
   * 辅助方法
   */
  setInitialState() {
    try {
      // 设置默认时间值（固定9-18点）
      this.setDefaultTimeValues();

      // 绑定时间选择器点击事件
      this.bindTimePickerEvents();

      // 设置初始UI状态
      this.toggleMasterSwitch(false);
      this.updateSelectedSignalsDisplay();
      this.updateSavedTasksDisplay();
      this.updateIntervalSettingState();

    } catch (error) {
      console.error('TimerSettings: 设置初始状态失败:', error);
    }
  }

  /**
   * 设置默认时间值（固定9-18点）
   */
  setDefaultTimeValues() {
    try {
      if (this.elements.startTimeInput && !this.elements.startTimeInput.value) {
        this.elements.startTimeInput.value = '09:00:00';
      }
      if (this.elements.endTimeInput && !this.elements.endTimeInput.value) {
        this.elements.endTimeInput.value = '18:00:00';
      }

      console.log('TimerSettings: 默认时间值设置完成（9-18点）');
    } catch (error) {
      console.error('TimerSettings: 设置默认时间值失败:', error);
    }
  }

  /**
   * 绑定时间选择器点击事件
   */
  bindTimePickerEvents() {
    try {
      // 时间选择器事件已在上面的统一事件注册中处理

      console.log('TimerSettings: 时间选择器事件绑定完成');
    } catch (error) {
      console.error('TimerSettings: 绑定时间选择器事件失败:', error);
    }
  }

  /**
   * 为时间选择器设置当前时间
   */
  setCurrentTimeForPicker(timeInput) {
    try {
      // 只有在输入框为空或为默认值时才设置当前时间
      if (!timeInput.value || timeInput.value === '09:00:00' || timeInput.value === '18:00:00') {
        const now = new Date();
        const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS格式
        timeInput.value = currentTime;
        console.log('TimerSettings: 时间选择器设置为当前时间:', currentTime);
      }
    } catch (error) {
      console.error('TimerSettings: 设置时间选择器当前时间失败:', error);
    }
  }

  /**
   * 处理间隔选择变化
   */
  handleIntervalChange(value) {
    try {
      if (value === 'custom') {
        // 显示自定义间隔输入
        if (this.elements.customIntervalGroup) {
          this.elements.customIntervalGroup.style.display = 'block';
        }
      } else {
        // 隐藏自定义间隔输入
        if (this.elements.customIntervalGroup) {
          this.elements.customIntervalGroup.style.display = 'none';
        }
      }
      console.log('TimerSettings: 间隔选择变化:', value);
    } catch (error) {
      console.error('TimerSettings: 处理间隔变化失败:', error);
    }
  }

  /**
   * 获取间隔值
   */
  getIntervalValue() {
    try {
      const selectedValue = this.elements.intervalSelect?.value || '0';

      if (selectedValue === 'custom') {
        // 使用自定义间隔值
        const customValue = parseInt(this.elements.customIntervalInput?.value || '60');
        return Math.max(0, Math.min(43200, customValue)); // 限制在0-43200分钟之间
      } else {
        // 使用预设间隔值
        return parseInt(selectedValue);
      }
    } catch (error) {
      console.error('TimerSettings: 获取间隔值失败:', error);
      return 0; // 默认返回0（从不重复）
    }
  }

  /**
   * 获取间隔显示文本
   */
  getIntervalDisplayText(intervalMinutes) {
    if (intervalMinutes === 0) {
      return '从不';
    } else if (intervalMinutes < 60) {
      return `每${intervalMinutes}分钟`;
    } else if (intervalMinutes === 60) {
      return '每1小时';
    } else if (intervalMinutes % 60 === 0) {
      return `每${intervalMinutes / 60}小时`;
    } else {
      const hours = Math.floor(intervalMinutes / 60);
      const minutes = intervalMinutes % 60;
      return `每${hours}小时${minutes}分钟`;
    }
  }

  /**
   * 更新间隔设置状态 - 多任务模式禁用间隔
   */
  updateIntervalSettingState() {
    try {
      const hasMultipleTasks = this.timerTasks.length > 1;

      // 多任务模式下禁用间隔设置 - 避免执行时间冲突和混乱
      if (this.elements.intervalSelect && this.elements.customIntervalGroup) {
        if (hasMultipleTasks) {
          // 多任务模式：禁用间隔设置，强制为"从不"
          this.elements.intervalSelect.value = '0';
          this.elements.intervalSelect.disabled = true;
          this.elements.customIntervalGroup.style.display = 'none';

          // 添加说明提示
          this.showMultiTaskIntervalHint();
        } else {
          // 单任务模式：启用间隔设置
          this.elements.intervalSelect.disabled = !this.timerState.isTimerEnabled;
          this.hideMultiTaskIntervalHint();
        }
      }

      console.log('TimerSettings: 间隔设置状态已更新', {
        hasMultipleTasks,
        intervalDisabled: hasMultipleTasks,
        reason: hasMultipleTasks ? '多任务模式避免时间冲突' : '单任务模式允许间隔'
      });

    } catch (error) {
      console.error('TimerSettings: 更新间隔设置状态失败:', error);
    }
  }

  /**
   * 显示多任务间隔禁用提示
   */
  showMultiTaskIntervalHint() {
    try {
      // 移除已存在的提示
      this.hideMultiTaskIntervalHint();

      const intervalGroup = this.elements.intervalSelect?.parentElement;
      if (intervalGroup) {
        const hint = R1Utils.dom.create('div');
        hint.className = 'multi-task-interval-hint';
        hint.innerHTML = `
          <small style="color: var(--info-color); font-size: 0.75rem; line-height: 1.4;">
            💡 多任务模式下间隔设置已禁用<br>
            原因：多个任务的间隔执行会造成时间冲突和执行混乱<br>
            建议：每个任务设置独立的执行时间段，避免重叠
          </small>
        `;
        intervalGroup.appendChild(hint);
      }
    } catch (error) {
      console.error('TimerSettings: 显示多任务间隔提示失败:', error);
    }
  }

  /**
   * 隐藏多任务间隔提示
   */
  hideMultiTaskIntervalHint() {
    try {
      const hint = $('.multi-task-interval-hint');
      if (hint) {
        hint.remove();
      }
      // 同时清理旧的提示
      const oldHint = $('.interval-disabled-hint');
      if (oldHint) {
        oldHint.remove();
      }
    } catch (error) {
      console.error('TimerSettings: 隐藏多任务间隔提示失败:', error);
    }
  }

  enterEditMode(taskId) {
    this.editingTaskId = taskId;
    this.elements.saveTaskBtn.style.display = 'none';
    this.elements.updateTaskBtn.style.display = 'inline-block';
    this.elements.cancelEditBtn.style.display = 'inline-block';
  }

  exitEditMode() {
    this.editingTaskId = null;
    this.elements.saveTaskBtn.style.display = 'inline-block';
    this.elements.updateTaskBtn.style.display = 'none';
    this.elements.cancelEditBtn.style.display = 'none';
  }

  fillFormWithTask(task) {
    if (this.elements.startTimeInput) this.elements.startTimeInput.value = task.startTime;
    if (this.elements.endTimeInput) this.elements.endTimeInput.value = task.endTime;
    if (this.elements.intervalSelect) this.elements.intervalSelect.value = task.interval.toString();
    if (this.elements.dailyCheckbox) this.elements.dailyCheckbox.checked = task.isDaily;
  }

  resetForm() {
    if (this.elements.startTimeInput) this.elements.startTimeInput.value = '09:00:00';
    if (this.elements.endTimeInput) this.elements.endTimeInput.value = '18:00:00';
    if (this.elements.intervalSelect) this.elements.intervalSelect.value = '0'; // 默认"从不"
    if (this.elements.dailyCheckbox) this.elements.dailyCheckbox.checked = true;

    // 隐藏自定义间隔输入
    if (this.elements.customIntervalGroup) {
      this.elements.customIntervalGroup.style.display = 'none';
    }

    // 更新间隔设置状态
    this.updateIntervalSettingState();
  }

  resetAfterTaskOperation() {
    this.timerSettings.selectedSignalIds = [];
    this.updateSelectedSignalsDisplay();
  }

  calculateNextExecutionTime(task) {
    const now = new Date();
    const timeParts = task.startTime.split(':').map(Number);
    const startHour = timeParts[0] || 0;
    const startMinute = timeParts[1] || 0;
    const startSecond = timeParts[2] || 0;

    let nextTime = new Date();
    nextTime.setHours(startHour, startMinute, startSecond, 0);

    // 检查是否是测试任务（名称包含"测试"）
    const isTestTask = task.name && task.name.includes('测试');

    // 如果时间已过
    if (nextTime <= now) {
      if (isTestTask) {
        // 测试任务：设置为30秒后执行，便于测试
        nextTime = new Date(now.getTime() + 30000);
        console.log('🧪 测试任务时间已过，设置为30秒后执行:', nextTime.toLocaleString());
      } else {
        // 正常任务：设置为明天
        nextTime.setDate(nextTime.getDate() + 1);
        console.log('⏭️ 时间已过，设置为明天:', nextTime.toLocaleString());
      }
    }

    console.log(`📅 任务 ${task.name} 下次执行时间: ${nextTime.toLocaleString()}, 延迟: ${nextTime.getTime() - now.getTime()}ms`);
    return nextTime.getTime();
  }



  /**
   * 停止所有定时任务
   */
  stopAllTasks() {
    try {
      // 清理所有定时器
      const timerIds = this.timerManager.getAllTimerIds();
      const taskTimers = timerIds.filter(id => id.startsWith('timer_task_'));

      for (const timerId of taskTimers) {
        this.timerManager.removeTimer(timerId);
        console.log('TimerSettings: 已清理定时器:', timerId);
      }

      // 清空激活任务
      this.activeTasks = [];

      console.log('TimerSettings: 所有定时任务已停止');

    } catch (error) {
      console.error('TimerSettings: 停止定时任务失败:', error);
    }
  }

  /**
   * 销毁模块 - 清理所有资源
   */
  destroy() {
    try {
      // 停止所有定时任务
      this.stopAllTasks();

      // 清理事件监听器
      this.eventBus.off('timer.task.due');
      this.eventBus.off('timer.task.info.request');
      this.eventBus.off('timer.create.submit');
      this.eventBus.off('timer.edit.submit');

      // 清理数据
      this.timerTasks = [];
      this.activeTasks = [];

      console.log('TimerSettings: 模块已销毁');
    } catch (error) {
      console.error('TimerSettings: 销毁模块失败:', error);
    }
  }
}

window.TimerSettings = TimerSettings;
