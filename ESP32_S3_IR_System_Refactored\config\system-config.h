#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

// ==================== 系统版本信息 ====================
#define SYSTEM_VERSION "2.0.0"
#define SYSTEM_NAME "ESP32-S3 IR Control System"
#define API_VERSION "1.0"

// ==================== 调试配置 ====================
#define DEBUG_MODE 1
#define SERIAL_BAUD_RATE 115200

// ==================== 系统模式定义 ====================
enum class SystemMode {
    HIGH_PERFORMANCE,  // PSRAM可用 - 高性能模式
    STANDARD          // 仅RAM - 标准模式
};

// ==================== 系统容量配置 ====================
struct SystemCapacity {
    int maxSignals;      // 最大信号数量
    int maxTasks;        // 最大任务数量
    int maxTimers;       // 最大定时器数量
    size_t bufferSize;   // 缓冲区大小
    int maxConnections;  // 最大WebSocket连接数
    
    // 根据系统模式获取容量配置
    static SystemCapacity getCapacity(SystemMode mode) {
        if (mode == SystemMode::HIGH_PERFORMANCE) {
            // 高性能模式 - PSRAM可用
            return {
                .maxSignals = 1000,
                .maxTasks = 100,
                .maxTimers = 50,
                .bufferSize = 8192,
                .maxConnections = 10
            };
        } else {
            // 标准模式 - 仅使用普通RAM
            return {
                .maxSignals = 100,
                .maxTasks = 20,
                .maxTimers = 10,
                .bufferSize = 2048,
                .maxConnections = 3
            };
        }
    }
};

// ==================== PSRAM配置 ====================
#define PSRAM_THRESHOLD_SIZE 1024        // 超过此大小优先使用PSRAM
#define PSRAM_TEST_SIZE 4096            // PSRAM功能测试大小
#define PSRAM_INIT_RETRY_COUNT 3        // PSRAM初始化重试次数
#define PSRAM_INIT_DELAY_MS 100         // PSRAM初始化延迟

// ==================== 内存管理配置 ====================
#define MIN_FREE_HEAP 10240             // 最小空闲堆内存 (10KB)
#define MIN_FREE_PSRAM 20480            // 最小空闲PSRAM (20KB)
#define MEMORY_CHECK_INTERVAL 30000     // 内存检查间隔 (30秒)

// ==================== 网络配置 ====================
#define WEB_SERVER_PORT 80
#define WEBSOCKET_PATH "/ws"
#define MAX_WEBSOCKET_CLIENTS 5

// WiFi配置 - 请修改为您的网络信息
#define WIFI_SSID "Your_WiFi_SSID"
#define WIFI_PASSWORD "Your_WiFi_Password"
#define WIFI_HOSTNAME "ESP32-S3-IR-Controller"

// AP模式配置
#define AP_SSID_PREFIX "ESP32-S3-IR-"
#define AP_PASSWORD "12345678"
#define AP_IP_ADDRESS IPAddress(192, 168, 4, 1)

// 连接配置
#define WIFI_CONNECTION_TIMEOUT 30000   // WiFi连接超时 (30秒)
#define MAX_CONNECTION_ATTEMPTS 3       // 最大连接尝试次数
#define CONNECTION_RETRY_DELAY 5000     // 连接重试延迟 (5秒)

// ==================== 硬件配置 ====================
#include "hardware-config.h"

// ==================== 文件系统配置 ====================
#define SPIFFS_FORMAT_ON_FAIL true
#define MAX_FILE_SIZE 65536             // 最大文件大小 (64KB)

// 数据文件路径
#define SIGNALS_FILE_PATH "/signals.json"
#define TIMERS_FILE_PATH "/timers.json"
#define TASKS_FILE_PATH "/tasks.json"
#define CONFIG_FILE_PATH "/config.json"
#define LOG_FILE_PATH "/system.log"

// ==================== API配置 ====================
#define API_BASE_PATH "/api"
#define API_TIMEOUT 30000               // API超时 (30秒)
#define MAX_REQUEST_SIZE 8192           // 最大请求大小 (8KB)
#define MAX_RESPONSE_SIZE 16384         // 最大响应大小 (16KB)

// API端点定义
#define API_SIGNALS_PATH "/api/signals"
#define API_TIMERS_PATH "/api/timers"
#define API_TASKS_PATH "/api/tasks"
#define API_SYSTEM_PATH "/api/system"
#define API_LEARN_PATH "/api/learn"
#define API_DATA_PATH "/api/data"

// ==================== 任务配置 ====================
#define TASK_QUEUE_SIZE 50
#define TASK_EXECUTION_TIMEOUT 10000    // 任务执行超时 (10秒)
#define TASK_RETRY_COUNT 3              // 任务重试次数
#define TASK_RETRY_DELAY 1000           // 任务重试延迟 (1秒)

// ==================== 定时器配置 ====================
#define TIMER_RESOLUTION_MS 1000        // 定时器分辨率 (1秒)
#define MAX_TIMER_DURATION 86400000     // 最大定时器时长 (24小时)
#define TIMER_CHECK_INTERVAL 1000       // 定时器检查间隔 (1秒)

// ==================== 学习功能配置 ====================
#define LEARN_TIMEOUT 30000             // 学习超时 (30秒)
#define LEARN_RETRY_COUNT 3             // 学习重试次数
#define LEARN_SIGNAL_BUFFER_SIZE 512    // 学习信号缓冲区大小

// ==================== 日志配置 ====================
#define LOG_LEVEL_DEBUG 0
#define LOG_LEVEL_INFO 1
#define LOG_LEVEL_WARNING 2
#define LOG_LEVEL_ERROR 3

#define CURRENT_LOG_LEVEL LOG_LEVEL_INFO
#define MAX_LOG_FILE_SIZE 32768         // 最大日志文件大小 (32KB)
#define LOG_ROTATION_COUNT 3            // 日志轮转数量

// ==================== 性能监控配置 ====================
#define PERFORMANCE_MONITORING true
#define STATS_UPDATE_INTERVAL 60000     // 统计更新间隔 (1分钟)
#define MEMORY_MONITOR_INTERVAL 30000   // 内存监控间隔 (30秒)

// ==================== 安全配置 ====================
#define ENABLE_CORS true
#define MAX_REQUEST_RATE 100            // 每分钟最大请求数
#define RATE_LIMIT_WINDOW 60000         // 速率限制窗口 (1分钟)

// ==================== 错误处理配置 ====================
#define AUTO_RECOVERY true              // 自动错误恢复
#define MAX_ERROR_COUNT 10              // 最大错误计数
#define ERROR_RESET_INTERVAL 300000     // 错误重置间隔 (5分钟)
#define CRITICAL_ERROR_RESTART true     // 严重错误时重启

// ==================== 开发配置 ====================
#ifdef DEBUG_MODE
    #define DEBUG_PRINT(x) Serial.print(x)
    #define DEBUG_PRINTLN(x) Serial.println(x)
    #define DEBUG_PRINTF(format, ...) Serial.printf(format, ##__VA_ARGS__)
#else
    #define DEBUG_PRINT(x)
    #define DEBUG_PRINTLN(x)
    #define DEBUG_PRINTF(format, ...)
#endif

// ==================== 功能开关 ====================
#define ENABLE_WEBSOCKET true
#define ENABLE_OTA_UPDATE false         // OTA更新功能
#define ENABLE_MDNS true               // mDNS服务发现
#define ENABLE_TIME_SYNC true          // 时间同步
#define ENABLE_BACKUP_RESTORE true     // 备份恢复功能

// ==================== 兼容性配置 ====================
#define LEGACY_API_SUPPORT false       // 旧版API支持
#define FRONTEND_COMPATIBILITY_MODE true // 前端兼容模式

// ==================== 系统限制 ====================
#define MAX_SIGNAL_NAME_LENGTH 64
#define MAX_SIGNAL_CODE_LENGTH 128
#define MAX_DESCRIPTION_LENGTH 256
#define MAX_FILENAME_LENGTH 32

// ==================== 默认值配置 ====================
#define DEFAULT_IR_FREQUENCY 38000      // 默认红外频率
#define DEFAULT_IR_PROTOCOL "NEC"       // 默认红外协议
#define DEFAULT_DEVICE_NAME "ESP32-S3 IR Controller"

#endif // SYSTEM_CONFIG_H
