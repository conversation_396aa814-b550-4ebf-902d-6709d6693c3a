#ifndef DATA_STRUCTURES_H
#define DATA_STRUCTURES_H

#include <Arduino.h>
#include <ArduinoJson.h>

/**
 * 数据结构定义文件
 * 
 * 定义系统中使用的所有数据结构
 * 确保数据格式的一致性和标准化
 */

// ==================== 信号数据结构 ====================

/**
 * 信号数据结构
 */
struct SignalData {
    String id;                          // 信号唯一ID
    String name;                        // 信号名称
    String signalCode;                  // 信号代码
    String protocol;                    // 信号协议 (NEC, RC5, RC6, RAW等)
    String type;                        // 设备类型 (TV, AC, STB等)
    String brand;                       // 品牌
    String model;                       // 型号
    String description;                 // 描述
    unsigned long frequency;            // 载波频率
    unsigned long createdTime;          // 创建时间
    unsigned long lastUsedTime;         // 最后使用时间
    int useCount;                       // 使用次数
    bool isValid;                       // 是否有效
    
    // 原始数据（用于RAW协议）
    uint16_t* rawData;                  // 原始时序数据
    size_t rawDataLength;               // 原始数据长度
    
    /**
     * 构造函数
     */
    SignalData() 
        : id(""), name(""), signalCode(""), protocol("NEC"), type(""), 
          brand(""), model(""), description(""), frequency(38000),
          createdTime(0), lastUsedTime(0), useCount(0), isValid(false),
          rawData(nullptr), rawDataLength(0) {}
    
    /**
     * 析构函数
     */
    ~SignalData() {
        if (rawData) {
            free(rawData);
            rawData = nullptr;
        }
    }
    
    /**
     * 转换为JSON
     * @return DynamicJsonDocument JSON表示
     */
    DynamicJsonDocument toJSON() const {
        DynamicJsonDocument doc(512);
        
        doc["id"] = id;
        doc["name"] = name;
        doc["signalCode"] = signalCode;
        doc["protocol"] = protocol;
        doc["type"] = type;
        doc["brand"] = brand;
        doc["model"] = model;
        doc["description"] = description;
        doc["frequency"] = frequency;
        doc["createdTime"] = createdTime;
        doc["lastUsedTime"] = lastUsedTime;
        doc["useCount"] = useCount;
        doc["isValid"] = isValid;
        
        // 原始数据处理
        if (rawData && rawDataLength > 0) {
            JsonArray rawArray = doc.createNestedArray("rawData");
            for (size_t i = 0; i < rawDataLength; i++) {
                rawArray.add(rawData[i]);
            }
            doc["rawDataLength"] = rawDataLength;
        }
        
        return doc;
    }
    
    /**
     * 从JSON创建
     * @param json JSON数据
     * @return bool 是否成功
     */
    bool fromJSON(const DynamicJsonDocument& json) {
        if (!json.containsKey("id") || !json.containsKey("name") || !json.containsKey("signalCode")) {
            return false;
        }
        
        id = json["id"].as<String>();
        name = json["name"].as<String>();
        signalCode = json["signalCode"].as<String>();
        protocol = json["protocol"] | "NEC";
        type = json["type"] | "";
        brand = json["brand"] | "";
        model = json["model"] | "";
        description = json["description"] | "";
        frequency = json["frequency"] | 38000;
        createdTime = json["createdTime"] | millis();
        lastUsedTime = json["lastUsedTime"] | 0;
        useCount = json["useCount"] | 0;
        isValid = json["isValid"] | true;
        
        // 处理原始数据
        if (json.containsKey("rawData") && json["rawData"].is<JsonArray>()) {
            JsonArray rawArray = json["rawData"];
            rawDataLength = rawArray.size();
            
            if (rawDataLength > 0) {
                rawData = (uint16_t*)malloc(rawDataLength * sizeof(uint16_t));
                if (rawData) {
                    for (size_t i = 0; i < rawDataLength; i++) {
                        rawData[i] = rawArray[i];
                    }
                } else {
                    rawDataLength = 0;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 验证数据有效性
     * @return bool 是否有效
     */
    bool validate() const {
        if (id.isEmpty() || name.isEmpty() || signalCode.isEmpty()) {
            return false;
        }
        
        if (protocol.isEmpty()) {
            return false;
        }
        
        if (frequency < 30000 || frequency > 60000) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 更新使用统计
     */
    void updateUsage() {
        lastUsedTime = millis();
        useCount++;
    }
};

// ==================== 任务数据结构 ====================

/**
 * 任务类型枚举
 */
enum class TaskType {
    SINGLE_SIGNAL,      // 单个信号
    ALL_SIGNALS,        // 所有信号
    SELECTED_SIGNALS,   // 选定信号
    SCHEDULED,          // 定时任务
    CUSTOM              // 自定义任务
};

/**
 * 任务状态枚举
 */
enum class TaskStatus {
    PENDING,            // 等待中
    RUNNING,            // 执行中
    PAUSED,             // 已暂停
    COMPLETED,          // 已完成
    FAILED,             // 失败
    CANCELLED           // 已取消
};

/**
 * 任务数据结构
 */
struct TaskData {
    String id;                          // 任务唯一ID
    String name;                        // 任务名称
    TaskType type;                      // 任务类型
    TaskStatus status;                  // 任务状态
    String description;                 // 任务描述
    
    // 执行参数
    std::vector<String> signalIds;      // 信号ID列表
    bool loopMode;                      // 是否循环模式
    int interval;                       // 信号间隔（毫秒）
    int repeatCount;                    // 重复次数 (-1表示无限循环)
    
    // 时间信息
    unsigned long createdTime;          // 创建时间
    unsigned long startTime;            // 开始时间
    unsigned long endTime;              // 结束时间
    unsigned long scheduledTime;        // 计划执行时间
    
    // 执行统计
    int currentIndex;                   // 当前执行索引
    int completedSignals;               // 已完成信号数
    int totalSignals;                   // 总信号数
    int currentLoop;                    // 当前循环次数
    
    // 结果信息
    String errorMessage;                // 错误信息
    bool success;                       // 是否成功
    
    /**
     * 构造函数
     */
    TaskData() 
        : id(""), name(""), type(TaskType::SINGLE_SIGNAL), status(TaskStatus::PENDING),
          description(""), loopMode(false), interval(100), repeatCount(1),
          createdTime(0), startTime(0), endTime(0), scheduledTime(0),
          currentIndex(0), completedSignals(0), totalSignals(0), currentLoop(0),
          errorMessage(""), success(false) {}
    
    /**
     * 转换为JSON
     * @return DynamicJsonDocument JSON表示
     */
    DynamicJsonDocument toJSON() const {
        DynamicJsonDocument doc(1024);
        
        doc["id"] = id;
        doc["name"] = name;
        doc["type"] = static_cast<int>(type);
        doc["status"] = static_cast<int>(status);
        doc["description"] = description;
        doc["loopMode"] = loopMode;
        doc["interval"] = interval;
        doc["repeatCount"] = repeatCount;
        doc["createdTime"] = createdTime;
        doc["startTime"] = startTime;
        doc["endTime"] = endTime;
        doc["scheduledTime"] = scheduledTime;
        doc["currentIndex"] = currentIndex;
        doc["completedSignals"] = completedSignals;
        doc["totalSignals"] = totalSignals;
        doc["currentLoop"] = currentLoop;
        doc["errorMessage"] = errorMessage;
        doc["success"] = success;
        
        // 信号ID列表
        JsonArray signalArray = doc.createNestedArray("signalIds");
        for (const String& signalId : signalIds) {
            signalArray.add(signalId);
        }
        
        return doc;
    }
    
    /**
     * 从JSON创建
     * @param json JSON数据
     * @return bool 是否成功
     */
    bool fromJSON(const DynamicJsonDocument& json) {
        if (!json.containsKey("id") || !json.containsKey("name")) {
            return false;
        }
        
        id = json["id"].as<String>();
        name = json["name"].as<String>();
        type = static_cast<TaskType>(json["type"] | 0);
        status = static_cast<TaskStatus>(json["status"] | 0);
        description = json["description"] | "";
        loopMode = json["loopMode"] | false;
        interval = json["interval"] | 100;
        repeatCount = json["repeatCount"] | 1;
        createdTime = json["createdTime"] | millis();
        startTime = json["startTime"] | 0;
        endTime = json["endTime"] | 0;
        scheduledTime = json["scheduledTime"] | 0;
        currentIndex = json["currentIndex"] | 0;
        completedSignals = json["completedSignals"] | 0;
        totalSignals = json["totalSignals"] | 0;
        currentLoop = json["currentLoop"] | 0;
        errorMessage = json["errorMessage"] | "";
        success = json["success"] | false;
        
        // 处理信号ID列表
        if (json.containsKey("signalIds") && json["signalIds"].is<JsonArray>()) {
            JsonArray signalArray = json["signalIds"];
            signalIds.clear();
            for (JsonVariant signalId : signalArray) {
                signalIds.push_back(signalId.as<String>());
            }
        }
        
        return true;
    }
    
    /**
     * 验证数据有效性
     * @return bool 是否有效
     */
    bool validate() const {
        if (id.isEmpty() || name.isEmpty()) {
            return false;
        }
        
        if (interval < 0 || repeatCount < -1) {
            return false;
        }
        
        if (type == TaskType::SINGLE_SIGNAL && signalIds.size() != 1) {
            return false;
        }
        
        if ((type == TaskType::ALL_SIGNALS || type == TaskType::SELECTED_SIGNALS) && signalIds.empty()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 计算进度百分比
     * @return float 进度百分比 (0-100)
     */
    float getProgress() const {
        if (totalSignals == 0) {
            return 0.0f;
        }
        
        return (float)completedSignals / totalSignals * 100.0f;
    }
    
    /**
     * 获取预估剩余时间
     * @return unsigned long 剩余时间（毫秒）
     */
    unsigned long getEstimatedRemainingTime() const {
        if (status != TaskStatus::RUNNING || totalSignals == 0 || completedSignals == 0) {
            return 0;
        }
        
        unsigned long elapsed = millis() - startTime;
        unsigned long avgTimePerSignal = elapsed / completedSignals;
        unsigned long remainingSignals = totalSignals - completedSignals;
        
        return remainingSignals * avgTimePerSignal;
    }
};

// ==================== 定时器数据结构 ====================

/**
 * 定时器类型枚举
 */
enum class TimerType {
    ONCE,               // 一次性
    DAILY,              // 每日
    WEEKLY,             // 每周
    MONTHLY,            // 每月
    CUSTOM              // 自定义
};

/**
 * 定时器状态枚举
 */
enum class TimerStatus {
    ACTIVE,             // 激活
    INACTIVE,           // 未激活
    EXPIRED,            // 已过期
    ERROR               // 错误
};

/**
 * 定时器数据结构
 */
struct TimerData {
    String id;                          // 定时器唯一ID
    String name;                        // 定时器名称
    TimerType type;                     // 定时器类型
    TimerStatus status;                 // 定时器状态
    String description;                 // 描述
    
    // 时间设置
    unsigned long triggerTime;          // 触发时间
    unsigned long nextTriggerTime;      // 下次触发时间
    String cronExpression;              // Cron表达式（用于复杂调度）
    
    // 执行设置
    String taskId;                      // 关联的任务ID
    bool enabled;                       // 是否启用
    int maxExecutions;                  // 最大执行次数 (-1表示无限)
    
    // 统计信息
    unsigned long createdTime;          // 创建时间
    unsigned long lastExecutionTime;    // 最后执行时间
    int executionCount;                 // 执行次数
    
    /**
     * 构造函数
     */
    TimerData() 
        : id(""), name(""), type(TimerType::ONCE), status(TimerStatus::INACTIVE),
          description(""), triggerTime(0), nextTriggerTime(0), cronExpression(""),
          taskId(""), enabled(false), maxExecutions(1),
          createdTime(0), lastExecutionTime(0), executionCount(0) {}
    
    /**
     * 转换为JSON
     * @return DynamicJsonDocument JSON表示
     */
    DynamicJsonDocument toJSON() const {
        DynamicJsonDocument doc(512);
        
        doc["id"] = id;
        doc["name"] = name;
        doc["type"] = static_cast<int>(type);
        doc["status"] = static_cast<int>(status);
        doc["description"] = description;
        doc["triggerTime"] = triggerTime;
        doc["nextTriggerTime"] = nextTriggerTime;
        doc["cronExpression"] = cronExpression;
        doc["taskId"] = taskId;
        doc["enabled"] = enabled;
        doc["maxExecutions"] = maxExecutions;
        doc["createdTime"] = createdTime;
        doc["lastExecutionTime"] = lastExecutionTime;
        doc["executionCount"] = executionCount;
        
        return doc;
    }
    
    /**
     * 从JSON创建
     * @param json JSON数据
     * @return bool 是否成功
     */
    bool fromJSON(const DynamicJsonDocument& json) {
        if (!json.containsKey("id") || !json.containsKey("name")) {
            return false;
        }
        
        id = json["id"].as<String>();
        name = json["name"].as<String>();
        type = static_cast<TimerType>(json["type"] | 0);
        status = static_cast<TimerStatus>(json["status"] | 0);
        description = json["description"] | "";
        triggerTime = json["triggerTime"] | 0;
        nextTriggerTime = json["nextTriggerTime"] | 0;
        cronExpression = json["cronExpression"] | "";
        taskId = json["taskId"] | "";
        enabled = json["enabled"] | false;
        maxExecutions = json["maxExecutions"] | 1;
        createdTime = json["createdTime"] | millis();
        lastExecutionTime = json["lastExecutionTime"] | 0;
        executionCount = json["executionCount"] | 0;
        
        return true;
    }
    
    /**
     * 验证数据有效性
     * @return bool 是否有效
     */
    bool validate() const {
        if (id.isEmpty() || name.isEmpty()) {
            return false;
        }
        
        if (taskId.isEmpty()) {
            return false;
        }
        
        if (triggerTime == 0 && cronExpression.isEmpty()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查是否应该触发
     * @return bool 是否应该触发
     */
    bool shouldTrigger() const {
        if (!enabled || status != TimerStatus::ACTIVE) {
            return false;
        }
        
        unsigned long currentTime = millis();
        return currentTime >= nextTriggerTime;
    }
    
    /**
     * 更新下次触发时间
     */
    void updateNextTriggerTime() {
        unsigned long currentTime = millis();
        
        switch (type) {
            case TimerType::ONCE:
                // 一次性定时器不需要更新
                break;
                
            case TimerType::DAILY:
                nextTriggerTime = currentTime + (24 * 60 * 60 * 1000); // 24小时后
                break;
                
            case TimerType::WEEKLY:
                nextTriggerTime = currentTime + (7 * 24 * 60 * 60 * 1000); // 7天后
                break;
                
            case TimerType::MONTHLY:
                nextTriggerTime = currentTime + (30 * 24 * 60 * 60 * 1000); // 30天后
                break;
                
            case TimerType::CUSTOM:
                // 自定义类型需要解析cron表达式
                // 第二阶段简化处理
                nextTriggerTime = currentTime + (60 * 60 * 1000); // 1小时后
                break;
        }
    }
};

#endif // DATA_STRUCTURES_H
