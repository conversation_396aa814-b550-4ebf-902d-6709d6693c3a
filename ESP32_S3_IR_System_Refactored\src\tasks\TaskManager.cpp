#include "TaskManager.h"
#include "../data/DataManager.h"
#include "../hardware/IRController.h"
#include "../core/MemoryAllocator.h"

// ==================== 构造函数和析构函数 ====================
TaskManager::TaskManager(const SystemCapacity& capacity)
    : m_capacity(capacity)
    , m_initialized(false)
    , m_dataManager(nullptr)
    , m_irController(nullptr)
    , m_currentTaskId("")
    , m_isExecuting(false)
    , m_isPaused(false)
    , m_taskStartTime(0)
    , m_taskQueue(nullptr)
    , m_taskHistory(nullptr)
    , m_historySize(0)
    , m_totalTasks(0)
    , m_completedTasks(0)
    , m_failedTasks(0)
{
    Serial.printf("⚙️  TaskManager created with capacity: %d tasks\n", m_capacity.maxTasks);
}

TaskManager::~TaskManager() {
    shutdown();
    cleanupStorage();
    Serial.println("⚙️  TaskManager destroyed");
}

// ==================== 系统生命周期 ====================
bool TaskManager::initialize() {
    if (m_initialized) {
        Serial.println("⚠️  TaskManager already initialized");
        return true;
    }
    
    Serial.println("⚙️  Initializing TaskManager...");
    
    // 1. 初始化存储
    if (!initializeStorage()) {
        logError("Storage initialization failed");
        return false;
    }
    
    m_initialized = true;
    Serial.println("✅ TaskManager initialization completed");
    return true;
}

void TaskManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("⚙️  Shutting down TaskManager...");
    
    // 停止所有任务
    stopAllTasks();
    
    m_initialized = false;
    Serial.println("✅ TaskManager shutdown completed");
}

bool TaskManager::isHealthy() const {
    return m_initialized && m_taskQueue && m_taskHistory;
}

void TaskManager::handleLoop() {
    if (!m_initialized) {
        return;
    }

    // 1. 处理当前执行的任务
    if (m_isExecuting && !m_isPaused) {
        processCurrentTask();
    }

    // 2. 检查任务队列
    processTaskQueue();

    // 3. 检查定时任务
    processScheduledTasks();

    // 4. 清理完成的任务
    cleanupCompletedTasks();

    // 5. 定期保存任务状态
    static unsigned long lastSave = 0;
    if (millis() - lastSave > 30000) {  // 每30秒保存一次
        saveTaskState();
        lastSave = millis();
    }
}

void TaskManager::setDataManager(DataManager* dataManager) {
    m_dataManager = dataManager;
    Serial.println("⚙️  TaskManager: DataManager dependency set");
}

void TaskManager::setIRController(IRController* irController) {
    m_irController = irController;
    Serial.println("⚙️  TaskManager: IRController dependency set");
}

// ==================== 任务执行 ====================
bool TaskManager::executeTask(const String& taskId) {
    if (!m_initialized || !m_dataManager || !m_irController) {
        logError("TaskManager not properly initialized or missing dependencies");
        return false;
    }
    
    if (m_isExecuting) {
        logError("Another task is already executing");
        return false;
    }
    
    // 第一阶段：模拟任务执行
    Serial.printf("⚙️  Executing task: %s - SIMULATED\n", taskId.c_str());
    
    m_currentTaskId = taskId;
    m_isExecuting = true;
    m_taskStartTime = millis();
    
    // 模拟任务执行
    delay(100);
    
    // 模拟任务完成
    handleTaskCompletion(taskId, true);
    
    return true;
}

bool TaskManager::stopTask(const String& taskId) {
    if (m_currentTaskId == taskId && m_isExecuting) {
        m_isExecuting = false;
        m_isPaused = false;
        m_currentTaskId = "";
        
        Serial.printf("⚙️  Task stopped: %s\n", taskId.c_str());
        handleTaskCompletion(taskId, false);
        return true;
    }
    
    return false;
}

bool TaskManager::pauseTask(const String& taskId) {
    if (m_currentTaskId == taskId && m_isExecuting && !m_isPaused) {
        m_isPaused = true;
        Serial.printf("⚙️  Task paused: %s\n", taskId.c_str());
        return true;
    }
    
    return false;
}

bool TaskManager::resumeTask(const String& taskId) {
    if (m_currentTaskId == taskId && m_isExecuting && m_isPaused) {
        m_isPaused = false;
        Serial.printf("⚙️  Task resumed: %s\n", taskId.c_str());
        return true;
    }
    
    return false;
}

DynamicJsonDocument TaskManager::getTaskStatus(const String& taskId) {
    DynamicJsonDocument status(512);
    
    status["task_id"] = taskId;
    status["is_current"] = (m_currentTaskId == taskId);
    status["is_executing"] = m_isExecuting;
    status["is_paused"] = m_isPaused;
    
    if (m_currentTaskId == taskId && m_isExecuting) {
        status["start_time"] = m_taskStartTime;
        status["elapsed_time"] = millis() - m_taskStartTime;
    }
    
    return status;
}

// ==================== 批量操作 ====================
String TaskManager::executeAllSignals(bool loopMode, int interval) {
    if (!m_initialized || !m_dataManager) {
        logError("TaskManager not properly initialized");
        return "";
    }
    
    String taskId = generateTaskId();
    
    // 第一阶段：模拟批量执行
    Serial.printf("⚙️  Executing all signals (loop: %s, interval: %d ms) - SIMULATED\n", 
                 loopMode ? "true" : "false", interval);
    
    // 获取所有信号
    DynamicJsonDocument signals = m_dataManager->getAllSignals();
    
    // 模拟执行
    m_currentTaskId = taskId;
    m_isExecuting = true;
    m_taskStartTime = millis();
    
    // 第一阶段：立即完成
    delay(200);
    handleTaskCompletion(taskId, true);
    
    return taskId;
}

String TaskManager::executeSelectedSignals(const std::vector<String>& signalIds, bool loopMode, int interval) {
    if (!m_initialized || !m_dataManager) {
        logError("TaskManager not properly initialized");
        return "";
    }
    
    String taskId = generateTaskId();
    
    // 第一阶段：模拟选定信号执行
    Serial.printf("⚙️  Executing %d selected signals - SIMULATED\n", signalIds.size());
    
    m_currentTaskId = taskId;
    m_isExecuting = true;
    m_taskStartTime = millis();
    
    // 模拟执行每个信号
    for (const String& signalId : signalIds) {
        if (!executeSingleSignal(signalId)) {
            logError("Failed to execute signal: " + signalId);
        }
        if (interval > 0) {
            delay(interval);
        }
    }
    
    handleTaskCompletion(taskId, true);
    return taskId;
}

int TaskManager::stopAllTasks() {
    int stoppedCount = 0;
    
    if (m_isExecuting) {
        stopTask(m_currentTaskId);
        stoppedCount = 1;
    }
    
    Serial.printf("⚙️  Stopped %d tasks\n", stoppedCount);
    return stoppedCount;
}

// ==================== 任务调度 ====================
String TaskManager::scheduleTask(const DynamicJsonDocument& taskData) {
    // 第一阶段：基本调度占位符
    String taskId = generateTaskId();
    
    Serial.printf("⚙️  Task scheduled: %s - SIMULATED\n", taskId.c_str());
    return taskId;
}

bool TaskManager::cancelScheduledTask(const String& taskId) {
    // 第一阶段：模拟取消
    Serial.printf("⚙️  Scheduled task cancelled: %s - SIMULATED\n", taskId.c_str());
    return true;
}

DynamicJsonDocument TaskManager::getScheduledTasks() {
    DynamicJsonDocument tasks(512);
    tasks.to<JsonArray>();
    
    // 第一阶段：返回空数组
    return tasks;
}

// ==================== 任务监控 ====================
DynamicJsonDocument TaskManager::getCurrentTask() {
    DynamicJsonDocument task(512);
    
    if (m_isExecuting) {
        task["task_id"] = m_currentTaskId;
        task["is_executing"] = true;
        task["is_paused"] = m_isPaused;
        task["start_time"] = m_taskStartTime;
        task["elapsed_time"] = millis() - m_taskStartTime;
    } else {
        task["task_id"] = "";
        task["is_executing"] = false;
    }
    
    return task;
}

DynamicJsonDocument TaskManager::getTaskQueue() {
    if (!m_taskQueue) {
        DynamicJsonDocument queue(512);
        queue.to<JsonArray>();
        return queue;
    }

    // 获取队列状态和统计信息
    DynamicJsonDocument queueInfo(1024);
    queueInfo["status"] = m_taskQueue->getQueueStatus();
    queueInfo["statistics"] = m_taskQueue->getStatistics();
    queueInfo["priority_distribution"] = m_taskQueue->getPriorityDistribution();

    return queueInfo;
}

DynamicJsonDocument TaskManager::getTaskHistory(int limit) {
    DynamicJsonDocument history(1024);
    history.to<JsonArray>();
    
    // 第一阶段：返回空历史
    return history;
}

DynamicJsonDocument TaskManager::getTaskStatistics() {
    DynamicJsonDocument stats(256);
    
    stats["total_tasks"] = m_totalTasks;
    stats["completed_tasks"] = m_completedTasks;
    stats["failed_tasks"] = m_failedTasks;
    stats["success_rate"] = m_totalTasks > 0 ? (float)m_completedTasks / m_totalTasks * 100 : 0;
    stats["is_executing"] = m_isExecuting;
    stats["current_task"] = m_currentTaskId;
    
    return stats;
}

// ==================== 私有方法 ====================
bool TaskManager::initializeStorage() {
    // 创建任务队列管理器
    m_taskQueue = new TaskQueue(m_capacity.maxTasks);
    if (!m_taskQueue) {
        logError("Failed to create task queue");
        return false;
    }

    // 分配历史存储
    size_t historyStorageSize = 100 * 256;  // 历史记录100条
    m_taskHistory = MemoryAllocator::smartAlloc(historyStorageSize);

    if (!m_taskHistory) {
        logError("Failed to allocate task history storage");
        cleanupStorage();
        return false;
    }

    memset(m_taskHistory, 0, historyStorageSize);
    m_historySize = historyStorageSize;

    Serial.printf("⚙️  Task storage initialized: TaskQueue created, %d KB history allocated\n",
                 historyStorageSize / 1024);
    return true;
}

void TaskManager::cleanupStorage() {
    if (m_taskQueue) {
        delete m_taskQueue;
        m_taskQueue = nullptr;
    }

    if (m_taskHistory) {
        MemoryAllocator::smartFree(m_taskHistory);
        m_taskHistory = nullptr;
        m_historySize = 0;
    }
}

bool TaskManager::executeSingleSignal(const String& signalId) {
    if (!m_irController || !m_dataManager) {
        return false;
    }
    
    // 第一阶段：模拟单个信号执行
    DynamicJsonDocument signal = m_dataManager->getSignal(signalId);
    
    if (signal.containsKey("signalCode")) {
        return m_irController->sendSignal(signal["signalCode"], signal["protocol"] | "NEC");
    }
    
    return false;
}

void TaskManager::handleTaskCompletion(const String& taskId, bool success) {
    m_isExecuting = false;
    m_isPaused = false;
    m_currentTaskId = "";
    
    updateStatistics(success);
    
    // 添加到历史记录
    DynamicJsonDocument taskData(256);
    taskData["task_id"] = taskId;
    taskData["success"] = success;
    taskData["completion_time"] = millis();
    taskData["duration"] = millis() - m_taskStartTime;
    
    addToHistory(taskData);
    
    Serial.printf("⚙️  Task completed: %s (success: %s)\n", 
                 taskId.c_str(), success ? "true" : "false");
}

void TaskManager::addToHistory(const DynamicJsonDocument& taskData) {
    // 第一阶段：基本历史记录占位符
    Serial.println("⚙️  Task added to history - SIMULATED");
}

String TaskManager::generateTaskId() {
    static unsigned long counter = 0;
    return "task_" + String(millis()) + "_" + String(counter++);
}

void TaskManager::logError(const String& error) {
    Serial.printf("❌ TaskManager Error: %s\n", error.c_str());
}

void TaskManager::updateStatistics(bool success) {
    m_totalTasks++;
    if (success) {
        m_completedTasks++;
    } else {
        m_failedTasks++;
    }
}

// ==================== 任务处理方法实现 ====================
void TaskManager::processCurrentTask() {
    if (m_currentTaskId.isEmpty()) {
        return;
    }

    // 检查任务是否超时
    unsigned long currentTime = millis();
    if (currentTime - m_taskStartTime > 300000) {  // 5分钟超时
        logError("Task timeout: " + m_currentTaskId);
        handleTaskCompletion(m_currentTaskId, false);
        return;
    }

    // 第五阶段：基本任务处理
    // 实际的任务执行逻辑在executeTask中处理
}

void TaskManager::processTaskQueue() {
    if (!m_taskQueue || m_isExecuting) {
        return;  // 当前有任务在执行，不处理队列
    }

    // 1. 提升到期的定时任务到主队列
    m_taskQueue->promoteScheduledTasks();

    // 2. 检查是否有待执行的任务
    if (!m_taskQueue->isEmpty()) {
        QueuedTask nextTask = m_taskQueue->dequeue();

        if (!nextTask.taskId.isEmpty()) {
            Serial.printf("⚙️  Starting queued task: %s\n", nextTask.taskId.c_str());
            executeTask(nextTask.taskId);
        }
    }
}

void TaskManager::processScheduledTasks() {
    // 第五阶段：检查定时任务
    unsigned long currentTime = millis();

    // 简化的定时任务检查
    // 完整的定时任务管理将在后续实现

    static unsigned long lastScheduleCheck = 0;
    if (currentTime - lastScheduleCheck > 60000) {  // 每分钟检查一次
        Serial.println("⚙️  Checking scheduled tasks...");
        lastScheduleCheck = currentTime;
    }
}

void TaskManager::cleanupCompletedTasks() {
    // 第五阶段：清理已完成的任务
    static unsigned long lastCleanup = 0;
    unsigned long currentTime = millis();

    if (currentTime - lastCleanup > 300000) {  // 每5分钟清理一次
        Serial.println("⚙️  Cleaning up completed tasks...");

        // 简化的清理逻辑
        // 完整的清理将在后续实现

        lastCleanup = currentTime;
    }
}

void TaskManager::saveTaskState() {
    // 第五阶段：保存任务状态
    if (!m_dataManager) {
        return;
    }

    // 简化的状态保存
    Serial.println("⚙️  Saving task state...");

    // 创建任务状态数据
    DynamicJsonDocument taskState(512);
    taskState["current_task_id"] = m_currentTaskId;
    taskState["is_executing"] = m_isExecuting;
    taskState["is_paused"] = m_isPaused;
    taskState["task_start_time"] = m_taskStartTime;
    taskState["total_tasks"] = m_totalTasks;
    taskState["completed_tasks"] = m_completedTasks;
    taskState["failed_tasks"] = m_failedTasks;
    taskState["save_time"] = millis();

    // 第五阶段：简化保存，实际保存逻辑将在后续实现
}

bool TaskManager::loadTaskState() {
    // 第五阶段：加载任务状态
    if (!m_dataManager) {
        return false;
    }

    Serial.println("⚙️  Loading task state...");

    // 简化的状态加载
    // 完整的加载逻辑将在后续实现

    return true;
}

void TaskManager::resetTaskState() {
    // 重置任务状态
    m_currentTaskId = "";
    m_isExecuting = false;
    m_isPaused = false;
    m_taskStartTime = 0;

    Serial.println("⚙️  Task state reset");
}

// ==================== 任务队列管理实现 ====================
bool TaskManager::enqueueTask(const String& taskId, TaskPriority priority) {
    if (!m_taskQueue) {
        logError("Task queue not initialized");
        return false;
    }

    return m_taskQueue->enqueue(taskId, priority);
}

bool TaskManager::enqueueScheduledTask(const String& taskId, unsigned long scheduleTime, TaskPriority priority) {
    if (!m_taskQueue) {
        logError("Task queue not initialized");
        return false;
    }

    return m_taskQueue->enqueueScheduled(taskId, scheduleTime, priority);
}

bool TaskManager::removeTaskFromQueue(const String& taskId) {
    if (!m_taskQueue) {
        logError("Task queue not initialized");
        return false;
    }

    return m_taskQueue->remove(taskId);
}

bool TaskManager::updateTaskPriority(const String& taskId, TaskPriority newPriority) {
    if (!m_taskQueue) {
        logError("Task queue not initialized");
        return false;
    }

    return m_taskQueue->updatePriority(taskId, newPriority);
}

int TaskManager::getTaskQueuePosition(const String& taskId) {
    if (!m_taskQueue) {
        return -1;
    }

    return m_taskQueue->getPosition(taskId);
}

void TaskManager::clearTaskQueue() {
    if (m_taskQueue) {
        m_taskQueue->clear();
        Serial.println("⚙️  Task queue cleared");
    }
}
