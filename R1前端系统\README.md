# R1 ESP32-S3 红外控制系统 - 前端

基于ESP32-S3的智能红外控制系统前端界面，采用事件驱动架构，专为ESP32S3-WROOM-N16R8硬件平台优化。

## 📋 项目概述

本项目是R1智能红外控制系统的前端部分，提供完整的Web界面来管理和控制ESP32-S3红外设备。系统采用模块化设计，支持信号管理、定时控制、系统监控等功能。

### 🎯 核心特性

- **事件驱动架构** - 模块间零直接依赖，通过EventBus通信
- **模块化设计** - 每个功能模块独立开发和维护
- **响应式界面** - 支持桌面和移动设备
- **实时监控** - 系统状态和性能实时显示
- **OTA升级** - 支持远程固件更新
- **深色主题** - 适合控制系统的专业界面

## 🏗️ 系统架构

### 核心组件

```
R1前端系统/
├── js/
│   ├── config.js              # 系统配置
│   ├── app.js                 # 应用启动脚本
│   ├── core/                  # 核心组件
│   │   ├── EventBus.js        # 事件总线
│   │   ├── BaseModule.js      # 模块基类
│   │   ├── ESP32Communicator.js # ESP32通信
│   │   └── R1System.js        # 系统管理器
│   └── modules/               # 功能模块
│       ├── SignalManager.js   # 信号管理
│       ├── ControlCenter.js   # 控制中心
│       ├── TimerManager.js    # 定时器管理
│       ├── SystemMonitor.js   # 系统监控
│       └── OTAManager.js      # OTA升级管理
├── css/
│   ├── main.css              # 主要样式
│   └── modules.css           # 模块样式
└── index.html                # 主页面
```

### 架构原则

1. **事件驱动优先** - 避免无意义循环，所有功能通过事件触发
2. **按需资源分配** - 需要时分配，用完释放，避免资源浪费
3. **模块完全解耦** - 模块间零直接依赖，通过事件通信
4. **硬件性能优化** - 针对ESP32S3双核240MHz处理器和8MB内存优化

## 🚀 快速开始

### 环境要求

- 现代Web浏览器（Chrome 80+, Firefox 75+, Safari 13+）
- ESP32-S3设备运行对应的后端固件
- 设备与浏览器在同一网络

### 部署步骤

1. **上传文件到ESP32**
   ```bash
   # 将整个R1前端系统文件夹上传到ESP32的SPIFFS文件系统
   # 确保文件结构完整
   ```

2. **配置网络**
   ```javascript
   // 在js/config.js中配置ESP32的IP地址
   esp32: {
     connection: {
       host: '*************', // 修改为你的ESP32 IP地址
       port: 80
     }
   }
   ```

3. **访问系统**
   ```
   http://[ESP32_IP_ADDRESS]/
   ```

### 开发环境

如果需要在开发环境中运行：

1. **启动本地服务器**
   ```bash
   # 使用Python
   python -m http.server 8080
   
   # 或使用Node.js
   npx http-server -p 8080
   ```

2. **配置代理**
   ```javascript
   // 修改config.js中的ESP32地址
   host: '*************' // ESP32的实际IP
   ```

## 📱 功能模块

### 1. 信号管理 (SignalManager)

- **信号学习** - 学习遥控器信号
- **信号管理** - 增删改查信号数据
- **批量操作** - 批量发送、导出、删除
- **分类管理** - 信号分类和标签
- **搜索过滤** - 快速查找信号

### 2. 控制中心 (ControlCenter)

- **快速控制** - 常用信号快速访问
- **批量发送** - 多信号批量执行
- **最近使用** - 历史记录管理
- **任务监控** - 运行任务状态监控

### 3. 定时器管理 (TimerManager)

- **定时任务** - 创建和管理定时任务
- **重复设置** - 支持每日、每周、自定义重复
- **实时时钟** - 系统时间显示
- **任务状态** - 定时器启用/禁用管理

### 4. 系统监控 (SystemMonitor)

- **系统状态** - CPU、内存、温度监控
- **性能图表** - 历史数据可视化
- **硬件信息** - 设备硬件详情
- **系统日志** - 错误和操作日志
- **错误统计** - 系统错误分析

### 5. OTA升级 (OTAManager)

- **版本检查** - 自动检查固件更新
- **固件升级** - 远程固件更新
- **进度监控** - 升级进度实时显示
- **安全认证** - 升级前身份验证
- **备份恢复** - 升级前自动备份

## 🔧 配置说明

### 系统配置 (js/config.js)

```javascript
// ESP32连接配置
esp32: {
  connection: {
    host: '*************',    // ESP32 IP地址
    port: 80,                 // 端口号
    timeout: 10000,           // 请求超时时间
    retryAttempts: 3          // 重试次数
  }
}

// UI配置
ui: {
  defaults: {
    defaultModule: 'SignalManager',  // 默认模块
    theme: 'dark',                   // 主题
    language: 'zh-CN'                // 语言
  }
}
```

### 模块配置

每个模块都有独立的配置选项，可在config.js中的`ui.modules`部分进行调整。

## 🎨 界面定制

### 主题配置

系统使用CSS变量定义主题，可在`css/main.css`中修改：

```css
:root {
  --primary-color: #007bff;      /* 主色调 */
  --bg-primary: #1a1a1a;        /* 主背景色 */
  --text-primary: #ffffff;       /* 主文字色 */
  /* ... 更多变量 */
}
```

### 响应式设计

系统支持多种屏幕尺寸：
- 桌面端：1200px+
- 平板端：768px - 1199px
- 手机端：< 768px

## 🔍 调试和开发

### 调试模式

在`js/config.js`中启用调试模式：

```javascript
debug: {
  enabled: true,
  logLevel: 'debug',
  enableConsoleOutput: true,
  enableEventLogging: true
}
```

### 开发工具

系统提供了开发调试接口：

```javascript
// 浏览器控制台中可用
window.R1App.switchModule('SignalManager');  // 切换模块
window.R1App.showNotification('测试', 'info'); // 显示通知
window.r1System.eventBus.emit('test.event');  // 发送事件
```

### 性能监控

系统内置性能监控，可在控制台查看：

```javascript
// 查看事件总线性能
window.r1System.eventBus.getPerformance();

// 查看模块状态
window.r1System.modules.get('SignalManager').getStatus();
```

## 📚 API文档

### 事件系统

系统使用事件驱动架构，主要事件类型：

```javascript
// 系统级事件
'system.ready'           // 系统启动完成
'system.error'           // 系统错误
'system.refresh'         // 系统刷新

// 模块级事件
'module.ready'           // 模块就绪
'module.error'           // 模块错误
'module.switch'          // 模块切换

// ESP32通信事件
'esp32.connected'        // ESP32连接成功
'esp32.disconnected'     // ESP32连接断开
'esp32.error'           // ESP32通信错误
```

### ESP32 API端点

系统与ESP32后端通信的API端点：

```
GET    /api/signals              # 获取信号列表
POST   /api/signals              # 创建新信号
GET    /api/signals/{id}         # 获取特定信号
PUT    /api/signals/{id}         # 更新信号
DELETE /api/signals/{id}         # 删除信号
POST   /api/signals/{id}/emit    # 发送信号

GET    /api/timers               # 获取定时器列表
POST   /api/timers               # 创建定时器
POST   /api/timers/{id}/toggle   # 切换定时器状态

GET    /api/system/status        # 获取系统状态
GET    /api/system/performance   # 获取性能数据
GET    /api/system/hardware      # 获取硬件信息

GET    /api/ota/status           # 获取OTA状态
POST   /api/ota/firmware         # 固件更新
```

## 🛠️ 故障排除

### 常见问题

1. **无法连接ESP32**
   - 检查网络连接
   - 确认ESP32 IP地址正确
   - 检查防火墙设置

2. **页面加载缓慢**
   - 检查网络延迟
   - 启用浏览器缓存
   - 优化图片和资源

3. **功能异常**
   - 打开浏览器开发者工具查看错误
   - 检查ESP32后端日志
   - 确认固件版本兼容性

### 日志查看

系统日志可在以下位置查看：
- 浏览器控制台：开发者工具 > Console
- 系统监控模块：系统日志面板
- ESP32设备：串口监视器

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 🤝 贡献

欢迎提交问题和功能请求！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 支持

如有问题，请通过以下方式联系：

- 项目Issues：[GitHub Issues](https://github.com/your-repo/issues)
- 邮箱：<EMAIL>
- 文档：[在线文档](https://docs.r1system.com)

---

**R1 ESP32-S3 红外控制系统** - 让智能控制更简单！
