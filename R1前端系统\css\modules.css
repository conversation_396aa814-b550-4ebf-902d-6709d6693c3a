/**
 * R1系统 - 模块专用样式文件
 * 基于：R1前端系统架构标准文档.md 模块设计标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md UI设计规范
 *
 * 模块样式规范：
 * - 每个模块使用独立的CSS类前缀
 * - 统一的视觉风格和交互体验
 * - 响应式设计，适配不同屏幕
 * - 高性能，避免重绘和重排
 */

/* ==================== SignalManager 信号管理模块 ==================== */

.signal-manager {
  width: 100%;
}

.signal-toolbar {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.search-input {
  background: none;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-size: 14px;
  width: 200px;
}

.search-input:focus {
  outline: none;
}

.search-btn {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  color: var(--text-secondary);
  cursor: pointer;
  transition: color 0.2s ease;
}

.search-btn:hover {
  color: var(--text-primary);
}

.filter-select {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-size: 14px;
}

.signal-categories {
  margin-bottom: var(--spacing-lg);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.category-tag {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-tag:hover,
.category-tag.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.signal-list-container {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.signal-list {
  min-height: 400px;
}

.signal-list.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.signal-list.list-view {
  display: flex;
  flex-direction: column;
}

.signal-item {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.signal-list.list-view .signal-item {
  border-radius: 0;
  border-left: none;
  border-right: none;
  border-top: none;
  margin: 0;
}

.signal-list.list-view .signal-item:first-child {
  border-top: 1px solid var(--border-color);
  border-top-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
}

.signal-list.list-view .signal-item:last-child {
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}

.signal-item:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.signal-item.selected {
  border-color: var(--primary-color);
  background-color: rgba(0, 123, 255, 0.1);
}

.signal-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.signal-checkbox {
  flex-shrink: 0;
}

.signal-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.signal-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.signal-info {
  flex: 1;
  min-width: 0;
}

.signal-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.signal-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 12px;
  color: var(--text-secondary);
}

.signal-protocol,
.signal-category,
.signal-usage {
  background-color: var(--bg-primary);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.signal-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.action-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-light);
}

.action-btn.favorite-btn.active {
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.action-btn.emit-btn:hover {
  color: var(--success-color);
  border-color: var(--success-color);
}

.action-btn.delete-btn:hover {
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.batch-actions {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-top: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.batch-info {
  font-size: 14px;
  color: var(--text-secondary);
}

.batch-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.pagination {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.pagination-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.pagination-btn.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

.empty-text {
  font-size: 16px;
  margin-bottom: var(--spacing-lg);
}

/* ==================== ControlCenter 控制中心模块 ==================== */

.control-center {
  width: 100%;
}

.control-status {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 14px;
}

.status-label {
  color: var(--text-secondary);
}

.status-value {
  color: var(--text-primary);
  font-weight: 500;
}

.status-value.connected {
  color: var(--success-color);
}

.status-value.disconnected {
  color: var(--danger-color);
}

.quick-control-panel,
.recent-panel,
.batch-control-panel,
.task-monitor-panel {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.panel-header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.panel-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.quick-signals,
.recent-signals {
  padding: var(--spacing-md);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.quick-signal-item,
.recent-signal-item {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.quick-signal-item:hover,
.recent-signal-item:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.quick-signal-item.selected {
  border-color: var(--primary-color);
  background-color: rgba(0, 123, 255, 0.1);
}

.batch-settings {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.batch-settings label {
  font-size: 14px;
  color: var(--text-secondary);
}

.batch-buttons {
  padding: var(--spacing-md);
  display: flex;
  gap: var(--spacing-md);
}

.batch-progress {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
  font-size: 14px;
  color: var(--text-secondary);
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
  width: 0%;
}

.running-tasks-list {
  padding: var(--spacing-md);
}

.task-item {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.task-status {
  font-size: 12px;
  color: var(--text-secondary);
}

.task-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* ==================== TimerManager 定时器管理模块 ==================== */

.timer-manager {
  width: 100%;
}

.timer-status-bar {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.current-time-display {
  text-align: center;
}

.time-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.time-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
}

.date-value {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.timer-stats {
  display: flex;
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.timer-toolbar {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.timer-list-container {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.timer-list {
  min-height: 400px;
}

.timer-item {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.2s ease;
}

.timer-item:last-child {
  border-bottom: none;
}

.timer-item:hover {
  background-color: var(--bg-tertiary);
}

.timer-item.enabled {
  border-left: 4px solid var(--success-color);
}

.timer-item.disabled {
  border-left: 4px solid var(--text-muted);
  opacity: 0.7;
}

.timer-checkbox {
  flex-shrink: 0;
}

.timer-info {
  flex: 1;
  min-width: 0;
}

.timer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.timer-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.timer-status .status-badge {
  font-size: 11px;
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  font-weight: 500;
}

.status-badge.enabled {
  background-color: var(--success-color);
  color: white;
}

.status-badge.disabled {
  background-color: var(--text-muted);
  color: white;
}

.timer-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 12px;
  color: var(--text-secondary);
  flex-wrap: wrap;
}

.timer-time,
.timer-repeat,
.timer-signal,
.timer-next {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--bg-primary);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.timer-description {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  font-style: italic;
}

.timer-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.action-btn.toggle-btn.enabled {
  color: var(--success-color);
  border-color: var(--success-color);
}

.action-btn.toggle-btn.disabled {
  color: var(--text-muted);
  border-color: var(--text-muted);
}

.action-btn.test-btn:hover {
  color: var(--info-color);
  border-color: var(--info-color);
}

.timer-form {
  max-width: 600px;
}

.form-row {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.form-row .form-group {
  flex: 1;
}

.time-input-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.time-input {
  width: 60px;
  text-align: center;
}

.time-separator {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-secondary);
}

.repeat-options {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  font-size: 14px;
  color: var(--text-secondary);
}

.radio-option input[type="radio"] {
  accent-color: var(--primary-color);
}

.weekdays-selector {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.weekday-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  font-size: 13px;
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  transition: all 0.2s ease;
}

.weekday-option:hover {
  background-color: var(--bg-card);
  border-color: var(--border-light);
}

.weekday-option input[type="checkbox"]:checked + span {
  color: var(--primary-color);
  font-weight: 500;
}

.weekday-option input[type="checkbox"] {
  accent-color: var(--primary-color);
}

/* ==================== SystemMonitor 系统监控模块 ==================== */

.system-monitor {
  width: 100%;
}

.monitor-controls {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.controls-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.switch-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 14px;
  color: var(--text-secondary);
  cursor: pointer;
}

.switch-slider {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: 0.2s;
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 1px;
  bottom: 1px;
  background-color: var(--text-secondary);
  transition: 0.2s;
  border-radius: 50%;
}

.switch-label input:checked + .switch-slider {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.switch-label input:checked + .switch-slider:before {
  transform: translateX(20px);
  background-color: white;
}

.controls-right {
  font-size: 12px;
  color: var(--text-secondary);
}

.monitor-panel {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.monitor-panel .panel-header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.monitor-panel .panel-content {
  padding: var(--spacing-md);
}

.status-grid,
.hardware-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.status-item,
.hardware-item {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  text-align: center;
}

.status-label,
.hardware-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.status-value,
.hardware-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.system-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: var(--spacing-sm);
}

.system-status-indicator.healthy {
  background-color: var(--success-color);
}

.system-status-indicator.warning {
  background-color: var(--warning-color);
}

.performance-charts {
  margin-bottom: var(--spacing-lg);
}

.chart-container {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.chart-container h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 14px;
  color: var(--text-primary);
}

.simple-chart {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 100px;
  background-color: var(--bg-primary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  overflow-x: auto;
}

.chart-bar {
  min-width: 8px;
  background: linear-gradient(to top, var(--primary-color), var(--primary-light));
  border-radius: 2px 2px 0 0;
  position: relative;
  transition: all 0.2s ease;
}

.chart-bar:hover {
  background: linear-gradient(to top, var(--primary-hover), var(--primary-color));
}

.bar-value {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: var(--text-secondary);
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chart-bar:hover .bar-value {
  opacity: 1;
}

.no-data {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-lg);
}

.performance-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.log-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  background-color: var(--bg-primary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

.log-entry {
  display: grid;
  grid-template-columns: auto auto auto 1fr;
  gap: var(--spacing-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  margin-bottom: 1px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  align-items: start;
}

.log-entry.error {
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 3px solid var(--danger-color);
}

.log-entry.warn {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 3px solid var(--warning-color);
}

.log-entry.info {
  background-color: rgba(23, 162, 184, 0.1);
  border-left: 3px solid var(--info-color);
}

.log-entry.debug {
  background-color: rgba(108, 117, 125, 0.1);
  border-left: 3px solid var(--text-muted);
}

.log-timestamp {
  color: var(--text-secondary);
  white-space: nowrap;
}

.log-level {
  font-weight: bold;
  white-space: nowrap;
}

.log-level.ERROR {
  color: var(--danger-color);
}

.log-level.WARN {
  color: var(--warning-color);
}

.log-level.INFO {
  color: var(--info-color);
}

.log-level.DEBUG {
  color: var(--text-muted);
}

.log-operation {
  color: var(--text-secondary);
  white-space: nowrap;
}

.log-message {
  color: var(--text-primary);
  word-break: break-word;
}

.no-logs {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-lg);
}

.error-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

/* ==================== OTAManager OTA升级管理模块 ==================== */

.ota-manager {
  width: 100%;
}

.ota-panel {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.version-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.update-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.update-indicator.update-available {
  background-color: var(--warning-color);
  animation: pulse 2s infinite;
}

.update-indicator.up-to-date {
  background-color: var(--success-color);
}

.version-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.version-item {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  text-align: center;
}

.version-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.version-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.version-value.update-available {
  color: var(--warning-color);
}

.version-value.up-to-date {
  color: var(--success-color);
}

.update-controls {
  padding: var(--spacing-md);
}

.updating-notice,
.update-available,
.up-to-date {
  text-align: center;
  padding: var(--spacing-lg);
}

.updating-notice {
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid var(--warning-color);
  border-radius: var(--radius-md);
  color: var(--warning-color);
}

.update-available {
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid var(--warning-color);
  border-radius: var(--radius-md);
}

.up-to-date {
  background-color: rgba(40, 167, 69, 0.1);
  border: 1px solid var(--success-color);
  border-radius: var(--radius-md);
}

.updating-icon,
.update-icon,
.check-icon {
  font-size: 24px;
  margin-bottom: var(--spacing-sm);
  display: block;
}

.updating-text,
.update-text,
.check-text {
  font-size: 16px;
  margin-bottom: var(--spacing-md);
  display: block;
}

.update-actions,
.check-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.update-progress {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.progress-header h4 {
  margin: 0;
  font-size: 16px;
  color: var(--text-primary);
}

.progress-percentage {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

.progress-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--spacing-sm);
  font-size: 12px;
  color: var(--text-secondary);
}

.update-history {
  padding: var(--spacing-md);
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.history-info {
  flex: 1;
}

.history-version {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.history-date {
  font-size: 12px;
  color: var(--text-secondary);
}

.history-status {
  font-size: 12px;
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  font-weight: 500;
}

.history-status.success {
  background-color: var(--success-color);
  color: white;
}

.history-status.failed {
  background-color: var(--danger-color);
  color: white;
}

.ota-settings {
  padding: var(--spacing-md);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 14px;
  color: var(--text-secondary);
  cursor: pointer;
}

.setting-text {
  color: var(--text-primary);
}

.auth-form {
  max-width: 400px;
  margin: 0 auto;
}

.auth-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid var(--warning-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-top: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.warning-icon {
  font-size: 18px;
  color: var(--warning-color);
}

.warning-text {
  font-size: 13px;
  color: var(--text-primary);
}

/* ==================== 响应式设计 ==================== */

@media (max-width: 768px) {
  .signal-toolbar,
  .control-status,
  .timer-status-bar,
  .monitor-controls {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .toolbar-left,
  .toolbar-right,
  .controls-left,
  .controls-right {
    width: 100%;
    justify-content: center;
  }

  .signal-list.grid-view {
    grid-template-columns: 1fr;
  }

  .quick-signals,
  .recent-signals {
    grid-template-columns: 1fr;
  }

  .timer-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .status-grid,
  .hardware-grid,
  .version-grid,
  .performance-stats,
  .error-stats {
    grid-template-columns: 1fr;
  }

  .signal-details,
  .timer-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .update-actions,
  .check-actions {
    flex-direction: column;
    align-items: center;
  }

  .progress-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .signal-item,
  .timer-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .signal-actions,
  .timer-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .batch-actions {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .batch-buttons {
    width: 100%;
    justify-content: center;
  }

  .form-row {
    flex-direction: column;
  }

  .repeat-options,
  .weekdays-selector {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .time-input-group {
    justify-content: center;
  }

  .log-entry {
    grid-template-columns: 1fr;
    gap: var(--spacing-xs);
  }

  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}