<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R1 ESP32-S3 红外控制系统</title>
    
    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/modules.css">
    
    <!-- 性能优化 -->
    <meta name="theme-color" content="#1a1a1a">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="js/config.js" as="script">
    <link rel="preload" href="js/core/EventBus.js" as="script">
    <link rel="preload" href="js/core/BaseModule.js" as="script">
    <link rel="preload" href="js/core/ESP32Communicator.js" as="script">
</head>
<body>
    <!-- 系统加载指示器 -->
    <div id="system-loader" class="system-loader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <div class="loader-text">R1系统初始化中...</div>
            <div class="loader-progress">
                <div class="progress-bar" id="init-progress"></div>
            </div>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="app-container" style="display: none;">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-left">
                <h1 class="app-title">R1 红外控制系统</h1>
                <div class="system-status" id="system-status">
                    <span class="status-indicator" id="esp32-status"></span>
                    <span class="status-text">ESP32连接中...</span>
                </div>
            </div>
            <div class="header-right">
                <button class="header-btn" id="refresh-btn" title="刷新系统">
                    <span class="icon">🔄</span>
                </button>
                <button class="header-btn" id="settings-btn" title="系统设置">
                    <span class="icon">⚙️</span>
                </button>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="app-main">
            <!-- 侧边导航 -->
            <nav class="app-sidebar" id="app-sidebar">
                <div class="sidebar-content">
                    <div class="nav-section">
                        <h3 class="nav-title">信号管理</h3>
                        <button class="nav-item active" data-module="signal-manager">
                            <span class="nav-icon">📡</span>
                            <span class="nav-text">信号管理</span>
                        </button>
                    </div>
                    
                    <div class="nav-section">
                        <h3 class="nav-title">控制中心</h3>
                        <button class="nav-item" data-module="control-center">
                            <span class="nav-icon">🎮</span>
                            <span class="nav-text">控制中心</span>
                        </button>
                        <button class="nav-item" data-module="timer-manager">
                            <span class="nav-icon">⏰</span>
                            <span class="nav-text">定时器</span>
                        </button>
                    </div>
                    
                    <div class="nav-section">
                        <h3 class="nav-title">系统管理</h3>
                        <button class="nav-item" data-module="system-monitor">
                            <span class="nav-icon">📊</span>
                            <span class="nav-text">系统监控</span>
                        </button>
                        <button class="nav-item" data-module="ota-manager">
                            <span class="nav-icon">🔄</span>
                            <span class="nav-text">OTA升级</span>
                        </button>
                    </div>
                </div>
            </nav>

            <!-- 内容区域 -->
            <div class="app-content" id="app-content">
                <!-- 信号管理模块 -->
                <div class="module-container active" id="signal-manager-container">
                    <div class="module-header">
                        <h2 class="module-title">信号管理</h2>
                        <div class="module-actions">
                            <button class="btn btn-primary" id="learn-signal-btn">
                                <span class="icon">📖</span>
                                学习信号
                            </button>
                            <button class="btn btn-secondary" id="import-signals-btn">
                                <span class="icon">📥</span>
                                导入
                            </button>
                            <button class="btn btn-secondary" id="export-signals-btn">
                                <span class="icon">📤</span>
                                导出
                            </button>
                        </div>
                    </div>
                    <div class="module-content" id="signal-manager-content">
                        <!-- 信号管理内容将由SignalManager模块动态生成 -->
                    </div>
                </div>

                <!-- 控制中心模块 -->
                <div class="module-container" id="control-center-container">
                    <div class="module-header">
                        <h2 class="module-title">控制中心</h2>
                        <div class="module-actions">
                            <button class="btn btn-primary" id="quick-control-btn">
                                <span class="icon">⚡</span>
                                快速控制
                            </button>
                        </div>
                    </div>
                    <div class="module-content" id="control-center-content">
                        <!-- 控制中心内容将由ControlCenter模块动态生成 -->
                    </div>
                </div>

                <!-- 定时器管理模块 -->
                <div class="module-container" id="timer-manager-container">
                    <div class="module-header">
                        <h2 class="module-title">定时器管理</h2>
                        <div class="module-actions">
                            <button class="btn btn-primary" id="add-timer-btn">
                                <span class="icon">➕</span>
                                添加定时器
                            </button>
                        </div>
                    </div>
                    <div class="module-content" id="timer-manager-content">
                        <!-- 定时器管理内容将由TimerManager模块动态生成 -->
                    </div>
                </div>

                <!-- 系统监控模块 -->
                <div class="module-container" id="system-monitor-container">
                    <div class="module-header">
                        <h2 class="module-title">系统监控</h2>
                        <div class="module-actions">
                            <button class="btn btn-secondary" id="export-logs-btn">
                                <span class="icon">📋</span>
                                导出日志
                            </button>
                        </div>
                    </div>
                    <div class="module-content" id="system-monitor-content">
                        <!-- 系统监控内容将由SystemMonitor模块动态生成 -->
                    </div>
                </div>

                <!-- OTA升级模块 -->
                <div class="module-container" id="ota-manager-container">
                    <div class="module-header">
                        <h2 class="module-title">OTA升级</h2>
                        <div class="module-actions">
                            <button class="btn btn-warning" id="check-update-btn">
                                <span class="icon">🔍</span>
                                检查更新
                            </button>
                        </div>
                    </div>
                    <div class="module-content" id="ota-manager-content">
                        <!-- OTA升级内容将由OTAManager模块动态生成 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 全局通知容器 -->
    <div id="notification-container" class="notification-container"></div>

    <!-- 模态对话框容器 -->
    <div id="modal-container" class="modal-container"></div>

    <!-- 配置文件 -->
    <script src="js/config.js"></script>

    <!-- 核心脚本 - 按依赖顺序加载 -->
    <script src="js/core/EventBus.js"></script>
    <script src="js/core/ESP32Communicator.js"></script>
    <script src="js/core/BaseModule.js"></script>
    <script src="js/core/R1System.js"></script>
    
    <!-- 模块脚本 -->
    <script src="js/modules/SignalManager.js"></script>
    <script src="js/modules/ControlCenter.js"></script>
    <script src="js/modules/TimerManager.js"></script>
    <script src="js/modules/SystemMonitor.js"></script>
    <script src="js/modules/OTAManager.js"></script>
    
    <!-- 应用启动脚本 -->
    <script src="js/app.js"></script>
</body>
</html>
