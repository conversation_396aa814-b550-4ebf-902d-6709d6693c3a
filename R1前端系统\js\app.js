/**
 * R1系统 - 应用启动脚本
 * 基于：R1前端系统架构标准文档.md 系统启动标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 系统架构
 * 
 * 启动流程：
 * 1. 初始化核心组件
 * 2. 创建模块实例
 * 3. 设置事件监听
 * 4. 启动系统
 * 5. 显示用户界面
 */

(function() {
  'use strict';

  // 应用配置
  const APP_CONFIG = {
    name: 'R1 ESP32-S3 红外控制系统',
    version: '1.0.0',
    esp32: {
      host: window.location.hostname,
      port: window.location.port || 80,
      timeout: 10000,
      retryAttempts: 3,
      retryDelay: 1000
    },
    ui: {
      defaultModule: 'SignalManager',
      animationDuration: 300,
      notificationTimeout: 5000
    },
    performance: {
      maxEventListeners: 100,
      maxLogEntries: 1000,
      gcInterval: 300000 // 5分钟
    }
  };

  // 全局应用状态
  let app = {
    isInitialized: false,
    isReady: false,
    currentModule: null,
    modules: new Map(),
    startTime: Date.now(),
    initProgress: 0
  };

  /**
   * 更新初始化进度
   */
  function updateInitProgress(progress, message) {
    app.initProgress = Math.min(100, Math.max(0, progress));
    
    const progressBar = document.getElementById('init-progress');
    const loaderText = document.querySelector('.loader-text');
    
    if (progressBar) {
      progressBar.style.width = `${app.initProgress}%`;
    }
    
    if (loaderText && message) {
      loaderText.textContent = message;
    }
    
    console.log(`📊 Init Progress: ${app.initProgress}% - ${message}`);
  }

  /**
   * 显示错误信息
   */
  function showError(error, context = 'System') {
    console.error(`❌ ${context} Error:`, error);
    
    const loaderText = document.querySelector('.loader-text');
    if (loaderText) {
      loaderText.textContent = `初始化失败: ${error.message}`;
      loaderText.style.color = 'var(--danger-color)';
    }
    
    // 显示重试按钮
    setTimeout(() => {
      const loaderContent = document.querySelector('.loader-content');
      if (loaderContent && !loaderContent.querySelector('.retry-btn')) {
        const retryBtn = document.createElement('button');
        retryBtn.className = 'btn btn-primary retry-btn';
        retryBtn.textContent = '重试';
        retryBtn.style.marginTop = 'var(--spacing-md)';
        retryBtn.onclick = () => window.location.reload();
        loaderContent.appendChild(retryBtn);
      }
    }, 2000);
  }

  /**
   * 初始化核心组件
   */
  async function initializeCore() {
    updateInitProgress(10, '初始化核心组件...');
    
    try {
      // 检查必需的类是否存在
      if (!window.EventBus) {
        throw new Error('EventBus class not found');
      }
      if (!window.ESP32Communicator) {
        throw new Error('ESP32Communicator class not found');
      }
      if (!window.R1System) {
        throw new Error('R1System class not found');
      }
      
      updateInitProgress(20, '核心组件检查完成');
      return true;
      
    } catch (error) {
      throw new Error(`核心组件初始化失败: ${error.message}`);
    }
  }

  /**
   * 创建系统实例
   */
  async function createSystemInstance() {
    updateInitProgress(30, '创建系统实例...');
    
    try {
      // 创建R1系统实例
      window.r1System = new R1System(APP_CONFIG);
      
      // 监听系统事件
      window.r1System.eventBus.on('system.ready', () => {
        console.log('✅ System ready event received');
        app.isReady = true;
      });
      
      window.r1System.eventBus.on('system.error', (data) => {
        console.error('❌ System error:', data);
        showError(new Error(data.error), 'System');
      });
      
      window.r1System.eventBus.on('module.switch', (data) => {
        switchToModule(data.moduleName);
      });
      
      updateInitProgress(40, '系统实例创建完成');
      return true;
      
    } catch (error) {
      throw new Error(`系统实例创建失败: ${error.message}`);
    }
  }

  /**
   * 初始化系统
   */
  async function initializeSystem() {
    updateInitProgress(50, '初始化系统...');
    
    try {
      await window.r1System.init();
      updateInitProgress(80, '系统初始化完成');
      return true;
      
    } catch (error) {
      throw new Error(`系统初始化失败: ${error.message}`);
    }
  }

  /**
   * 设置用户界面
   */
  async function setupUserInterface() {
    updateInitProgress(90, '设置用户界面...');
    
    try {
      // 隐藏加载器
      const loader = document.getElementById('system-loader');
      if (loader) {
        setTimeout(() => {
          loader.style.opacity = '0';
          setTimeout(() => {
            loader.style.display = 'none';
          }, APP_CONFIG.ui.animationDuration);
        }, 500);
      }
      
      // 设置导航事件
      setupNavigation();
      
      // 设置全局事件监听
      setupGlobalEventListeners();
      
      // 切换到默认模块
      setTimeout(() => {
        switchToModule(APP_CONFIG.ui.defaultModule);
      }, APP_CONFIG.ui.animationDuration + 100);
      
      updateInitProgress(100, '系统启动完成');
      app.isInitialized = true;
      
      // 发布系统就绪事件
      window.r1System.eventBus.emit('system.startup.complete', {
        startTime: app.startTime,
        initTime: Date.now() - app.startTime,
        version: APP_CONFIG.version
      });
      
      console.log(`🚀 R1 System initialized in ${Date.now() - app.startTime}ms`);
      return true;
      
    } catch (error) {
      throw new Error(`用户界面设置失败: ${error.message}`);
    }
  }

  /**
   * 设置导航
   */
  function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
      item.addEventListener('click', (event) => {
        event.preventDefault();
        
        const moduleName = item.dataset.module;
        if (moduleName) {
          switchToModule(moduleName);
        }
      });
    });
  }

  /**
   * 切换模块
   */
  function switchToModule(moduleName) {
    if (!app.isInitialized || !window.r1System) {
      console.warn('⚠️ System not ready for module switch');
      return;
    }
    
    try {
      // 更新导航状态
      const navItems = document.querySelectorAll('.nav-item');
      navItems.forEach(item => {
        if (item.dataset.module === moduleName) {
          item.classList.add('active');
        } else {
          item.classList.remove('active');
        }
      });
      
      // 切换模块容器
      const moduleContainers = document.querySelectorAll('.module-container');
      moduleContainers.forEach(container => {
        if (container.id === `${moduleName.toLowerCase()}-container`) {
          container.classList.add('active');
        } else {
          container.classList.remove('active');
        }
      });
      
      // 激活模块
      if (window.r1System.modules.has(moduleName)) {
        const module = window.r1System.modules.get(moduleName);
        if (module && typeof module.activate === 'function') {
          module.activate();
        }
      }
      
      app.currentModule = moduleName;
      console.log(`📱 Switched to module: ${moduleName}`);
      
    } catch (error) {
      console.error('❌ Module switch error:', error);
      showNotification('模块切换失败', 'error');
    }
  }

  /**
   * 设置全局事件监听
   */
  function setupGlobalEventListeners() {
    // 窗口大小变化
    window.addEventListener('resize', debounce(() => {
      window.r1System.eventBus.emit('window.resize', {
        width: window.innerWidth,
        height: window.innerHeight
      });
    }, 250));
    
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      window.r1System.eventBus.emit('page.visibility.change', {
        hidden: document.hidden
      });
    });
    
    // 网络状态变化
    window.addEventListener('online', () => {
      window.r1System.eventBus.emit('network.online');
    });
    
    window.addEventListener('offline', () => {
      window.r1System.eventBus.emit('network.offline');
    });
    
    // 错误处理
    window.addEventListener('error', (event) => {
      console.error('❌ Global error:', event.error);
      window.r1System.eventBus.emit('system.error', {
        error: event.error.message,
        stack: event.error.stack,
        filename: event.filename,
        lineno: event.lineno
      });
    });
    
    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      console.error('❌ Unhandled promise rejection:', event.reason);
      window.r1System.eventBus.emit('system.error', {
        error: 'Unhandled promise rejection',
        reason: event.reason
      });
    });
  }

  /**
   * 显示通知
   */
  function showNotification(message, type = 'info', duration = APP_CONFIG.ui.notificationTimeout) {
    const container = document.querySelector('.notification-container');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-message">${message}</div>
        <button class="notification-close">×</button>
      </div>
    `;
    
    // 关闭按钮事件
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
      removeNotification(notification);
    });
    
    // 自动关闭
    setTimeout(() => {
      removeNotification(notification);
    }, duration);
    
    container.appendChild(notification);
  }

  /**
   * 移除通知
   */
  function removeNotification(notification) {
    if (notification && notification.parentNode) {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }
  }

  /**
   * 防抖函数
   */
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * 主启动函数
   */
  async function startApplication() {
    console.log('🚀 Starting R1 System...');
    
    try {
      await initializeCore();
      await createSystemInstance();
      await initializeSystem();
      await setupUserInterface();
      
      console.log('✅ R1 System started successfully');
      
    } catch (error) {
      console.error('❌ Application startup failed:', error);
      showError(error, 'Startup');
    }
  }

  // 等待DOM加载完成后启动应用
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startApplication);
  } else {
    startApplication();
  }

  // 导出全局函数供调试使用
  window.R1App = {
    config: APP_CONFIG,
    state: app,
    switchModule: switchToModule,
    showNotification: showNotification
  };

})();
