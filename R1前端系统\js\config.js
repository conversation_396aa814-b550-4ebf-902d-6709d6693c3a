/**
 * R1系统 - 配置文件
 * 基于：R1前端系统架构标准文档.md 配置标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 系统配置
 * 
 * 配置说明：
 * - 系统级配置参数
 * - 模块默认配置
 * - API端点定义
 * - 常量定义
 */

window.R1Config = {
  // 系统信息
  system: {
    name: 'R1 ESP32-S3 红外控制系统',
    version: '1.0.0',
    buildDate: '2025-06-30',
    author: 'R1 Development Team',
    description: '基于ESP32-S3的智能红外控制系统'
  },

  // ESP32通信配置
  esp32: {
    // 连接配置
    connection: {
      host: window.location.hostname || '*************',
      port: window.location.port || 80,
      protocol: window.location.protocol || 'http:',
      timeout: 10000,
      retryAttempts: 3,
      retryDelay: 1000,
      maxRetryDelay: 10000,
      retryBackoffFactor: 2
    },
    
    // API端点
    endpoints: {
      // 信号管理API
      signals: {
        list: '/api/signals',
        create: '/api/signals',
        get: '/api/signals/{id}',
        update: '/api/signals/{id}',
        delete: '/api/signals/{id}',
        emit: '/api/signals/{id}/emit',
        learn: '/api/signals/learn',
        import: '/api/signals/import',
        export: '/api/signals/export'
      },
      
      // 任务管理API
      tasks: {
        list: '/api/tasks',
        create: '/api/tasks',
        get: '/api/tasks/{id}',
        update: '/api/tasks/{id}',
        delete: '/api/tasks/{id}',
        start: '/api/tasks/{id}/start',
        stop: '/api/tasks/{id}/stop',
        pause: '/api/tasks/{id}/pause',
        resume: '/api/tasks/{id}/resume'
      },
      
      // 定时器管理API
      timers: {
        list: '/api/timers',
        create: '/api/timers',
        get: '/api/timers/{id}',
        update: '/api/timers/{id}',
        delete: '/api/timers/{id}',
        toggle: '/api/timers/{id}/toggle'
      },
      
      // 系统管理API
      system: {
        status: '/api/system/status',
        performance: '/api/system/performance',
        hardware: '/api/system/hardware',
        reset: '/api/system/reset',
        logs: '/api/system/logs'
      },
      
      // OTA管理API
      ota: {
        status: '/api/ota/status',
        login: '/api/ota/login',
        firmware: '/api/ota/firmware',
        filesystem: '/api/ota/filesystem'
      },
      
      // 配置管理API
      config: {
        get: '/api/config',
        update: '/api/config',
        export: '/api/config/export',
        import: '/api/config/import',
        reset: '/api/config/reset',
        quickAccess: '/api/config/quick-access',
        recentSignals: '/api/config/recent-signals'
      }
    }
  },

  // UI配置
  ui: {
    // 默认设置
    defaults: {
      theme: 'dark',
      language: 'zh-CN',
      defaultModule: 'SignalManager',
      animationDuration: 300,
      notificationTimeout: 5000,
      autoSave: true,
      autoSaveInterval: 30000
    },
    
    // 分页配置
    pagination: {
      defaultPageSize: 20,
      pageSizeOptions: [10, 20, 50, 100],
      maxPages: 100
    },
    
    // 搜索配置
    search: {
      debounceDelay: 300,
      minSearchLength: 2,
      maxResults: 100
    },
    
    // 模块配置
    modules: {
      SignalManager: {
        defaultView: 'grid',
        itemsPerPage: 20,
        enableBatchMode: true,
        enableCategories: true,
        enableFavorites: true
      },
      
      ControlCenter: {
        quickAccessLimit: 12,
        recentLimit: 10,
        enableBatchControl: true,
        batchDelay: 1000
      },
      
      TimerManager: {
        timeFormat: '24h',
        showSeconds: false,
        autoRefresh: true,
        refreshInterval: 30000
      },
      
      SystemMonitor: {
        autoRefresh: true,
        refreshInterval: 5000,
        maxLogEntries: 1000,
        maxHistoryPoints: 50
      },
      
      OTAManager: {
        autoCheck: true,
        checkInterval: 3600000,
        requireAuth: true,
        backupBeforeUpdate: true
      }
    }
  },

  // 性能配置
  performance: {
    // 事件系统
    events: {
      maxListeners: 100,
      maxEventHistory: 1000,
      enablePerformanceTracking: true
    },
    
    // 内存管理
    memory: {
      gcInterval: 300000, // 5分钟
      maxCacheSize: 50 * 1024 * 1024, // 50MB
      enableMemoryMonitoring: true
    },
    
    // 渲染优化
    rendering: {
      enableVirtualScrolling: true,
      renderBatchSize: 50,
      renderDelay: 16, // 60fps
      enableRenderCache: true
    }
  },

  // 安全配置
  security: {
    // 认证配置
    auth: {
      sessionTimeout: 3600000, // 1小时
      maxLoginAttempts: 5,
      lockoutDuration: 300000, // 5分钟
      requireStrongPassword: true
    },
    
    // 数据验证
    validation: {
      maxSignalNameLength: 50,
      maxDescriptionLength: 200,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowedFileTypes: ['.json', '.txt', '.csv']
    }
  },

  // 调试配置
  debug: {
    enabled: false, // 生产环境设为false
    logLevel: 'info', // 'debug', 'info', 'warn', 'error'
    enableConsoleOutput: true,
    enablePerformanceLogging: false,
    enableEventLogging: false
  },

  // 常量定义
  constants: {
    // 红外协议
    IR_PROTOCOLS: {
      UNKNOWN: 0,
      NEC: 1,
      SONY: 2,
      LG: 3,
      SAMSUNG: 4,
      PANASONIC: 5,
      JVC: 6,
      RC5: 7,
      RC6: 8,
      RAW: 255
    },
    
    // 设备类型
    DEVICE_TYPES: {
      UNKNOWN: 0,
      TV: 1,
      AC: 2,
      FAN: 3,
      LIGHT: 4,
      AUDIO: 5,
      PROJECTOR: 6,
      OTHER: 255
    },
    
    // 任务状态
    TASK_STATUS: {
      PENDING: 0,
      RUNNING: 1,
      COMPLETED: 2,
      FAILED: 3,
      CANCELLED: 4,
      PAUSED: 5
    },
    
    // 任务类型
    TASK_TYPES: {
      SINGLE_SIGNAL: 0,
      BATCH_SIGNALS: 1,
      SCHEDULED: 2,
      REPEATED: 3,
      CONDITIONAL: 4
    },
    
    // 优先级
    PRIORITY: {
      LOW: 0,
      NORMAL: 1,
      HIGH: 2,
      CRITICAL: 3
    },
    
    // 错误级别
    ERROR_LEVELS: {
      INFO: 'info',
      WARN: 'warn',
      ERROR: 'error',
      FATAL: 'fatal'
    },
    
    // 事件类型
    EVENT_TYPES: {
      // 系统级事件
      SYSTEM_READY: 'system.ready',
      SYSTEM_ERROR: 'system.error',
      SYSTEM_REFRESH: 'system.refresh',
      
      // 模块级事件
      MODULE_READY: 'module.ready',
      MODULE_ERROR: 'module.error',
      MODULE_SUCCESS: 'module.success',
      MODULE_SWITCH: 'module.switch',
      
      // ESP32通信事件
      ESP32_CONNECTED: 'esp32.connected',
      ESP32_DISCONNECTED: 'esp32.disconnected',
      ESP32_ERROR: 'esp32.error',
      ESP32_REQUEST_SUCCESS: 'esp32.request.success',
      ESP32_REQUEST_ERROR: 'esp32.request.error',
      
      // 信号相关事件
      SIGNAL_SELECTED: 'signal.selected',
      SIGNAL_EMITTED: 'signal.emitted',
      SIGNAL_LEARNED: 'signal.learned',
      
      // 定时器相关事件
      TIMER_TRIGGERED: 'timer.triggered',
      TIMER_STATUS_CHANGED: 'timer.status.changed',
      
      // 任务相关事件
      TASK_STARTED: 'task.started',
      TASK_COMPLETED: 'task.completed',
      TASK_FAILED: 'task.failed'
    }
  },

  // 本地化配置
  i18n: {
    defaultLanguage: 'zh-CN',
    supportedLanguages: ['zh-CN', 'en-US'],
    fallbackLanguage: 'zh-CN',
    
    // 文本资源
    texts: {
      'zh-CN': {
        // 通用
        'common.ok': '确定',
        'common.cancel': '取消',
        'common.save': '保存',
        'common.delete': '删除',
        'common.edit': '编辑',
        'common.add': '添加',
        'common.search': '搜索',
        'common.refresh': '刷新',
        'common.loading': '加载中...',
        'common.error': '错误',
        'common.success': '成功',
        'common.warning': '警告',
        'common.info': '信息',
        
        // 模块名称
        'module.signalManager': '信号管理',
        'module.controlCenter': '控制中心',
        'module.timerManager': '定时器管理',
        'module.systemMonitor': '系统监控',
        'module.otaManager': 'OTA升级',
        
        // 状态文本
        'status.connected': '已连接',
        'status.disconnected': '未连接',
        'status.enabled': '已启用',
        'status.disabled': '已禁用',
        'status.running': '运行中',
        'status.stopped': '已停止'
      }
    }
  }
};

// 冻结配置对象，防止意外修改
Object.freeze(window.R1Config);
