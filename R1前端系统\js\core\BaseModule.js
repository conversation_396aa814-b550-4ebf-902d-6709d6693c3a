/**
 * R1系统 - BaseModule统一模块基类
 * 基于：R1前端系统架构标准文档.md 第2节：BaseModule - 统一模块基类
 * 
 * 所有模块必须继承BaseModule，确保统一的生命周期和接口标准
 * 
 * 标准生命周期：
 * - constructor(eventBus, esp32, moduleName) - 构造函数
 * - async init() - 初始化入口
 * - async setupEventListeners() - 事件监听器设置（子类实现）
 * - async setupUI() - UI设置（子类实现）
 * - async loadModuleData() - 数据加载（子类实现）
 * - destroy() - 销毁清理
 */

class BaseModule {
  constructor(eventBus, esp32, moduleName) {
    if (!eventBus || !esp32 || !moduleName) {
      throw new Error('BaseModule: eventBus, esp32, and moduleName are required');
    }
    
    this.eventBus = eventBus;
    this.esp32 = esp32;
    this.moduleName = moduleName;
    
    // 基础状态（所有模块必需）
    this.state = {
      loading: false,           // 加载状态
      error: null,              // 错误信息
      data: new Map()           // 通用数据存储
    };
    
    // 生命周期状态
    this.isInitialized = false; // 是否已初始化
    this.isActive = false;      // 是否激活状态
    
    // 性能统计
    this.performance = {
      initTime: 0,              // 初始化耗时
      operationCount: 0,        // 操作计数
      errorCount: 0,            // 错误计数
      lastOperation: null       // 最后操作
    };
    
    // UI元素缓存
    this.elements = new Map();
    
    // 事件处理器缓存（用于清理）
    this.boundHandlers = new Map();
    
    console.log(`✅ BaseModule '${this.moduleName}' constructed`);
  }

  /**
   * 初始化入口 - 标准生命周期方法
   */
  async init() {
    if (this.isInitialized) {
      console.warn(`⚠️ Module '${this.moduleName}' already initialized`);
      return;
    }
    
    const startTime = performance.now();
    this.state.loading = true;
    this.state.error = null;
    
    try {
      console.log(`🚀 Initializing module '${this.moduleName}'...`);
      
      // 按标准顺序执行初始化步骤
      await this.setupEventListeners();
      await this.setupUI();
      await this.loadModuleData();
      
      // 标记为已初始化
      this.isInitialized = true;
      this.isActive = true;
      this.state.loading = false;
      
      // 记录性能统计
      this.performance.initTime = performance.now() - startTime;
      
      console.log(`✅ Module '${this.moduleName}' initialized in ${this.performance.initTime.toFixed(2)}ms`);
      
      // 发布初始化完成事件
      this.emitEvent('module.initialized', {
        moduleName: this.moduleName,
        initTime: this.performance.initTime
      });
      
    } catch (error) {
      this.state.loading = false;
      this.handleError(error, 'Module initialization');
      throw error;
    }
  }

  /**
   * 事件监听器设置 - 子类必须实现
   */
  async setupEventListeners() {
    // 基础系统事件监听
    this.eventBus.on('system.refresh', () => {
      this.refresh();
    });
    
    this.eventBus.on('system.emergency.stop', () => {
      this.emergencyStop();
    });
    
    // 子类应该重写此方法添加特定的事件监听器
    console.log(`📡 BaseModule '${this.moduleName}': Basic event listeners setup`);
  }

  /**
   * UI设置 - 子类必须实现
   */
  async setupUI() {
    // 子类应该重写此方法设置UI
    console.log(`🎨 BaseModule '${this.moduleName}': UI setup (override in subclass)`);
  }

  /**
   * 数据加载 - 子类必须实现
   */
  async loadModuleData() {
    // 子类应该重写此方法加载数据
    console.log(`📊 BaseModule '${this.moduleName}': Data loading (override in subclass)`);
  }

  /**
   * 发布事件 - 自动添加source字段
   * @param {string} eventType - 事件类型
   * @param {*} data - 事件数据
   */
  emitEvent(eventType, data = null) {
    // 自动添加source字段（如果数据中没有）
    if (data && typeof data === 'object' && !data.source) {
      data.source = this.moduleName;
    }
    
    // 直接发送原始数据，不包装
    this.eventBus.emit(eventType, data);
  }

  /**
   * ESP32请求 - 统一的ESP32通信接口
   * @param {string} endpoint - API端点
   * @param {object} options - 请求选项
   * @returns {Promise<object>} 响应数据
   */
  async requestESP32(endpoint, options = {}) {
    this.performance.operationCount++;
    this.performance.lastOperation = `ESP32 Request: ${endpoint}`;
    
    try {
      const response = await this.esp32.request(endpoint, options);
      
      if (response.success) {
        return response;
      } else {
        throw new Error(response.error || 'ESP32 request failed');
      }
    } catch (error) {
      this.performance.errorCount++;
      this.handleError(error, `ESP32 Request: ${endpoint}`);
      throw error;
    }
  }

  /**
   * 错误处理 - 统一的错误处理机制
   * @param {Error} error - 错误对象
   * @param {string} operation - 操作描述
   */
  handleError(error, operation = 'Unknown operation') {
    this.state.error = {
      message: error.message,
      operation,
      timestamp: Date.now(),
      moduleName: this.moduleName
    };
    
    this.performance.errorCount++;
    
    console.error(`❌ ${this.moduleName} Error in ${operation}:`, error);
    
    // 发布错误事件
    this.emitEvent('module.error', {
      moduleName: this.moduleName,
      error: error.message,
      operation,
      timestamp: Date.now()
    });
    
    // 发布全局错误事件
    this.eventBus.emit('system.error', {
      source: this.moduleName,
      error: error.message,
      operation,
      level: 'ERROR',
      timestamp: Date.now()
    });
  }

  /**
   * 成功处理 - 统一的成功处理机制
   * @param {string} message - 成功消息
   * @param {string} operation - 操作描述
   * @param {*} data - 相关数据
   */
  handleSuccess(message, operation = 'Unknown operation', data = null) {
    console.log(`✅ ${this.moduleName} Success in ${operation}: ${message}`);
    
    // 清除错误状态
    this.state.error = null;
    
    // 发布成功事件
    this.emitEvent('module.success', {
      moduleName: this.moduleName,
      message,
      operation,
      data,
      timestamp: Date.now()
    });
    
    // 发布全局成功事件
    this.eventBus.emit('system.success', {
      source: this.moduleName,
      message,
      operation,
      level: 'INFO',
      timestamp: Date.now()
    });
  }

  /**
   * 获取模块状态
   * @returns {object} 模块状态信息
   */
  getStatus() {
    return {
      moduleName: this.moduleName,
      isInitialized: this.isInitialized,
      isActive: this.isActive,
      state: { ...this.state },
      performance: { ...this.performance },
      elementsCount: this.elements.size,
      handlersCount: this.boundHandlers.size
    };
  }

  /**
   * 紧急停止 - 系统控制方法
   */
  emergencyStop() {
    console.warn(`🛑 Emergency stop triggered for module '${this.moduleName}'`);
    
    this.isActive = false;
    this.state.loading = false;
    
    // 发布紧急停止事件
    this.emitEvent('module.emergency.stopped', {
      moduleName: this.moduleName,
      timestamp: Date.now()
    });
  }

  /**
   * 刷新数据 - 系统控制方法
   */
  async refresh() {
    if (!this.isInitialized) {
      console.warn(`⚠️ Cannot refresh uninitialized module '${this.moduleName}'`);
      return;
    }
    
    console.log(`🔄 Refreshing module '${this.moduleName}'...`);
    
    try {
      this.state.loading = true;
      await this.loadModuleData();
      this.state.loading = false;
      
      this.handleSuccess('Module refreshed successfully', 'Refresh');
    } catch (error) {
      this.state.loading = false;
      this.handleError(error, 'Refresh');
    }
  }

  /**
   * 缓存UI元素
   * @param {string} key - 元素键名
   * @param {HTMLElement|string} element - DOM元素或选择器
   */
  cacheElement(key, element) {
    if (typeof element === 'string') {
      element = document.querySelector(element);
    }
    
    if (element) {
      this.elements.set(key, element);
    } else {
      console.warn(`⚠️ Element not found for key '${key}' in module '${this.moduleName}'`);
    }
  }

  /**
   * 获取缓存的UI元素
   * @param {string} key - 元素键名
   * @returns {HTMLElement|null} DOM元素
   */
  getElement(key) {
    return this.elements.get(key) || null;
  }

  /**
   * 绑定事件处理器（用于清理）
   * @param {HTMLElement} element - DOM元素
   * @param {string} eventType - 事件类型
   * @param {function} handler - 事件处理器
   * @param {object} options - 事件选项
   */
  bindEventHandler(element, eventType, handler, options = {}) {
    const boundHandler = handler.bind(this);
    element.addEventListener(eventType, boundHandler, options);
    
    // 缓存绑定信息用于清理
    const key = `${element.tagName}_${eventType}_${Date.now()}`;
    this.boundHandlers.set(key, {
      element,
      eventType,
      handler: boundHandler,
      options
    });
  }

  /**
   * 销毁模块 - 清理资源
   */
  destroy() {
    console.log(`🧹 Destroying module '${this.moduleName}'...`);
    
    // 标记为非活跃状态
    this.isActive = false;
    
    // 清理事件处理器
    for (const [key, handlerInfo] of this.boundHandlers) {
      handlerInfo.element.removeEventListener(
        handlerInfo.eventType,
        handlerInfo.handler,
        handlerInfo.options
      );
    }
    this.boundHandlers.clear();
    
    // 清理UI元素缓存
    this.elements.clear();
    
    // 清理状态
    this.state.data.clear();
    this.state.error = null;
    
    // 发布销毁事件
    this.emitEvent('module.destroyed', {
      moduleName: this.moduleName,
      timestamp: Date.now()
    });
    
    console.log(`✅ Module '${this.moduleName}' destroyed`);
  }
}

// 导出BaseModule类
window.BaseModule = BaseModule;
