/**
 * R1系统 - ESP32Communicator硬件通信层
 * 基于：R1前端系统架构标准文档.md 第3节：ESP32Communicator - 硬件通信层
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md API接口规范
 * 
 * 核心功能：
 * - 连接管理 - 自动重连、超时处理、优雅降级
 * - 批量请求 - 减少通信开销，提高效率
 * - 错误恢复 - 指数退避重连机制
 * - 性能优化 - 请求队列和批量处理
 */

class ESP32Communicator {
  constructor(eventBus) {
    this.eventBus = eventBus;
    
    // 连接配置
    this.config = {
      baseUrl: window.location.origin,
      timeout: 10000,
      retryAttempts: 3,
      retryDelay: 1000,
      maxRetryDelay: 30000,
      batchSize: 10,
      batchDelay: 100
    };
    
    // 连接状态
    this.connectionState = {
      isConnected: false,
      isConnecting: false,
      lastConnectTime: 0,
      failedAttempts: 0,
      connectionQuality: 'unknown'
    };
    
    // 请求队列和批量处理
    this.requestQueue = [];
    this.batchTimer = null;
    this.activeRequests = new Map();
    
    // 性能统计
    this.performance = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      lastRequestTime: 0,
      connectionUptime: 0
    };
    
    // WebSocket连接（用于实时通信）
    this.websocket = null;
    this.wsReconnectTimer = null;
    
    console.log('✅ ESP32Communicator initialized');
    this.initializeConnection();
  }

  /**
   * 初始化连接
   */
  async initializeConnection() {
    console.log('🔌 ESP32Communicator: Initializing connection...');
    
    try {
      // 检查ESP32连接状态
      await this.checkConnection();
      
      // 初始化WebSocket连接
      this.initWebSocket();
      
      // 启动连接监控
      this.startConnectionMonitoring();
      
    } catch (error) {
      console.error('❌ ESP32Communicator: Failed to initialize connection:', error);
      this.handleConnectionError(error);
    }
  }

  /**
   * 检查ESP32连接状态
   */
  async checkConnection() {
    try {
      const response = await this.request('/api/system/status', {
        method: 'GET',
        timeout: 5000
      });
      
      if (response.success) {
        this.connectionState.isConnected = true;
        this.connectionState.lastConnectTime = Date.now();
        this.connectionState.failedAttempts = 0;
        this.connectionState.connectionQuality = 'good';
        
        this.eventBus.emit('esp32.connected', {
          status: response.data,
          connectionTime: Date.now()
        });
        
        console.log('✅ ESP32 connection established');
        return true;
      }
    } catch (error) {
      this.connectionState.isConnected = false;
      this.connectionState.failedAttempts++;
      
      console.warn('⚠️ ESP32 connection check failed:', error.message);
      return false;
    }
  }

  /**
   * 发送HTTP请求到ESP32
   * @param {string} endpoint - API端点
   * @param {object} options - 请求选项
   * @returns {Promise<object>} 响应数据
   */
  async request(endpoint, options = {}) {
    const startTime = performance.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 默认选项
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId
      },
      timeout: this.config.timeout
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    // 构建完整URL
    const url = `${this.config.baseUrl}${endpoint}`;
    
    // 更新性能统计
    this.performance.totalRequests++;
    this.performance.lastRequestTime = Date.now();
    
    try {
      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), finalOptions.timeout);
      
      // 记录活跃请求
      this.activeRequests.set(requestId, {
        url,
        startTime,
        controller
      });
      
      // 发送请求
      const response = await fetch(url, {
        ...finalOptions,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // 解析响应
      let responseData;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }
      
      // 计算响应时间
      const responseTime = performance.now() - startTime;
      this.updatePerformanceStats(responseTime, true);
      
      // 移除活跃请求记录
      this.activeRequests.delete(requestId);
      
      if (response.ok) {
        // 成功响应
        this.performance.successfulRequests++;
        
        // 更新连接状态
        if (!this.connectionState.isConnected) {
          this.connectionState.isConnected = true;
          this.connectionState.failedAttempts = 0;
          this.eventBus.emit('esp32.reconnected', { timestamp: Date.now() });
        }
        
        return {
          success: true,
          data: responseData,
          responseTime,
          timestamp: Date.now()
        };
      } else {
        // HTTP错误响应
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
    } catch (error) {
      // 请求失败处理
      const responseTime = performance.now() - startTime;
      this.updatePerformanceStats(responseTime, false);
      this.performance.failedRequests++;
      
      // 移除活跃请求记录
      this.activeRequests.delete(requestId);
      
      // 处理不同类型的错误
      if (error.name === 'AbortError') {
        throw new Error(`Request timeout after ${finalOptions.timeout}ms`);
      } else if (error instanceof TypeError) {
        // 网络错误
        this.handleConnectionError(error);
        throw new Error('Network error: Unable to connect to ESP32');
      } else {
        throw error;
      }
    }
  }

  /**
   * 批量请求处理
   * @param {Array} requests - 请求数组
   * @returns {Promise<Array>} 响应数组
   */
  async batchRequest(requests) {
    console.log(`📦 ESP32Communicator: Processing batch of ${requests.length} requests`);
    
    const batchStartTime = performance.now();
    const results = [];
    
    // 分批处理请求
    for (let i = 0; i < requests.length; i += this.config.batchSize) {
      const batch = requests.slice(i, i + this.config.batchSize);
      
      // 并行执行当前批次
      const batchPromises = batch.map(async (req) => {
        try {
          return await this.request(req.endpoint, req.options);
        } catch (error) {
          return {
            success: false,
            error: error.message,
            timestamp: Date.now()
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // 批次间延迟
      if (i + this.config.batchSize < requests.length) {
        await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
      }
    }
    
    const batchTime = performance.now() - batchStartTime;
    console.log(`✅ Batch completed in ${batchTime.toFixed(2)}ms`);
    
    return results;
  }

  /**
   * 初始化WebSocket连接
   */
  initWebSocket() {
    try {
      const wsUrl = `ws://${window.location.host}/ws`;
      this.websocket = new WebSocket(wsUrl);
      
      this.websocket.onopen = () => {
        console.log('✅ WebSocket connected');
        this.eventBus.emit('esp32.websocket.connected', { timestamp: Date.now() });
      };
      
      this.websocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.eventBus.emit('esp32.websocket.message', data);
        } catch (error) {
          console.error('❌ WebSocket message parse error:', error);
        }
      };
      
      this.websocket.onclose = () => {
        console.warn('⚠️ WebSocket disconnected');
        this.eventBus.emit('esp32.websocket.disconnected', { timestamp: Date.now() });
        this.scheduleWebSocketReconnect();
      };
      
      this.websocket.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        this.eventBus.emit('esp32.websocket.error', { error: error.message });
      };
      
    } catch (error) {
      console.error('❌ Failed to initialize WebSocket:', error);
    }
  }

  /**
   * 处理连接错误
   * @param {Error} error - 错误对象
   */
  handleConnectionError(error) {
    this.connectionState.isConnected = false;
    this.connectionState.failedAttempts++;
    
    // 指数退避重连机制
    const delay = Math.min(
      this.config.retryDelay * Math.pow(2, this.connectionState.failedAttempts - 1),
      this.config.maxRetryDelay
    );
    
    console.warn(`⚠️ Connection error, retrying in ${delay}ms (attempt ${this.connectionState.failedAttempts})`);
    
    this.eventBus.emit('esp32.connection.error', {
      error: error.message,
      failedAttempts: this.connectionState.failedAttempts,
      nextRetryIn: delay
    });
    
    // 安排重连
    setTimeout(() => {
      if (this.connectionState.failedAttempts <= this.config.retryAttempts) {
        this.checkConnection();
      }
    }, delay);
  }

  /**
   * 安排WebSocket重连
   */
  scheduleWebSocketReconnect() {
    if (this.wsReconnectTimer) {
      clearTimeout(this.wsReconnectTimer);
    }
    
    this.wsReconnectTimer = setTimeout(() => {
      if (this.connectionState.isConnected) {
        this.initWebSocket();
      }
    }, 5000);
  }

  /**
   * 启动连接监控
   */
  startConnectionMonitoring() {
    // 每30秒检查一次连接状态
    setInterval(() => {
      if (this.connectionState.isConnected) {
        this.checkConnection();
      }
    }, 30000);
  }

  /**
   * 更新性能统计
   * @param {number} responseTime - 响应时间
   * @param {boolean} success - 是否成功
   */
  updatePerformanceStats(responseTime, success) {
    if (success) {
      this.performance.averageResponseTime = 
        (this.performance.averageResponseTime * (this.performance.successfulRequests - 1) + responseTime) / 
        this.performance.successfulRequests;
    }
  }

  /**
   * 获取连接状态
   * @returns {object} 连接状态信息
   */
  getConnectionState() {
    return {
      ...this.connectionState,
      performance: this.performance,
      activeRequests: this.activeRequests.size,
      websocketState: this.websocket ? this.websocket.readyState : -1
    };
  }

  /**
   * 销毁通信器
   */
  destroy() {
    // 取消所有活跃请求
    for (const [requestId, request] of this.activeRequests) {
      request.controller.abort();
    }
    this.activeRequests.clear();
    
    // 关闭WebSocket
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    
    // 清理定时器
    if (this.wsReconnectTimer) {
      clearTimeout(this.wsReconnectTimer);
    }
    
    console.log('🧹 ESP32Communicator destroyed');
  }
}

// 导出ESP32Communicator类
window.ESP32Communicator = ESP32Communicator;
