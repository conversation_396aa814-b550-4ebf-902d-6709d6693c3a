/**
 * R1系统 - EventBus事件驱动核心
 * 基于：R1前端系统架构标准文档.md 第1节：EventBus - 事件驱动核心
 * 
 * 核心特性：
 * - Map存储优化 - O(1)查找效率，适合ESP32S3内存特性
 * - 异步事件处理 - 充分利用双核架构
 * - 优先级支持 - 关键事件优先处理
 * - 性能统计 - 内置轻量级性能监控
 */

class EventBus {
  constructor() {
    // Map存储优化 - O(1)查找效率
    this.listeners = new Map();
    
    // 性能统计
    this.performance = {
      totalEvents: 0,
      totalHandlers: 0,
      errorCount: 0,
      averageHandleTime: 0,
      lastEventTime: 0,
      eventHistory: []
    };
    
    // 优先级队列
    this.priorityQueue = [];
    this.isProcessing = false;
    
    // 调试模式
    this.debugMode = false;
    
    console.log('✅ EventBus initialized with Map storage optimization');
  }

  /**
   * 监听事件
   * @param {string} event - 事件名称
   * @param {function} handler - 事件处理器
   * @param {object} options - 选项 {priority: number, once: boolean}
   */
  on(event, handler, options = {}) {
    if (typeof event !== 'string' || typeof handler !== 'function') {
      console.error('❌ EventBus.on: Invalid parameters');
      return;
    }

    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }

    const listenerInfo = {
      handler,
      priority: options.priority || 0,
      once: options.once || false,
      addedAt: Date.now()
    };

    this.listeners.get(event).push(listenerInfo);
    this.performance.totalHandlers++;

    if (this.debugMode) {
      console.log(`📝 EventBus: Added listener for '${event}', total handlers: ${this.performance.totalHandlers}`);
    }
  }

  /**
   * 一次性监听事件
   * @param {string} event - 事件名称
   * @param {function} handler - 事件处理器
   */
  once(event, handler) {
    this.on(event, handler, { once: true });
  }

  /**
   * 取消监听事件
   * @param {string} event - 事件名称
   * @param {function} handler - 事件处理器
   */
  off(event, handler) {
    if (!this.listeners.has(event)) {
      return;
    }

    const listeners = this.listeners.get(event);
    const index = listeners.findIndex(listener => listener.handler === handler);
    
    if (index !== -1) {
      listeners.splice(index, 1);
      this.performance.totalHandlers--;
      
      if (listeners.length === 0) {
        this.listeners.delete(event);
      }
      
      if (this.debugMode) {
        console.log(`🗑️ EventBus: Removed listener for '${event}', total handlers: ${this.performance.totalHandlers}`);
      }
    }
  }

  /**
   * 发布事件 - 异步事件处理，充分利用双核架构
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  async emit(event, data = null) {
    if (typeof event !== 'string') {
      console.error('❌ EventBus.emit: Invalid event name');
      return;
    }

    const startTime = performance.now();
    this.performance.totalEvents++;
    this.performance.lastEventTime = Date.now();

    // 记录事件历史（保留最近100个事件）
    this.performance.eventHistory.push({
      event,
      timestamp: Date.now(),
      dataSize: data ? JSON.stringify(data).length : 0
    });
    
    if (this.performance.eventHistory.length > 100) {
      this.performance.eventHistory.shift();
    }

    if (this.debugMode) {
      console.log(`📡 EventBus: Emitting '${event}'`, data);
    }

    if (!this.listeners.has(event)) {
      if (this.debugMode) {
        console.log(`⚠️ EventBus: No listeners for '${event}'`);
      }
      return;
    }

    const listeners = this.listeners.get(event);
    const toRemove = [];

    // 按优先级排序处理器
    const sortedListeners = [...listeners].sort((a, b) => b.priority - a.priority);

    // 异步处理所有监听器
    const promises = sortedListeners.map(async (listenerInfo, index) => {
      try {
        // 异步执行处理器
        await Promise.resolve(listenerInfo.handler(data));
        
        // 标记一次性监听器待删除
        if (listenerInfo.once) {
          toRemove.push(listenerInfo);
        }
      } catch (error) {
        this.performance.errorCount++;
        console.error(`❌ Event handler error for '${event}':`, error);
        
        // 发布错误事件
        if (event !== 'system.error') {
          setTimeout(() => {
            this.emit('system.error', {
              source: 'EventBus',
              event,
              error: error.message,
              timestamp: Date.now()
            });
          }, 0);
        }
      }
    });

    // 等待所有处理器完成
    await Promise.all(promises);

    // 移除一次性监听器
    toRemove.forEach(listenerInfo => {
      const index = listeners.indexOf(listenerInfo);
      if (index !== -1) {
        listeners.splice(index, 1);
        this.performance.totalHandlers--;
      }
    });

    // 如果没有监听器了，删除事件
    if (listeners.length === 0) {
      this.listeners.delete(event);
    }

    // 更新性能统计
    const handleTime = performance.now() - startTime;
    this.performance.averageHandleTime = 
      (this.performance.averageHandleTime * (this.performance.totalEvents - 1) + handleTime) / 
      this.performance.totalEvents;

    if (this.debugMode) {
      console.log(`✅ EventBus: '${event}' handled in ${handleTime.toFixed(2)}ms`);
    }
  }

  /**
   * 清空所有监听器
   */
  clear() {
    const totalHandlers = this.performance.totalHandlers;
    this.listeners.clear();
    this.performance.totalHandlers = 0;
    
    console.log(`🧹 EventBus: Cleared all listeners (${totalHandlers} handlers removed)`);
  }

  /**
   * 获取性能统计
   * @returns {object} 性能统计数据
   */
  getPerformance() {
    return {
      ...this.performance,
      activeEvents: this.listeners.size,
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * 获取内存使用情况
   * @returns {object} 内存使用统计
   */
  getMemoryUsage() {
    let totalListeners = 0;
    let totalEvents = this.listeners.size;
    
    for (const [event, listeners] of this.listeners) {
      totalListeners += listeners.length;
    }
    
    return {
      totalEvents,
      totalListeners,
      estimatedMemoryKB: Math.round((totalEvents * 50 + totalListeners * 100) / 1024)
    };
  }

  /**
   * 启用/禁用调试模式
   * @param {boolean} enabled - 是否启用调试
   */
  setDebugMode(enabled) {
    this.debugMode = enabled;
    console.log(`🔧 EventBus: Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * 获取事件统计信息
   * @returns {object} 事件统计
   */
  getEventStats() {
    const stats = new Map();
    
    for (const [event, listeners] of this.listeners) {
      stats.set(event, {
        listenerCount: listeners.length,
        priorities: listeners.map(l => l.priority),
        hasOnceListeners: listeners.some(l => l.once)
      });
    }
    
    return Object.fromEntries(stats);
  }
}

// 导出EventBus类
window.EventBus = EventBus;
