/**
 * R1系统 - 核心系统管理器
 * 基于：R1前端系统架构标准文档.md 事件驱动架构
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 系统设计原则
 * 
 * 核心功能：
 * - 系统初始化和生命周期管理
 * - 模块注册和管理
 * - 事件总线协调
 * - ESP32通信管理
 * - 全局状态管理
 */

class R1System {
  constructor() {
    // 核心组件
    this.eventBus = null;
    this.esp32 = null;
    this.modules = new Map();
    
    // 系统状态
    this.systemState = {
      isInitialized: false,
      isReady: false,
      startTime: 0,
      initProgress: 0,
      currentModule: null,
      esp32Connected: false
    };
    
    // 系统配置
    this.config = {
      initTimeout: 30000,        // 初始化超时时间
      moduleTimeout: 10000,      // 模块初始化超时
      esp32Timeout: 15000,       // ESP32连接超时
      debugMode: false,          // 调试模式
      performanceMonitoring: true // 性能监控
    };
    
    // 性能统计
    this.performance = {
      systemInitTime: 0,
      moduleInitTimes: new Map(),
      totalModules: 0,
      successfulModules: 0,
      failedModules: 0,
      memoryUsage: 0
    };
    
    // 错误收集
    this.errors = [];
    
    console.log('🚀 R1System constructor initialized');
  }

  /**
   * 系统初始化 - 主入口
   */
  async init() {
    if (this.systemState.isInitialized) {
      console.warn('⚠️ R1System already initialized');
      return;
    }
    
    const startTime = performance.now();
    this.systemState.startTime = Date.now();
    
    try {
      console.log('🚀 R1System: Starting system initialization...');
      this.updateInitProgress(10, '初始化核心组件...');
      
      // 1. 初始化核心组件
      await this.initCoreComponents();
      this.updateInitProgress(30, '建立ESP32连接...');
      
      // 2. 建立ESP32连接
      await this.initESP32Connection();
      this.updateInitProgress(50, '注册系统模块...');
      
      // 3. 注册系统模块
      await this.registerSystemModules();
      this.updateInitProgress(70, '初始化模块...');
      
      // 4. 初始化所有模块
      await this.initializeModules();
      this.updateInitProgress(90, '设置系统监听器...');
      
      // 5. 设置系统级事件监听器
      this.setupSystemEventListeners();
      this.updateInitProgress(100, '系统初始化完成');
      
      // 标记系统为已初始化
      this.systemState.isInitialized = true;
      this.systemState.isReady = true;
      this.performance.systemInitTime = performance.now() - startTime;
      
      console.log(`✅ R1System initialized successfully in ${this.performance.systemInitTime.toFixed(2)}ms`);
      
      // 发布系统就绪事件
      this.eventBus.emit('system.ready', {
        initTime: this.performance.systemInitTime,
        modulesCount: this.modules.size,
        timestamp: Date.now()
      });
      
      // 隐藏加载器，显示应用
      this.showApplication();
      
    } catch (error) {
      this.handleSystemError(error, 'System initialization');
      throw error;
    }
  }

  /**
   * 初始化核心组件
   */
  async initCoreComponents() {
    console.log('🔧 Initializing core components...');
    
    // 初始化EventBus
    this.eventBus = new EventBus();
    this.eventBus.setDebugMode(this.config.debugMode);
    
    // 初始化ESP32Communicator
    this.esp32 = new ESP32Communicator(this.eventBus);
    
    console.log('✅ Core components initialized');
  }

  /**
   * 建立ESP32连接
   */
  async initESP32Connection() {
    console.log('🔌 Establishing ESP32 connection...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('ESP32 connection timeout'));
      }, this.config.esp32Timeout);
      
      // 监听连接成功事件
      this.eventBus.once('esp32.connected', (data) => {
        clearTimeout(timeout);
        this.systemState.esp32Connected = true;
        console.log('✅ ESP32 connection established');
        resolve(data);
      });
      
      // 监听连接错误事件
      this.eventBus.once('esp32.connection.error', (data) => {
        clearTimeout(timeout);
        reject(new Error(`ESP32 connection failed: ${data.error}`));
      });
    });
  }

  /**
   * 注册系统模块
   */
  async registerSystemModules() {
    console.log('📦 Registering system modules...');
    
    // 模块注册列表（按依赖顺序）- 现在包含全部7个模块
    const moduleConfigs = [
      { name: 'DisplayManager', class: 'DisplayManager', required: true },      // 显示模块
      { name: 'SystemMonitor', class: 'SystemMonitor', required: true },        // 监控模块
      { name: 'SignalManager', class: 'SignalManager', required: true },        // 信号管理模块
      { name: 'ControlCenter', class: 'ControlCenter', required: true },        // 控制模块
      { name: 'TimerManager', class: 'TimerManager', required: true },          // 定时模块
      { name: 'ConfigManager', class: 'ConfigManager', required: true },        // 配置管理模块
      { name: 'OTAManager', class: 'OTAManager', required: false }              // OTA模块
    ];
    
    for (const config of moduleConfigs) {
      try {
        if (window[config.class]) {
          const moduleInstance = new window[config.class](this.eventBus, this.esp32);
          this.modules.set(config.name, {
            instance: moduleInstance,
            config: config,
            status: 'registered'
          });
          console.log(`✅ Module '${config.name}' registered`);
        } else if (config.required) {
          throw new Error(`Required module class '${config.class}' not found`);
        } else {
          console.warn(`⚠️ Optional module class '${config.class}' not found`);
        }
      } catch (error) {
        if (config.required) {
          throw error;
        } else {
          console.warn(`⚠️ Failed to register optional module '${config.name}':`, error.message);
        }
      }
    }
    
    this.performance.totalModules = this.modules.size;
    console.log(`✅ ${this.modules.size} modules registered`);
  }

  /**
   * 初始化所有模块
   */
  async initializeModules() {
    console.log('🔄 Initializing modules...');
    
    const initPromises = [];
    
    for (const [moduleName, moduleInfo] of this.modules) {
      const initPromise = this.initializeModule(moduleName, moduleInfo);
      initPromises.push(initPromise);
    }
    
    // 等待所有模块初始化完成
    const results = await Promise.allSettled(initPromises);
    
    // 统计结果
    results.forEach((result, index) => {
      const moduleName = Array.from(this.modules.keys())[index];
      if (result.status === 'fulfilled') {
        this.performance.successfulModules++;
        console.log(`✅ Module '${moduleName}' initialized successfully`);
      } else {
        this.performance.failedModules++;
        console.error(`❌ Module '${moduleName}' initialization failed:`, result.reason);
        this.errors.push({
          module: moduleName,
          error: result.reason.message,
          timestamp: Date.now()
        });
      }
    });
    
    console.log(`✅ Module initialization complete: ${this.performance.successfulModules}/${this.performance.totalModules} successful`);
  }

  /**
   * 初始化单个模块
   */
  async initializeModule(moduleName, moduleInfo) {
    const startTime = performance.now();
    
    try {
      moduleInfo.status = 'initializing';
      
      // 设置模块初始化超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Module '${moduleName}' initialization timeout`)), this.config.moduleTimeout);
      });
      
      // 执行模块初始化
      const initPromise = moduleInfo.instance.init();
      
      // 等待初始化完成或超时
      await Promise.race([initPromise, timeoutPromise]);
      
      moduleInfo.status = 'initialized';
      const initTime = performance.now() - startTime;
      this.performance.moduleInitTimes.set(moduleName, initTime);
      
      return { moduleName, success: true, initTime };
    } catch (error) {
      moduleInfo.status = 'failed';
      throw new Error(`Module '${moduleName}' initialization failed: ${error.message}`);
    }
  }

  /**
   * 设置系统级事件监听器
   */
  setupSystemEventListeners() {
    console.log('📡 Setting up system event listeners...');
    
    // 模块切换事件
    this.eventBus.on('module.switch', (data) => {
      this.switchModule(data.moduleName);
    });
    
    // 系统刷新事件
    this.eventBus.on('system.refresh', () => {
      this.refreshSystem();
    });
    
    // 系统错误事件
    this.eventBus.on('system.error', (data) => {
      this.handleSystemError(new Error(data.error), data.operation || 'Unknown');
    });
    
    // ESP32连接状态变化
    this.eventBus.on('esp32.disconnected', () => {
      this.systemState.esp32Connected = false;
      this.updateSystemStatus();
    });
    
    this.eventBus.on('esp32.reconnected', () => {
      this.systemState.esp32Connected = true;
      this.updateSystemStatus();
    });
    
    console.log('✅ System event listeners setup complete');
  }

  /**
   * 切换模块
   */
  switchModule(moduleName) {
    if (!this.modules.has(moduleName)) {
      console.error(`❌ Module '${moduleName}' not found`);
      return;
    }
    
    // 隐藏当前模块
    if (this.systemState.currentModule) {
      const currentContainer = document.getElementById(`${this.systemState.currentModule.toLowerCase()}-container`);
      if (currentContainer) {
        currentContainer.classList.remove('active');
      }
    }
    
    // 显示目标模块
    const targetContainer = document.getElementById(`${moduleName.toLowerCase()}-container`);
    if (targetContainer) {
      targetContainer.classList.add('active');
      this.systemState.currentModule = moduleName;
      
      // 更新导航状态
      this.updateNavigation(moduleName);
      
      console.log(`✅ Switched to module: ${moduleName}`);
    }
  }

  /**
   * 更新导航状态
   */
  updateNavigation(activeModule) {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.classList.remove('active');
      if (item.dataset.module === activeModule.toLowerCase()) {
        item.classList.add('active');
      }
    });
  }

  /**
   * 更新初始化进度
   */
  updateInitProgress(progress, message) {
    this.systemState.initProgress = progress;
    
    const progressBar = document.getElementById('init-progress');
    const loaderText = document.querySelector('.loader-text');
    
    if (progressBar) {
      progressBar.style.width = `${progress}%`;
    }
    
    if (loaderText) {
      loaderText.textContent = message;
    }
    
    console.log(`📊 Init Progress: ${progress}% - ${message}`);
  }

  /**
   * 显示应用界面
   */
  showApplication() {
    const loader = document.getElementById('system-loader');
    const app = document.getElementById('app');
    
    if (loader) {
      loader.style.display = 'none';
    }
    
    if (app) {
      app.style.display = 'block';
    }
    
    // 设置默认模块
    this.switchModule('SignalManager');
  }

  /**
   * 处理系统错误
   */
  handleSystemError(error, operation) {
    console.error(`❌ System Error in ${operation}:`, error);
    
    this.errors.push({
      error: error.message,
      operation,
      timestamp: Date.now(),
      stack: error.stack
    });
    
    // 发布系统错误事件
    this.eventBus.emit('system.error.handled', {
      error: error.message,
      operation,
      timestamp: Date.now()
    });
  }

  /**
   * 刷新系统
   */
  async refreshSystem() {
    console.log('🔄 Refreshing system...');
    
    // 发布刷新事件给所有模块
    this.eventBus.emit('system.refresh');
    
    // 更新系统状态
    this.updateSystemStatus();
  }

  /**
   * 更新系统状态显示
   */
  updateSystemStatus() {
    const statusIndicator = document.getElementById('esp32-status');
    const statusText = document.querySelector('.status-text');
    
    if (statusIndicator && statusText) {
      if (this.systemState.esp32Connected) {
        statusIndicator.className = 'status-indicator connected';
        statusText.textContent = 'ESP32已连接';
      } else {
        statusIndicator.className = 'status-indicator disconnected';
        statusText.textContent = 'ESP32连接中...';
      }
    }
  }

  /**
   * 获取系统状态
   */
  getSystemStatus() {
    return {
      ...this.systemState,
      performance: this.performance,
      modulesStatus: Object.fromEntries(
        Array.from(this.modules.entries()).map(([name, info]) => [
          name, 
          {
            status: info.status,
            moduleStatus: info.instance.getStatus()
          }
        ])
      ),
      errors: this.errors,
      eventBusPerformance: this.eventBus.getPerformance(),
      esp32Status: this.esp32.getConnectionState()
    };
  }

  /**
   * 销毁系统
   */
  destroy() {
    console.log('🧹 Destroying R1System...');
    
    // 销毁所有模块
    for (const [moduleName, moduleInfo] of this.modules) {
      try {
        moduleInfo.instance.destroy();
      } catch (error) {
        console.error(`❌ Error destroying module '${moduleName}':`, error);
      }
    }
    
    // 清理核心组件
    if (this.esp32) {
      this.esp32.destroy();
    }
    
    if (this.eventBus) {
      this.eventBus.clear();
    }
    
    // 重置状态
    this.systemState.isInitialized = false;
    this.systemState.isReady = false;
    
    console.log('✅ R1System destroyed');
  }
}

// 导出R1System类
window.R1System = R1System;
