/**
 * R1系统 - ConfigManager配置管理模块
 * 基于：R1前端系统架构标准文档.md BaseModule架构标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 配置管理API
 *
 * 功能特性：
 * - 系统配置管理
 * - 用户偏好设置
 * - 配置导入导出
 * - 配置备份恢复
 * - 实时配置同步
 */

class ConfigManager extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'ConfigManager');

    // 配置数据
    this.systemConfig = new Map();        // 系统配置
    this.userPreferences = new Map();     // 用户偏好
    this.moduleConfigs = new Map();       // 模块配置
    this.configHistory = [];              // 配置历史

    // 配置状态
    this.configState = {
      isLoading: false,
      hasUnsavedChanges: false,
      lastSyncTime: 0,
      autoSaveEnabled: true,
      backupEnabled: true
    };

    // 配置分类
    this.configCategories = {
      system: '系统设置',
      network: '网络配置',
      ui: '界面设置',
      security: '安全设置',
      performance: '性能优化',
      modules: '模块配置'
    };

    // 默认配置
    this.defaultConfigs = {
      system: {
        deviceName: 'R1-ESP32-S3',
        timezone: 'Asia/Shanghai',
        language: 'zh-CN',
        debugMode: false
      },
      network: {
        wifiSSID: '',
        wifiPassword: '',
        staticIP: false,
        ipAddress: '',
        gateway: '',
        subnet: ''
      },
      ui: {
        theme: 'dark',
        fontSize: 'medium',
        animationEnabled: true,
        notificationEnabled: true,
        autoRefresh: true
      },
      security: {
        authRequired: true,
        sessionTimeout: 3600,
        maxLoginAttempts: 5,
        passwordComplexity: 'medium'
      }
    };

    console.log('✅ ConfigManager constructed');
  }

  /**
   * 事件监听器设置 - 必需实现
   */
  async setupEventListeners() {
    await super.setupEventListeners();

    // 监听配置变更请求
    this.eventBus.on('config.update.request', (data) => {
      this.handleConfigUpdateRequest(data);
    });

    // 监听配置重置请求
    this.eventBus.on('config.reset.request', (data) => {
      this.handleConfigResetRequest(data);
    });

    // 监听配置导出请求
    this.eventBus.on('config.export.request', (data) => {
      this.exportConfig(data.category);
    });

    // 监听配置导入请求
    this.eventBus.on('config.import.request', (data) => {
      this.importConfig(data.file);
    });

    // 监听模块配置请求
    this.eventBus.on('config.module.request', (data) => {
      this.sendModuleConfig(data);
    });

    // 监听系统配置变更
    this.eventBus.on('system.config.changed', (data) => {
      this.handleSystemConfigChanged(data);
    });

    console.log('📡 ConfigManager: Event listeners setup complete');
  }

  /**
   * UI设置 - 必需实现
   */
  async setupUI() {
    console.log('🎨 ConfigManager: Setting up UI...');

    // 缓存UI元素
    this.cacheElement('container', '#config-manager-content');
    this.cacheElement('exportConfigBtn', '#export-config-btn');
    this.cacheElement('importConfigBtn', '#import-config-btn');

    // 创建配置管理界面
    this.createConfigManagerUI();

    // 设置事件委托
    this.setupEventDelegation();

    console.log('✅ ConfigManager: UI setup complete');
  }

  /**
   * 数据加载 - 必需实现
   */
  async loadModuleData() {
    console.log('📊 ConfigManager: Loading config data...');

    try {
      this.state.loading = true;

      // 加载系统配置
      await this.loadSystemConfig();

      // 加载用户偏好
      await this.loadUserPreferences();

      // 加载模块配置
      await this.loadModuleConfigs();

      // 渲染配置界面
      this.renderConfigInterface();

      // 启动自动保存
      if (this.configState.autoSaveEnabled) {
        this.startAutoSave();
      }

      this.handleSuccess('配置管理器初始化完成', 'Load config data');

    } catch (error) {
      this.handleError(error, 'Load config data');
    } finally {
      this.state.loading = false;
    }
  }

  /**
   * 创建配置管理界面
   */
  createConfigManagerUI() {
    const container = this.getElement('container');
    if (!container) return;

    container.innerHTML = `
      <div class="config-manager">
        <!-- 配置工具栏 -->
        <div class="config-toolbar">
          <div class="toolbar-left">
            <div class="config-status">
              <span class="status-indicator" id="config-status-indicator"></span>
              <span class="status-text" id="config-status-text">配置已同步</span>
            </div>
          </div>
          <div class="toolbar-right">
            <button class="btn btn-secondary" id="refresh-config">
              <span class="icon">🔄</span>
              刷新配置
            </button>
            <button class="btn btn-secondary" id="backup-config">
              <span class="icon">💾</span>
              备份配置
            </button>
            <button class="btn btn-danger" id="reset-all-config">
              <span class="icon">🔄</span>
              重置全部
            </button>
          </div>
        </div>

        <!-- 配置分类标签 -->
        <div class="config-tabs">
          <button class="config-tab active" data-category="system">系统设置</button>
          <button class="config-tab" data-category="network">网络配置</button>
          <button class="config-tab" data-category="ui">界面设置</button>
          <button class="config-tab" data-category="security">安全设置</button>
          <button class="config-tab" data-category="performance">性能优化</button>
          <button class="config-tab" data-category="modules">模块配置</button>
        </div>

        <!-- 配置内容区域 -->
        <div class="config-content">
          <div class="config-panel active" id="system-config">
            <!-- 系统配置将动态生成 -->
          </div>
          <div class="config-panel" id="network-config">
            <!-- 网络配置将动态生成 -->
          </div>
          <div class="config-panel" id="ui-config">
            <!-- 界面配置将动态生成 -->
          </div>
          <div class="config-panel" id="security-config">
            <!-- 安全配置将动态生成 -->
          </div>
          <div class="config-panel" id="performance-config">
            <!-- 性能配置将动态生成 -->
          </div>
          <div class="config-panel" id="modules-config">
            <!-- 模块配置将动态生成 -->
          </div>
        </div>

        <!-- 配置操作面板 -->
        <div class="config-actions">
          <div class="actions-left">
            <label class="switch-label">
              <input type="checkbox" id="auto-save-toggle" ${this.configState.autoSaveEnabled ? 'checked' : ''}>
              <span class="switch-slider"></span>
              自动保存
            </label>
            <label class="switch-label">
              <input type="checkbox" id="backup-toggle" ${this.configState.backupEnabled ? 'checked' : ''}>
              <span class="switch-slider"></span>
              自动备份
            </label>
          </div>
          <div class="actions-right">
            <button class="btn btn-secondary" id="discard-changes" disabled>
              <span class="icon">↶</span>
              撤销更改
            </button>
            <button class="btn btn-primary" id="save-config" disabled>
              <span class="icon">💾</span>
              保存配置
            </button>
          </div>
        </div>

        <!-- 配置导入对话框 -->
        <div class="modal" id="import-config-modal" style="display: none;">
          <div class="modal-content">
            <div class="modal-header">
              <h3>导入配置</h3>
              <button class="modal-close" id="import-modal-close">×</button>
            </div>
            <div class="modal-body">
              <div class="import-form">
                <div class="form-group">
                  <label for="config-file">选择配置文件</label>
                  <input type="file" id="config-file" class="form-input" accept=".json,.txt">
                </div>
                <div class="form-group">
                  <label>导入选项</label>
                  <div class="import-options">
                    <label class="checkbox-option">
                      <input type="checkbox" id="merge-config" checked>
                      <span>合并配置（保留现有设置）</span>
                    </label>
                    <label class="checkbox-option">
                      <input type="checkbox" id="backup-before-import" checked>
                      <span>导入前自动备份</span>
                    </label>
                  </div>
                </div>
                <div class="import-preview" id="import-preview" style="display: none;">
                  <h4>配置预览</h4>
                  <div class="preview-content" id="preview-content"></div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button class="btn btn-secondary" id="import-cancel">取消</button>
              <button class="btn btn-primary" id="import-confirm" disabled>确认导入</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 设置事件委托
   */
  setupEventDelegation() {
    const container = this.getElement('container');
    if (!container) return;

    // 配置标签切换
    const configTabs = container.querySelectorAll('.config-tab');
    configTabs.forEach(tab => {
      tab.addEventListener('click', (event) => {
        const category = event.target.dataset.category;
        this.switchConfigCategory(category);
      });
    });

    // 使用事件委托处理所有点击事件
    container.addEventListener('click', (event) => {
      const target = event.target.closest('[data-action]');
      if (!target) return;

      const action = target.dataset.action;
      const configKey = target.dataset.configKey;
      const category = target.dataset.category;

      switch (action) {
        case 'reset-category':
          this.resetCategoryConfig(category);
          break;
        case 'export-category':
          this.exportConfig(category);
          break;
        case 'toggle-config':
          this.toggleConfigValue(configKey);
          break;
      }
    });

    // 配置值变化监听
    container.addEventListener('change', (event) => {
      if (event.target.classList.contains('config-input')) {
        this.handleConfigChange(event.target);
      }
    });

    // 刷新配置按钮
    const refreshBtn = container.querySelector('#refresh-config');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.loadModuleData();
      });
    }

    // 备份配置按钮
    const backupBtn = container.querySelector('#backup-config');
    if (backupBtn) {
      backupBtn.addEventListener('click', () => {
        this.backupConfig();
      });
    }

    // 重置全部配置按钮
    const resetAllBtn = container.querySelector('#reset-all-config');
    if (resetAllBtn) {
      resetAllBtn.addEventListener('click', () => {
        this.resetAllConfig();
      });
    }

    // 保存配置按钮
    const saveBtn = container.querySelector('#save-config');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        this.saveAllConfig();
      });
    }

    // 撤销更改按钮
    const discardBtn = container.querySelector('#discard-changes');
    if (discardBtn) {
      discardBtn.addEventListener('click', () => {
        this.discardChanges();
      });
    }

    // 自动保存开关
    const autoSaveToggle = container.querySelector('#auto-save-toggle');
    if (autoSaveToggle) {
      autoSaveToggle.addEventListener('change', (event) => {
        this.toggleAutoSave(event.target.checked);
      });
    }

    // 导入导出按钮
    const exportBtn = this.getElement('exportConfigBtn');
    const importBtn = this.getElement('importConfigBtn');

    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        this.exportConfig();
      });
    }

    if (importBtn) {
      importBtn.addEventListener('click', () => {
        this.showImportModal();
      });
    }
  }

  /**
   * 加载系统配置
   */
  async loadSystemConfig() {
    try {
      const response = await this.requestESP32('/api/config', {
        method: 'GET'
      });

      if (response.success && response.data) {
        // 处理系统配置数据
        Object.keys(response.data).forEach(category => {
          if (this.defaultConfigs[category]) {
            this.systemConfig.set(category, {
              ...this.defaultConfigs[category],
              ...response.data[category]
            });
          }
        });

        console.log('✅ System config loaded');
      }
    } catch (error) {
      console.warn('⚠️ Failed to load system config:', error.message);
      // 使用默认配置
      Object.keys(this.defaultConfigs).forEach(category => {
        this.systemConfig.set(category, { ...this.defaultConfigs[category] });
      });
    }
  }

  /**
   * 加载用户偏好
   */
  async loadUserPreferences() {
    try {
      // 从localStorage加载用户偏好
      const savedPrefs = localStorage.getItem('r1_user_preferences');
      if (savedPrefs) {
        const preferences = JSON.parse(savedPrefs);
        Object.keys(preferences).forEach(key => {
          this.userPreferences.set(key, preferences[key]);
        });
      }

      console.log('✅ User preferences loaded');
    } catch (error) {
      console.warn('⚠️ Failed to load user preferences:', error.message);
    }
  }

  /**
   * 加载模块配置
   */
  async loadModuleConfigs() {
    try {
      // 请求各模块的配置
      const moduleNames = ['SignalManager', 'ControlCenter', 'TimerManager', 'SystemMonitor', 'OTAManager'];

      for (const moduleName of moduleNames) {
        this.emitEvent('config.module.request', {
          moduleName: moduleName,
          requestId: `config_req_${Date.now()}_${moduleName}`
        });
      }

      console.log('✅ Module configs requested');
    } catch (error) {
      console.warn('⚠️ Failed to load module configs:', error.message);
    }
  }

  /**
   * 渲染配置界面
   */
  renderConfigInterface() {
    this.renderSystemConfig();
    this.renderNetworkConfig();
    this.renderUIConfig();
    this.renderSecurityConfig();
    this.renderPerformanceConfig();
    this.renderModulesConfig();

    this.updateConfigStatus();
  }

  /**
   * 渲染系统配置
   */
  renderSystemConfig() {
    const panel = document.getElementById('system-config');
    if (!panel) return;

    const systemConfig = this.systemConfig.get('system') || this.defaultConfigs.system;

    panel.innerHTML = `
      <div class="config-section">
        <div class="section-header">
          <h3>基本设置</h3>
          <button class="btn btn-secondary" data-action="reset-category" data-category="system">重置</button>
        </div>
        <div class="config-items">
          <div class="config-item">
            <label class="config-label">设备名称</label>
            <input type="text" class="config-input" data-config-key="system.deviceName"
                   value="${systemConfig.deviceName}" placeholder="设备名称">
            <div class="config-description">用于识别此设备的名称</div>
          </div>

          <div class="config-item">
            <label class="config-label">时区设置</label>
            <select class="config-input" data-config-key="system.timezone">
              <option value="Asia/Shanghai" ${systemConfig.timezone === 'Asia/Shanghai' ? 'selected' : ''}>中国标准时间</option>
              <option value="UTC" ${systemConfig.timezone === 'UTC' ? 'selected' : ''}>协调世界时</option>
              <option value="America/New_York" ${systemConfig.timezone === 'America/New_York' ? 'selected' : ''}>美国东部时间</option>
              <option value="Europe/London" ${systemConfig.timezone === 'Europe/London' ? 'selected' : ''}>英国时间</option>
            </select>
            <div class="config-description">系统使用的时区</div>
          </div>

          <div class="config-item">
            <label class="config-label">系统语言</label>
            <select class="config-input" data-config-key="system.language">
              <option value="zh-CN" ${systemConfig.language === 'zh-CN' ? 'selected' : ''}>简体中文</option>
              <option value="en-US" ${systemConfig.language === 'en-US' ? 'selected' : ''}>English</option>
            </select>
            <div class="config-description">界面显示语言</div>
          </div>

          <div class="config-item">
            <label class="config-label">调试模式</label>
            <label class="switch">
              <input type="checkbox" class="config-input" data-config-key="system.debugMode"
                     ${systemConfig.debugMode ? 'checked' : ''}>
              <span class="slider"></span>
            </label>
            <div class="config-description">启用详细日志和调试信息</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染网络配置
   */
  renderNetworkConfig() {
    const panel = document.getElementById('network-config');
    if (!panel) return;

    const networkConfig = this.systemConfig.get('network') || this.defaultConfigs.network;

    panel.innerHTML = `
      <div class="config-section">
        <div class="section-header">
          <h3>WiFi设置</h3>
          <button class="btn btn-secondary" data-action="reset-category" data-category="network">重置</button>
        </div>
        <div class="config-items">
          <div class="config-item">
            <label class="config-label">WiFi名称 (SSID)</label>
            <input type="text" class="config-input" data-config-key="network.wifiSSID"
                   value="${networkConfig.wifiSSID}" placeholder="WiFi网络名称">
            <div class="config-description">要连接的WiFi网络名称</div>
          </div>

          <div class="config-item">
            <label class="config-label">WiFi密码</label>
            <input type="password" class="config-input" data-config-key="network.wifiPassword"
                   value="${networkConfig.wifiPassword}" placeholder="WiFi密码">
            <div class="config-description">WiFi网络密码</div>
          </div>

          <div class="config-item">
            <label class="config-label">使用静态IP</label>
            <label class="switch">
              <input type="checkbox" class="config-input" data-config-key="network.staticIP"
                     ${networkConfig.staticIP ? 'checked' : ''}>
              <span class="slider"></span>
            </label>
            <div class="config-description">启用静态IP地址配置</div>
          </div>

          <div class="config-item ${!networkConfig.staticIP ? 'disabled' : ''}">
            <label class="config-label">IP地址</label>
            <input type="text" class="config-input" data-config-key="network.ipAddress"
                   value="${networkConfig.ipAddress}" placeholder="*************"
                   ${!networkConfig.staticIP ? 'disabled' : ''}>
            <div class="config-description">静态IP地址</div>
          </div>

          <div class="config-item ${!networkConfig.staticIP ? 'disabled' : ''}">
            <label class="config-label">网关地址</label>
            <input type="text" class="config-input" data-config-key="network.gateway"
                   value="${networkConfig.gateway}" placeholder="***********"
                   ${!networkConfig.staticIP ? 'disabled' : ''}>
            <div class="config-description">网关IP地址</div>
          </div>

          <div class="config-item ${!networkConfig.staticIP ? 'disabled' : ''}">
            <label class="config-label">子网掩码</label>
            <input type="text" class="config-input" data-config-key="network.subnet"
                   value="${networkConfig.subnet}" placeholder="*************"
                   ${!networkConfig.staticIP ? 'disabled' : ''}>
            <div class="config-description">子网掩码</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 保存所有配置
   */
  async saveAllConfig() {
    try {
      this.configState.isLoading = true;

      // 收集所有配置数据
      const configData = this.collectAllConfigData();

      // 发送到ESP32
      const response = await this.requestESP32('/api/config', {
        method: 'PUT',
        body: JSON.stringify(configData)
      });

      if (response.success) {
        this.configState.hasUnsavedChanges = false;
        this.configState.lastSyncTime = Date.now();

        this.updateConfigButtons();
        this.updateConfigStatus();

        this.handleSuccess('配置保存成功', 'Save config');
      }

    } catch (error) {
      this.handleError(error, 'Save config');
    } finally {
      this.configState.isLoading = false;
    }
  }

  /**
   * 收集所有配置数据
   */
  collectAllConfigData() {
    const configData = {};

    // 收集所有配置输入
    const configInputs = document.querySelectorAll('.config-input');
    configInputs.forEach(input => {
      const configKey = input.dataset.configKey;
      if (!configKey) return;

      const keys = configKey.split('.');
      let current = configData;

      // 创建嵌套对象结构
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }

      // 设置值
      let value = input.value;
      if (input.type === 'checkbox') {
        value = input.checked;
      } else if (input.type === 'number') {
        value = parseFloat(value);
      }

      current[keys[keys.length - 1]] = value;
    });

    return configData;
  }

  /**
   * 更新配置按钮状态
   */
  updateConfigButtons() {
    const saveBtn = document.getElementById('save-config');
    const discardBtn = document.getElementById('discard-changes');

    if (saveBtn) {
      saveBtn.disabled = !this.configState.hasUnsavedChanges;
    }

    if (discardBtn) {
      discardBtn.disabled = !this.configState.hasUnsavedChanges;
    }
  }

  /**
   * 更新配置状态
   */
  updateConfigStatus() {
    const indicator = document.getElementById('config-status-indicator');
    const text = document.getElementById('config-status-text');

    if (indicator && text) {
      if (this.configState.hasUnsavedChanges) {
        indicator.className = 'status-indicator warning';
        text.textContent = '有未保存的更改';
      } else {
        indicator.className = 'status-indicator success';
        text.textContent = '配置已同步';
      }
    }
  }

  /**
   * 导出配置
   */
  exportConfig(category = null) {
    try {
      let exportData;

      if (category) {
        // 导出特定分类
        exportData = {
          category: category,
          config: this.systemConfig.get(category) || {},
          exportTime: new Date().toISOString()
        };
      } else {
        // 导出全部配置
        exportData = {
          systemConfig: Object.fromEntries(this.systemConfig),
          userPreferences: Object.fromEntries(this.userPreferences),
          moduleConfigs: Object.fromEntries(this.moduleConfigs),
          exportTime: new Date().toISOString()
        };
      }

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `r1-config-${category || 'all'}-${new Date().toISOString().slice(0, 10)}.json`;
      link.click();

      this.handleSuccess('配置导出成功', 'Export config');

    } catch (error) {
      this.handleError(error, 'Export config');
    }
  }

  /**
   * 销毁模块时清理资源
   */
  destroy() {
    // 停止自动保存
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }

    // 调用父类销毁方法
    super.destroy();
  }
}

// 导出ConfigManager类
window.ConfigManager = ConfigManager;