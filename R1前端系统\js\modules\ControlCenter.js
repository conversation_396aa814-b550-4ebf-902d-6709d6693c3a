/**
 * R1系统 - ControlCenter控制中心模块
 * 基于：R1前端系统架构标准文档.md BaseModule架构标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 控制API规范
 * 
 * 功能特性：
 * - 快速控制面板
 * - 信号发送控制
 * - 批量操作支持
 * - 实时状态监控
 * - 任务执行管理
 */

class ControlCenter extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'ControlCenter');
    
    // 业务数据
    this.availableSignals = new Map();     // 可用信号列表
    this.quickAccessSignals = new Set();   // 快速访问信号
    this.recentSignals = [];               // 最近使用信号
    this.runningTasks = new Map();         // 运行中的任务
    
    // 控制状态
    this.controlState = {
      isEmitting: false,
      lastEmitTime: 0,
      emitQueue: [],
      batchMode: false,
      selectedSignals: new Set()
    };
    
    // UI配置
    this.uiConfig = {
      quickAccessLimit: 12,
      recentLimit: 10,
      emitDelay: 500,
      batchDelay: 1000
    };
    
    // 性能统计
    this.controlStats = {
      totalEmits: 0,
      successfulEmits: 0,
      failedEmits: 0,
      averageEmitTime: 0
    };
    
    console.log('✅ ControlCenter constructed');
  }

  /**
   * 事件监听器设置 - 必需实现
   */
  async setupEventListeners() {
    await super.setupEventListeners();
    
    // 监听信号管理器的信号列表响应
    this.eventBus.on('control.signals.response', (data) => {
      this.handleSignalsResponse(data);
    });
    
    // 监听信号发送完成事件
    this.eventBus.on('signal.emitted', (data) => {
      this.handleSignalEmitted(data);
    });
    
    // 监听任务执行事件
    this.eventBus.on('task.execution.start', (data) => {
      this.handleTaskStart(data);
    });
    
    this.eventBus.on('task.execution.complete', (data) => {
      this.handleTaskComplete(data);
    });
    
    this.eventBus.on('task.execution.progress', (data) => {
      this.handleTaskProgress(data);
    });
    
    // 监听系统状态变化
    this.eventBus.on('esp32.connected', () => {
      this.updateConnectionStatus(true);
    });
    
    this.eventBus.on('esp32.disconnected', () => {
      this.updateConnectionStatus(false);
    });
    
    console.log('📡 ControlCenter: Event listeners setup complete');
  }

  /**
   * UI设置 - 必需实现
   */
  async setupUI() {
    console.log('🎨 ControlCenter: Setting up UI...');
    
    // 缓存UI元素
    this.cacheElement('container', '#control-center-content');
    this.cacheElement('quickControlBtn', '#quick-control-btn');
    
    // 创建控制中心界面
    this.createControlCenterUI();
    
    // 设置事件委托
    this.setupEventDelegation();
    
    console.log('✅ ControlCenter: UI setup complete');
  }

  /**
   * 数据加载 - 必需实现
   */
  async loadModuleData() {
    console.log('📊 ControlCenter: Loading control data...');
    
    try {
      this.state.loading = true;
      
      // 请求信号管理器提供信号列表
      this.requestSignalList();
      
      // 加载快速访问配置
      await this.loadQuickAccessConfig();
      
      // 加载最近使用记录
      await this.loadRecentSignals();
      
      // 加载运行中的任务
      await this.loadRunningTasks();
      
      this.handleSuccess('控制中心数据加载完成', 'Load control data');
      
    } catch (error) {
      this.handleError(error, 'Load control data');
    } finally {
      this.state.loading = false;
    }
  }

  /**
   * 创建控制中心界面
   */
  createControlCenterUI() {
    const container = this.getElement('container');
    if (!container) return;
    
    container.innerHTML = `
      <div class="control-center">
        <!-- 控制状态栏 -->
        <div class="control-status">
          <div class="status-item">
            <span class="status-label">连接状态:</span>
            <span class="status-value" id="connection-status">检查中...</span>
          </div>
          <div class="status-item">
            <span class="status-label">发送统计:</span>
            <span class="status-value" id="emit-stats">0/0</span>
          </div>
          <div class="status-item">
            <span class="status-label">运行任务:</span>
            <span class="status-value" id="running-tasks">0</span>
          </div>
        </div>
        
        <!-- 快速控制面板 -->
        <div class="quick-control-panel">
          <div class="panel-header">
            <h3>快速控制</h3>
            <div class="panel-actions">
              <button class="btn btn-secondary" id="edit-quick-access">编辑</button>
              <button class="btn btn-secondary" id="batch-mode-toggle">批量模式</button>
            </div>
          </div>
          <div class="quick-signals" id="quick-signals">
            <!-- 快速访问信号将动态生成 -->
          </div>
        </div>
        
        <!-- 最近使用 -->
        <div class="recent-panel">
          <div class="panel-header">
            <h3>最近使用</h3>
            <button class="btn btn-secondary" id="clear-recent">清空</button>
          </div>
          <div class="recent-signals" id="recent-signals">
            <!-- 最近使用信号将动态生成 -->
          </div>
        </div>
        
        <!-- 批量控制面板 -->
        <div class="batch-control-panel" id="batch-control-panel" style="display: none;">
          <div class="panel-header">
            <h3>批量控制</h3>
            <div class="batch-info">
              已选择 <span id="batch-selected-count">0</span> 个信号
            </div>
          </div>
          <div class="batch-actions">
            <div class="batch-settings">
              <label>发送间隔:</label>
              <select id="batch-delay-select">
                <option value="500">0.5秒</option>
                <option value="1000" selected>1秒</option>
                <option value="2000">2秒</option>
                <option value="5000">5秒</option>
              </select>
            </div>
            <div class="batch-buttons">
              <button class="btn btn-primary" id="batch-emit-start">开始发送</button>
              <button class="btn btn-secondary" id="batch-emit-stop" disabled>停止发送</button>
              <button class="btn btn-secondary" id="batch-clear">清空选择</button>
            </div>
          </div>
          <div class="batch-progress" id="batch-progress" style="display: none;">
            <div class="progress-info">
              <span id="batch-current">0</span> / <span id="batch-total">0</span>
            </div>
            <div class="progress-bar-container">
              <div class="progress-bar" id="batch-progress-bar"></div>
            </div>
          </div>
        </div>
        
        <!-- 任务监控面板 -->
        <div class="task-monitor-panel">
          <div class="panel-header">
            <h3>任务监控</h3>
            <button class="btn btn-secondary" id="refresh-tasks">刷新</button>
          </div>
          <div class="running-tasks-list" id="running-tasks-list">
            <!-- 运行中的任务将动态生成 -->
          </div>
        </div>
        
        <!-- 信号选择对话框 -->
        <div class="modal" id="signal-select-modal" style="display: none;">
          <div class="modal-content">
            <div class="modal-header">
              <h3>选择信号</h3>
              <button class="modal-close" id="signal-select-close">×</button>
            </div>
            <div class="modal-body">
              <div class="signal-search">
                <input type="text" id="signal-search-input" placeholder="搜索信号..." class="search-input">
              </div>
              <div class="signal-list" id="signal-select-list">
                <!-- 信号列表将动态生成 -->
              </div>
            </div>
            <div class="modal-footer">
              <button class="btn btn-secondary" id="signal-select-cancel">取消</button>
              <button class="btn btn-primary" id="signal-select-confirm">确认</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 设置事件委托
   */
  setupEventDelegation() {
    const container = this.getElement('container');
    if (!container) return;
    
    // 使用事件委托处理所有点击事件
    container.addEventListener('click', (event) => {
      const target = event.target.closest('[data-action]');
      if (!target) return;
      
      const action = target.dataset.action;
      const signalId = target.dataset.signalId;
      const taskId = target.dataset.taskId;
      
      switch (action) {
        case 'emit-signal':
          this.emitSingleSignal(signalId);
          break;
        case 'add-to-quick':
          this.addToQuickAccess(signalId);
          break;
        case 'remove-from-quick':
          this.removeFromQuickAccess(signalId);
          break;
        case 'select-for-batch':
          this.toggleBatchSelection(signalId);
          break;
        case 'stop-task':
          this.stopTask(taskId);
          break;
        case 'pause-task':
          this.pauseTask(taskId);
          break;
        case 'resume-task':
          this.resumeTask(taskId);
          break;
      }
    });
    
    // 批量模式切换
    const batchToggle = container.querySelector('#batch-mode-toggle');
    if (batchToggle) {
      batchToggle.addEventListener('click', () => {
        this.toggleBatchMode();
      });
    }
    
    // 批量发送控制
    const batchStart = container.querySelector('#batch-emit-start');
    const batchStop = container.querySelector('#batch-emit-stop');
    
    if (batchStart) {
      batchStart.addEventListener('click', () => {
        this.startBatchEmit();
      });
    }
    
    if (batchStop) {
      batchStop.addEventListener('click', () => {
        this.stopBatchEmit();
      });
    }
    
    // 快速控制按钮
    const quickControlBtn = this.getElement('quickControlBtn');
    if (quickControlBtn) {
      quickControlBtn.addEventListener('click', () => {
        this.showQuickControlDialog();
      });
    }
  }

  /**
   * 请求信号列表
   */
  requestSignalList() {
    const requestId = `control_req_${Date.now()}`;
    
    // 发送请求事件到信号管理器
    this.emitEvent('control.signals.request', {
      requestId: requestId,
      timestamp: Date.now()
    });
  }

  /**
   * 处理信号列表响应
   */
  handleSignalsResponse(data) {
    if (!data.signals) return;
    
    // 更新可用信号列表
    this.availableSignals.clear();
    data.signals.forEach(signal => {
      this.availableSignals.set(signal.id, signal);
    });
    
    // 渲染快速控制面板
    this.renderQuickControlPanel();
    
    // 渲染最近使用面板
    this.renderRecentPanel();
    
    console.log(`✅ Received ${data.signals.length} signals from SignalManager`);
  }

  /**
   * 渲染快速控制面板
   */
  renderQuickControlPanel() {
    const quickSignals = document.getElementById('quick-signals');
    if (!quickSignals) return;
    
    const quickAccessArray = Array.from(this.quickAccessSignals)
      .map(signalId => this.availableSignals.get(signalId))
      .filter(signal => signal);
    
    if (quickAccessArray.length === 0) {
      quickSignals.innerHTML = `
        <div class="empty-state">
          <div class="empty-icon">🎮</div>
          <div class="empty-text">暂无快速访问信号</div>
          <button class="btn btn-primary" id="add-quick-signal">添加信号</button>
        </div>
      `;
      return;
    }
    
    quickSignals.innerHTML = quickAccessArray.map(signal => `
      <div class="quick-signal-item ${this.controlState.selectedSignals.has(signal.id) ? 'selected' : ''}" 
           data-signal-id="${signal.id}">
        ${this.controlState.batchMode ? `
          <div class="signal-checkbox">
            <input type="checkbox" ${this.controlState.selectedSignals.has(signal.id) ? 'checked' : ''} 
                   data-action="select-for-batch" data-signal-id="${signal.id}">
          </div>
        ` : ''}
        
        <div class="signal-icon">
          <span class="protocol-icon">${this.getProtocolIcon(signal.protocol)}</span>
        </div>
        
        <div class="signal-info">
          <div class="signal-name">${signal.name}</div>
          <div class="signal-protocol">${signal.protocol}</div>
        </div>
        
        <div class="signal-actions">
          ${!this.controlState.batchMode ? `
            <button class="action-btn emit-btn" 
                    data-action="emit-signal" data-signal-id="${signal.id}" 
                    title="发送信号">
              📡
            </button>
          ` : ''}
          <button class="action-btn remove-btn" 
                  data-action="remove-from-quick" data-signal-id="${signal.id}" 
                  title="移除">
            ❌
          </button>
        </div>
      </div>
    `).join('');
  }

  /**
   * 渲染最近使用面板
   */
  renderRecentPanel() {
    const recentSignals = document.getElementById('recent-signals');
    if (!recentSignals) return;
    
    if (this.recentSignals.length === 0) {
      recentSignals.innerHTML = `
        <div class="empty-state">
          <div class="empty-icon">⏱️</div>
          <div class="empty-text">暂无最近使用记录</div>
        </div>
      `;
      return;
    }
    
    recentSignals.innerHTML = this.recentSignals.map(signalId => {
      const signal = this.availableSignals.get(signalId);
      if (!signal) return '';
      
      return `
        <div class="recent-signal-item" data-signal-id="${signal.id}">
          <div class="signal-icon">
            <span class="protocol-icon">${this.getProtocolIcon(signal.protocol)}</span>
          </div>
          <div class="signal-info">
            <div class="signal-name">${signal.name}</div>
            <div class="signal-time">${this.formatLastUsed(signal.lastUsed)}</div>
          </div>
          <div class="signal-actions">
            <button class="action-btn emit-btn" 
                    data-action="emit-signal" data-signal-id="${signal.id}" 
                    title="发送信号">
              📡
            </button>
            <button class="action-btn add-btn" 
                    data-action="add-to-quick" data-signal-id="${signal.id}" 
                    title="添加到快速访问">
              ⭐
            </button>
          </div>
        </div>
      `;
    }).join('');
  }

  /**
   * 发送单个信号
   */
  async emitSingleSignal(signalId) {
    if (this.controlState.isEmitting) {
      console.warn('⚠️ Signal emission in progress, please wait');
      return;
    }
    
    const signal = this.availableSignals.get(signalId);
    if (!signal) {
      this.handleError(new Error('信号不存在'), 'Emit signal');
      return;
    }
    
    try {
      this.controlState.isEmitting = true;
      const startTime = performance.now();
      
      // 发送信号请求事件
      this.emitEvent('signal.emit.request', {
        signalId: signalId,
        timestamp: Date.now()
      });
      
      // 更新统计
      this.controlStats.totalEmits++;
      
      // 添加到最近使用
      this.addToRecentSignals(signalId);
      
      // 更新UI状态
      this.updateEmitStats();
      
      console.log(`✅ Signal "${signal.name}" emit requested`);
      
    } catch (error) {
      this.controlStats.failedEmits++;
      this.handleError(error, 'Emit signal');
    } finally {
      this.controlState.isEmitting = false;
      this.controlState.lastEmitTime = Date.now();
    }
  }

  /**
   * 处理信号发送完成
   */
  handleSignalEmitted(data) {
    this.controlStats.successfulEmits++;
    this.updateEmitStats();
    
    console.log(`✅ Signal "${data.signalName}" emitted successfully`);
  }

  /**
   * 切换批量模式
   */
  toggleBatchMode() {
    this.controlState.batchMode = !this.controlState.batchMode;
    this.controlState.selectedSignals.clear();
    
    // 显示/隐藏批量控制面板
    const batchPanel = document.getElementById('batch-control-panel');
    if (batchPanel) {
      batchPanel.style.display = this.controlState.batchMode ? 'block' : 'none';
    }
    
    // 更新批量模式按钮状态
    const batchToggle = document.getElementById('batch-mode-toggle');
    if (batchToggle) {
      batchToggle.classList.toggle('active', this.controlState.batchMode);
      batchToggle.textContent = this.controlState.batchMode ? '退出批量' : '批量模式';
    }
    
    // 重新渲染快速控制面板
    this.renderQuickControlPanel();
  }

  /**
   * 切换批量选择
   */
  toggleBatchSelection(signalId) {
    if (this.controlState.selectedSignals.has(signalId)) {
      this.controlState.selectedSignals.delete(signalId);
    } else {
      this.controlState.selectedSignals.add(signalId);
    }
    
    // 更新选中计数
    const selectedCount = document.getElementById('batch-selected-count');
    if (selectedCount) {
      selectedCount.textContent = this.controlState.selectedSignals.size;
    }
    
    // 更新信号项样式
    const signalItem = document.querySelector(`[data-signal-id="${signalId}"]`);
    if (signalItem) {
      signalItem.classList.toggle('selected', this.controlState.selectedSignals.has(signalId));
      
      const checkbox = signalItem.querySelector('input[type="checkbox"]');
      if (checkbox) {
        checkbox.checked = this.controlState.selectedSignals.has(signalId);
      }
    }
  }

  /**
   * 开始批量发送
   */
  async startBatchEmit() {
    if (this.controlState.selectedSignals.size === 0) {
      this.handleError(new Error('请先选择要发送的信号'), 'Batch emit');
      return;
    }
    
    const selectedArray = Array.from(this.controlState.selectedSignals);
    const delaySelect = document.getElementById('batch-delay-select');
    const delay = delaySelect ? parseInt(delaySelect.value) : 1000;
    
    // 显示进度
    const progressPanel = document.getElementById('batch-progress');
    const totalSpan = document.getElementById('batch-total');
    const currentSpan = document.getElementById('batch-current');
    const progressBar = document.getElementById('batch-progress-bar');
    
    if (progressPanel) progressPanel.style.display = 'block';
    if (totalSpan) totalSpan.textContent = selectedArray.length;
    if (currentSpan) currentSpan.textContent = '0';
    if (progressBar) progressBar.style.width = '0%';
    
    // 禁用开始按钮，启用停止按钮
    const startBtn = document.getElementById('batch-emit-start');
    const stopBtn = document.getElementById('batch-emit-stop');
    if (startBtn) startBtn.disabled = true;
    if (stopBtn) stopBtn.disabled = false;
    
    try {
      for (let i = 0; i < selectedArray.length; i++) {
        if (!this.controlState.batchMode) break; // 检查是否被停止
        
        const signalId = selectedArray[i];
        
        // 发送信号
        await this.emitSingleSignal(signalId);
        
        // 更新进度
        const progress = ((i + 1) / selectedArray.length) * 100;
        if (currentSpan) currentSpan.textContent = (i + 1).toString();
        if (progressBar) progressBar.style.width = `${progress}%`;
        
        // 等待延迟（除了最后一个）
        if (i < selectedArray.length - 1) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
      
      this.handleSuccess(`批量发送完成，共发送 ${selectedArray.length} 个信号`, 'Batch emit');
      
    } catch (error) {
      this.handleError(error, 'Batch emit');
    } finally {
      // 恢复按钮状态
      if (startBtn) startBtn.disabled = false;
      if (stopBtn) stopBtn.disabled = true;
      
      // 隐藏进度
      if (progressPanel) progressPanel.style.display = 'none';
    }
  }

  /**
   * 停止批量发送
   */
  stopBatchEmit() {
    this.controlState.batchMode = false;
    
    const startBtn = document.getElementById('batch-emit-start');
    const stopBtn = document.getElementById('batch-emit-stop');
    const progressPanel = document.getElementById('batch-progress');
    
    if (startBtn) startBtn.disabled = false;
    if (stopBtn) stopBtn.disabled = true;
    if (progressPanel) progressPanel.style.display = 'none';
    
    console.log('🛑 Batch emit stopped');
  }

  /**
   * 添加到最近使用
   */
  addToRecentSignals(signalId) {
    // 移除已存在的记录
    const index = this.recentSignals.indexOf(signalId);
    if (index !== -1) {
      this.recentSignals.splice(index, 1);
    }
    
    // 添加到开头
    this.recentSignals.unshift(signalId);
    
    // 限制数量
    if (this.recentSignals.length > this.uiConfig.recentLimit) {
      this.recentSignals = this.recentSignals.slice(0, this.uiConfig.recentLimit);
    }
    
    // 重新渲染最近使用面板
    this.renderRecentPanel();
  }

  /**
   * 更新发送统计
   */
  updateEmitStats() {
    const emitStats = document.getElementById('emit-stats');
    if (emitStats) {
      emitStats.textContent = `${this.controlStats.successfulEmits}/${this.controlStats.totalEmits}`;
    }
  }

  /**
   * 更新连接状态
   */
  updateConnectionStatus(connected) {
    const connectionStatus = document.getElementById('connection-status');
    if (connectionStatus) {
      connectionStatus.textContent = connected ? '已连接' : '未连接';
      connectionStatus.className = `status-value ${connected ? 'connected' : 'disconnected'}`;
    }
  }

  /**
   * 获取协议图标
   */
  getProtocolIcon(protocol) {
    const icons = {
      'NEC': '📺',
      'Sony': '🎮',
      'RC5': '📻',
      'RC6': '📡',
      'Samsung': '📱',
      'LG': '🖥️',
      'Raw': '🔧'
    };
    return icons[protocol] || '📡';
  }

  /**
   * 格式化最后使用时间
   */
  formatLastUsed(timestamp) {
    if (!timestamp) return '未知';
    
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    return `${Math.floor(diff / 86400000)}天前`;
  }

  /**
   * 加载快速访问配置
   */
  async loadQuickAccessConfig() {
    try {
      const response = await this.requestESP32('/api/config/quick-access', {
        method: 'GET'
      });
      
      if (response.success && response.data.signalIds) {
        this.quickAccessSignals = new Set(response.data.signalIds);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load quick access config:', error.message);
    }
  }

  /**
   * 加载最近使用记录
   */
  async loadRecentSignals() {
    try {
      const response = await this.requestESP32('/api/config/recent-signals', {
        method: 'GET'
      });
      
      if (response.success && response.data.signalIds) {
        this.recentSignals = response.data.signalIds;
      }
    } catch (error) {
      console.warn('⚠️ Failed to load recent signals:', error.message);
    }
  }

  /**
   * 加载运行中的任务
   */
  async loadRunningTasks() {
    try {
      const response = await this.requestESP32('/api/tasks/running', {
        method: 'GET'
      });
      
      if (response.success && response.data.tasks) {
        this.runningTasks.clear();
        response.data.tasks.forEach(task => {
          this.runningTasks.set(task.id, task);
        });
        this.updateRunningTasksCount();
      }
    } catch (error) {
      console.warn('⚠️ Failed to load running tasks:', error.message);
    }
  }

  /**
   * 更新运行任务计数
   */
  updateRunningTasksCount() {
    const runningTasksSpan = document.getElementById('running-tasks');
    if (runningTasksSpan) {
      runningTasksSpan.textContent = this.runningTasks.size.toString();
    }
  }
}

// 导出ControlCenter类
window.ControlCenter = ControlCenter;
