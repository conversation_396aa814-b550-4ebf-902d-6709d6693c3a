/**
 * R1系统 - DisplayManager显示模块
 * 基于：R1前端系统架构标准文档.md BaseModule架构标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 显示管理API
 * 
 * 功能特性：
 * - 系统状态显示
 * - 实时数据展示
 * - 仪表盘管理
 * - 数据可视化
 * - 自定义显示布局
 */

class DisplayManager extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'DisplayManager');
    
    // 显示数据
    this.displayData = {
      systemStatus: {},
      realtimeData: {},
      statistics: {},
      alerts: []
    };
    
    // 显示配置
    this.displayConfig = {
      layout: 'dashboard',
      refreshInterval: 2000,
      showAnimations: true,
      autoScale: true,
      theme: 'dark'
    };
    
    // 仪表盘组件
    this.dashboardWidgets = new Map();
    this.widgetTypes = {
      status: '状态卡片',
      chart: '图表组件',
      gauge: '仪表盘',
      counter: '计数器',
      progress: '进度条',
      alert: '警报面板'
    };
    
    // 显示状态
    this.displayState = {
      isActive: false,
      lastUpdateTime: 0,
      updateTimer: null,
      activeWidgets: new Set()
    };
    
    console.log('✅ DisplayManager constructed');
  }

  /**
   * 事件监听器设置 - 必需实现
   */
  async setupEventListeners() {
    await super.setupEventListeners();
    
    // 监听系统状态更新
    this.eventBus.on('system.status.update', (data) => {
      this.updateSystemStatus(data);
    });
    
    // 监听实时数据更新
    this.eventBus.on('display.data.update', (data) => {
      this.updateRealtimeData(data);
    });
    
    // 监听信号发送事件
    this.eventBus.on('signal.emitted', (data) => {
      this.updateSignalActivity(data);
    });
    
    // 监听定时器触发事件
    this.eventBus.on('timer.triggered', (data) => {
      this.updateTimerActivity(data);
    });
    
    // 监听系统错误事件
    this.eventBus.on('system.error', (data) => {
      this.addAlert('error', data.error, data.operation);
    });
    
    // 监听模块状态变化
    this.eventBus.on('module.ready', (data) => {
      this.updateModuleStatus(data.moduleName, 'ready');
    });
    
    this.eventBus.on('module.error', (data) => {
      this.updateModuleStatus(data.moduleName, 'error');
    });
    
    console.log('📡 DisplayManager: Event listeners setup complete');
  }

  /**
   * UI设置 - 必需实现
   */
  async setupUI() {
    console.log('🎨 DisplayManager: Setting up UI...');
    
    // 缓存UI元素
    this.cacheElement('container', '#display-manager-content');
    this.cacheElement('layoutBtn', '#layout-toggle-btn');
    
    // 创建显示管理界面
    this.createDisplayManagerUI();
    
    // 设置事件委托
    this.setupEventDelegation();
    
    console.log('✅ DisplayManager: UI setup complete');
  }

  /**
   * 数据加载 - 必需实现
   */
  async loadModuleData() {
    console.log('📊 DisplayManager: Loading display data...');
    
    try {
      this.state.loading = true;
      
      // 初始化仪表盘组件
      this.initializeDashboardWidgets();
      
      // 加载显示配置
      await this.loadDisplayConfig();
      
      // 开始实时数据更新
      this.startRealtimeUpdates();
      
      // 渲染仪表盘
      this.renderDashboard();
      
      this.handleSuccess('显示管理器初始化完成', 'Load display data');
      
    } catch (error) {
      this.handleError(error, 'Load display data');
    } finally {
      this.state.loading = false;
    }
  }

  /**
   * 创建显示管理界面
   */
  createDisplayManagerUI() {
    const container = this.getElement('container');
    if (!container) return;
    
    container.innerHTML = `
      <div class="display-manager">
        <!-- 显示控制栏 -->
        <div class="display-controls">
          <div class="controls-left">
            <div class="display-status">
              <span class="status-indicator" id="display-status-indicator"></span>
              <span class="status-text">实时显示</span>
            </div>
            <div class="last-update">
              最后更新: <span id="last-update-time">--:--:--</span>
            </div>
          </div>
          <div class="controls-right">
            <button class="btn btn-secondary" id="refresh-display">
              <span class="icon">🔄</span>
              刷新
            </button>
            <button class="btn btn-secondary" id="layout-toggle">
              <span class="icon">📊</span>
              布局
            </button>
            <button class="btn btn-secondary" id="fullscreen-toggle">
              <span class="icon">⛶</span>
              全屏
            </button>
          </div>
        </div>
        
        <!-- 仪表盘网格 -->
        <div class="dashboard-grid" id="dashboard-grid">
          <!-- 系统概览卡片 -->
          <div class="widget-card system-overview" data-widget="system-overview">
            <div class="widget-header">
              <h3>系统概览</h3>
              <div class="widget-actions">
                <button class="widget-btn" data-action="refresh-widget">🔄</button>
                <button class="widget-btn" data-action="configure-widget">⚙️</button>
              </div>
            </div>
            <div class="widget-content" id="system-overview-content">
              <!-- 系统概览内容 -->
            </div>
          </div>
          
          <!-- 实时活动卡片 -->
          <div class="widget-card realtime-activity" data-widget="realtime-activity">
            <div class="widget-header">
              <h3>实时活动</h3>
              <div class="widget-actions">
                <button class="widget-btn" data-action="refresh-widget">🔄</button>
                <button class="widget-btn" data-action="configure-widget">⚙️</button>
              </div>
            </div>
            <div class="widget-content" id="realtime-activity-content">
              <!-- 实时活动内容 -->
            </div>
          </div>
          
          <!-- 性能监控卡片 -->
          <div class="widget-card performance-monitor" data-widget="performance-monitor">
            <div class="widget-header">
              <h3>性能监控</h3>
              <div class="widget-actions">
                <button class="widget-btn" data-action="refresh-widget">🔄</button>
                <button class="widget-btn" data-action="configure-widget">⚙️</button>
              </div>
            </div>
            <div class="widget-content" id="performance-monitor-content">
              <!-- 性能监控内容 -->
            </div>
          </div>
          
          <!-- 模块状态卡片 -->
          <div class="widget-card module-status" data-widget="module-status">
            <div class="widget-header">
              <h3>模块状态</h3>
              <div class="widget-actions">
                <button class="widget-btn" data-action="refresh-widget">🔄</button>
                <button class="widget-btn" data-action="configure-widget">⚙️</button>
              </div>
            </div>
            <div class="widget-content" id="module-status-content">
              <!-- 模块状态内容 -->
            </div>
          </div>
          
          <!-- 警报面板卡片 -->
          <div class="widget-card alerts-panel" data-widget="alerts-panel">
            <div class="widget-header">
              <h3>系统警报</h3>
              <div class="widget-actions">
                <button class="widget-btn" data-action="clear-alerts">🗑️</button>
                <button class="widget-btn" data-action="configure-widget">⚙️</button>
              </div>
            </div>
            <div class="widget-content" id="alerts-panel-content">
              <!-- 警报面板内容 -->
            </div>
          </div>
          
          <!-- 统计数据卡片 -->
          <div class="widget-card statistics-panel" data-widget="statistics-panel">
            <div class="widget-header">
              <h3>统计数据</h3>
              <div class="widget-actions">
                <button class="widget-btn" data-action="refresh-widget">🔄</button>
                <button class="widget-btn" data-action="configure-widget">⚙️</button>
              </div>
            </div>
            <div class="widget-content" id="statistics-panel-content">
              <!-- 统计数据内容 -->
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 设置事件委托
   */
  setupEventDelegation() {
    const container = this.getElement('container');
    if (!container) return;
    
    // 使用事件委托处理所有点击事件
    container.addEventListener('click', (event) => {
      const target = event.target.closest('[data-action]');
      if (!target) return;
      
      const action = target.dataset.action;
      const widgetElement = target.closest('[data-widget]');
      const widgetId = widgetElement ? widgetElement.dataset.widget : null;
      
      switch (action) {
        case 'refresh-widget':
          this.refreshWidget(widgetId);
          break;
        case 'configure-widget':
          this.configureWidget(widgetId);
          break;
        case 'clear-alerts':
          this.clearAlerts();
          break;
      }
    });
    
    // 刷新显示按钮
    const refreshBtn = container.querySelector('#refresh-display');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.refreshAllWidgets();
      });
    }
    
    // 布局切换按钮
    const layoutBtn = container.querySelector('#layout-toggle');
    if (layoutBtn) {
      layoutBtn.addEventListener('click', () => {
        this.toggleLayout();
      });
    }
    
    // 全屏切换按钮
    const fullscreenBtn = container.querySelector('#fullscreen-toggle');
    if (fullscreenBtn) {
      fullscreenBtn.addEventListener('click', () => {
        this.toggleFullscreen();
      });
    }
    
    // 布局按钮（从外部）
    const layoutToggleBtn = this.getElement('layoutBtn');
    if (layoutToggleBtn) {
      layoutToggleBtn.addEventListener('click', () => {
        this.toggleLayout();
      });
    }
  }

  /**
   * 初始化仪表盘组件
   */
  initializeDashboardWidgets() {
    const widgets = [
      'system-overview',
      'realtime-activity', 
      'performance-monitor',
      'module-status',
      'alerts-panel',
      'statistics-panel'
    ];
    
    widgets.forEach(widgetId => {
      this.dashboardWidgets.set(widgetId, {
        id: widgetId,
        type: this.getWidgetType(widgetId),
        enabled: true,
        lastUpdate: 0,
        data: {}
      });
      this.displayState.activeWidgets.add(widgetId);
    });
    
    console.log('✅ Dashboard widgets initialized');
  }

  /**
   * 获取组件类型
   */
  getWidgetType(widgetId) {
    const typeMap = {
      'system-overview': 'status',
      'realtime-activity': 'chart',
      'performance-monitor': 'gauge',
      'module-status': 'status',
      'alerts-panel': 'alert',
      'statistics-panel': 'counter'
    };
    return typeMap[widgetId] || 'status';
  }

  /**
   * 开始实时更新
   */
  startRealtimeUpdates() {
    if (this.displayState.updateTimer) {
      clearInterval(this.displayState.updateTimer);
    }
    
    this.displayState.isActive = true;
    this.displayState.updateTimer = setInterval(() => {
      this.updateRealtimeData();
    }, this.displayConfig.refreshInterval);
    
    console.log('📊 Realtime updates started');
  }

  /**
   * 停止实时更新
   */
  stopRealtimeUpdates() {
    if (this.displayState.updateTimer) {
      clearInterval(this.displayState.updateTimer);
      this.displayState.updateTimer = null;
    }
    
    this.displayState.isActive = false;
    console.log('📊 Realtime updates stopped');
  }

  /**
   * 渲染仪表盘
   */
  renderDashboard() {
    this.renderSystemOverview();
    this.renderRealtimeActivity();
    this.renderPerformanceMonitor();
    this.renderModuleStatus();
    this.renderAlertsPanel();
    this.renderStatisticsPanel();
    
    this.updateDisplayStatus();
  }

  /**
   * 渲染系统概览
   */
  renderSystemOverview() {
    const content = document.getElementById('system-overview-content');
    if (!content) return;
    
    const systemData = this.displayData.systemStatus;
    
    content.innerHTML = `
      <div class="overview-grid">
        <div class="overview-item">
          <div class="item-icon">🔌</div>
          <div class="item-info">
            <div class="item-label">连接状态</div>
            <div class="item-value ${systemData.connected ? 'connected' : 'disconnected'}">
              ${systemData.connected ? '已连接' : '未连接'}
            </div>
          </div>
        </div>
        
        <div class="overview-item">
          <div class="item-icon">⏱️</div>
          <div class="item-info">
            <div class="item-label">运行时间</div>
            <div class="item-value">${this.formatUptime(systemData.uptime || 0)}</div>
          </div>
        </div>
        
        <div class="overview-item">
          <div class="item-icon">💾</div>
          <div class="item-info">
            <div class="item-label">内存使用</div>
            <div class="item-value">${this.formatMemoryUsage(systemData.memoryUsage || 0)}%</div>
          </div>
        </div>
        
        <div class="overview-item">
          <div class="item-icon">🌡️</div>
          <div class="item-info">
            <div class="item-label">芯片温度</div>
            <div class="item-value">${systemData.temperature || '--'}°C</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染实时活动
   */
  renderRealtimeActivity() {
    const content = document.getElementById('realtime-activity-content');
    if (!content) return;
    
    const realtimeData = this.displayData.realtimeData;
    
    content.innerHTML = `
      <div class="activity-list">
        <div class="activity-item">
          <div class="activity-time">${this.formatTime(Date.now())}</div>
          <div class="activity-type signal">信号发送</div>
          <div class="activity-desc">最近发送: ${realtimeData.lastSignal || '无'}</div>
        </div>
        
        <div class="activity-item">
          <div class="activity-time">${this.formatTime(Date.now() - 30000)}</div>
          <div class="activity-type timer">定时器</div>
          <div class="activity-desc">活跃定时器: ${realtimeData.activeTimers || 0} 个</div>
        </div>
        
        <div class="activity-item">
          <div class="activity-time">${this.formatTime(Date.now() - 60000)}</div>
          <div class="activity-type system">系统</div>
          <div class="activity-desc">状态检查完成</div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染性能监控
   */
  renderPerformanceMonitor() {
    const content = document.getElementById('performance-monitor-content');
    if (!content) return;
    
    const perfData = this.displayData.systemStatus;
    
    content.innerHTML = `
      <div class="performance-gauges">
        <div class="gauge-item">
          <div class="gauge-label">CPU使用率</div>
          <div class="gauge-circle">
            <div class="gauge-value">${perfData.cpuUsage || 0}%</div>
          </div>
        </div>
        
        <div class="gauge-item">
          <div class="gauge-label">内存使用率</div>
          <div class="gauge-circle">
            <div class="gauge-value">${perfData.memoryUsage || 0}%</div>
          </div>
        </div>
        
        <div class="gauge-item">
          <div class="gauge-label">网络质量</div>
          <div class="gauge-circle">
            <div class="gauge-value">${this.calculateNetworkQuality(perfData.wifiSignal)}%</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染模块状态
   */
  renderModuleStatus() {
    const content = document.getElementById('module-status-content');
    if (!content) return;
    
    const modules = [
      { name: 'SignalManager', status: 'ready', label: '信号管理' },
      { name: 'ControlCenter', status: 'ready', label: '控制中心' },
      { name: 'TimerManager', status: 'ready', label: '定时器管理' },
      { name: 'SystemMonitor', status: 'ready', label: '系统监控' },
      { name: 'OTAManager', status: 'ready', label: 'OTA升级' },
      { name: 'ConfigManager', status: 'ready', label: '配置管理' },
      { name: 'DisplayManager', status: 'ready', label: '显示管理' }
    ];
    
    content.innerHTML = `
      <div class="module-list">
        ${modules.map(module => `
          <div class="module-item">
            <div class="module-status ${module.status}"></div>
            <div class="module-name">${module.label}</div>
            <div class="module-indicator">
              ${module.status === 'ready' ? '✅' : module.status === 'error' ? '❌' : '⏳'}
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  /**
   * 渲染警报面板
   */
  renderAlertsPanel() {
    const content = document.getElementById('alerts-panel-content');
    if (!content) return;
    
    if (this.displayData.alerts.length === 0) {
      content.innerHTML = `
        <div class="no-alerts">
          <div class="no-alerts-icon">✅</div>
          <div class="no-alerts-text">暂无警报</div>
        </div>
      `;
      return;
    }
    
    content.innerHTML = `
      <div class="alerts-list">
        ${this.displayData.alerts.slice(0, 5).map(alert => `
          <div class="alert-item ${alert.level}">
            <div class="alert-time">${this.formatTime(alert.timestamp)}</div>
            <div class="alert-message">${alert.message}</div>
            <div class="alert-source">${alert.source}</div>
          </div>
        `).join('')}
      </div>
    `;
  }

  /**
   * 渲染统计数据
   */
  renderStatisticsPanel() {
    const content = document.getElementById('statistics-panel-content');
    if (!content) return;
    
    const stats = this.displayData.statistics;
    
    content.innerHTML = `
      <div class="statistics-grid">
        <div class="stat-item">
          <div class="stat-value">${stats.totalSignals || 0}</div>
          <div class="stat-label">总信号数</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value">${stats.totalTimers || 0}</div>
          <div class="stat-label">定时器数</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value">${stats.todayEmits || 0}</div>
          <div class="stat-label">今日发送</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value">${stats.systemErrors || 0}</div>
          <div class="stat-label">系统错误</div>
        </div>
      </div>
    `;
  }

  /**
   * 更新系统状态
   */
  updateSystemStatus(data) {
    this.displayData.systemStatus = { ...this.displayData.systemStatus, ...data };
    this.renderSystemOverview();
    this.renderPerformanceMonitor();
    this.updateDisplayStatus();
  }

  /**
   * 更新实时数据
   */
  updateRealtimeData(data) {
    if (data) {
      this.displayData.realtimeData = { ...this.displayData.realtimeData, ...data };
    }
    
    this.displayState.lastUpdateTime = Date.now();
    this.renderRealtimeActivity();
    
    // 更新最后更新时间显示
    const lastUpdateElement = document.getElementById('last-update-time');
    if (lastUpdateElement) {
      lastUpdateElement.textContent = this.formatTime(this.displayState.lastUpdateTime);
    }
  }

  /**
   * 添加警报
   */
  addAlert(level, message, source) {
    const alert = {
      id: `alert_${Date.now()}`,
      level: level,
      message: message,
      source: source || 'System',
      timestamp: Date.now()
    };
    
    this.displayData.alerts.unshift(alert);
    
    // 限制警报数量
    if (this.displayData.alerts.length > 50) {
      this.displayData.alerts = this.displayData.alerts.slice(0, 50);
    }
    
    this.renderAlertsPanel();
  }

  /**
   * 清空警报
   */
  clearAlerts() {
    this.displayData.alerts = [];
    this.renderAlertsPanel();
    this.handleSuccess('警报已清空', 'Clear alerts');
  }

  /**
   * 刷新所有组件
   */
  refreshAllWidgets() {
    this.renderDashboard();
    this.handleSuccess('仪表盘已刷新', 'Refresh dashboard');
  }

  /**
   * 刷新单个组件
   */
  refreshWidget(widgetId) {
    if (!widgetId) return;
    
    const widget = this.dashboardWidgets.get(widgetId);
    if (widget) {
      widget.lastUpdate = Date.now();
      
      switch (widgetId) {
        case 'system-overview':
          this.renderSystemOverview();
          break;
        case 'realtime-activity':
          this.renderRealtimeActivity();
          break;
        case 'performance-monitor':
          this.renderPerformanceMonitor();
          break;
        case 'module-status':
          this.renderModuleStatus();
          break;
        case 'alerts-panel':
          this.renderAlertsPanel();
          break;
        case 'statistics-panel':
          this.renderStatisticsPanel();
          break;
      }
    }
  }

  /**
   * 更新显示状态
   */
  updateDisplayStatus() {
    const indicator = document.getElementById('display-status-indicator');
    if (indicator) {
      indicator.className = `status-indicator ${this.displayState.isActive ? 'active' : 'inactive'}`;
    }
  }

  /**
   * 格式化运行时间
   */
  formatUptime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }

  /**
   * 格式化内存使用率
   */
  formatMemoryUsage(usage) {
    return Math.round(usage);
  }

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * 计算网络质量
   */
  calculateNetworkQuality(signalStrength) {
    if (!signalStrength) return 0;
    // WiFi信号强度转换为质量百分比
    return Math.max(0, Math.min(100, (signalStrength + 100) * 2));
  }

  /**
   * 切换布局
   */
  toggleLayout() {
    const grid = document.getElementById('dashboard-grid');
    if (grid) {
      grid.classList.toggle('compact-layout');
      this.displayConfig.layout = grid.classList.contains('compact-layout') ? 'compact' : 'dashboard';
    }
  }

  /**
   * 切换全屏
   */
  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }

  /**
   * 销毁模块时清理资源
   */
  destroy() {
    // 停止实时更新
    this.stopRealtimeUpdates();
    
    // 调用父类销毁方法
    super.destroy();
  }
}

// 导出DisplayManager类
window.DisplayManager = DisplayManager;
