/**
 * R1系统 - OTAManager固件升级管理模块
 * 基于：R1前端系统架构标准文档.md BaseModule架构标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md OTA管理API
 * 
 * 功能特性：
 * - 固件版本检查
 * - 固件升级管理
 * - 文件系统更新
 * - 升级进度监控
 * - 安全验证机制
 */

class OTAManager extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'OTAManager');
    
    // OTA状态数据
    this.otaStatus = {
      currentVersion: '',
      latestVersion: '',
      updateAvailable: false,
      isUpdating: false,
      lastCheckTime: 0,
      updateProgress: 0
    };
    
    // 升级配置
    this.otaConfig = {
      autoCheck: true,
      checkInterval: 3600000, // 1小时
      requireAuth: true,
      backupBeforeUpdate: true
    };
    
    // 升级状态
    this.updateState = {
      type: null, // 'firmware' | 'filesystem'
      stage: null, // 'preparing' | 'downloading' | 'installing' | 'verifying' | 'complete'
      progress: 0,
      startTime: 0,
      estimatedTime: 0,
      error: null
    };
    
    // 认证状态
    this.authState = {
      isAuthenticated: false,
      sessionToken: null,
      expiresAt: 0
    };
    
    console.log('✅ OTAManager constructed');
  }

  /**
   * 事件监听器设置 - 必需实现
   */
  async setupEventListeners() {
    await super.setupEventListeners();
    
    // 监听系统更新检查请求
    this.eventBus.on('system.update.check', () => {
      this.checkForUpdates();
    });
    
    // 监听更新进度事件
    this.eventBus.on('ota.progress', (data) => {
      this.handleUpdateProgress(data);
    });
    
    // 监听更新完成事件
    this.eventBus.on('ota.complete', (data) => {
      this.handleUpdateComplete(data);
    });
    
    // 监听更新错误事件
    this.eventBus.on('ota.error', (data) => {
      this.handleUpdateError(data);
    });
    
    console.log('📡 OTAManager: Event listeners setup complete');
  }

  /**
   * UI设置 - 必需实现
   */
  async setupUI() {
    console.log('🎨 OTAManager: Setting up UI...');
    
    // 缓存UI元素
    this.cacheElement('container', '#ota-manager-content');
    this.cacheElement('checkUpdateBtn', '#check-update-btn');
    
    // 创建OTA管理界面
    this.createOTAManagerUI();
    
    // 设置事件委托
    this.setupEventDelegation();
    
    console.log('✅ OTAManager: UI setup complete');
  }

  /**
   * 数据加载 - 必需实现
   */
  async loadModuleData() {
    console.log('📊 OTAManager: Loading OTA data...');
    
    try {
      this.state.loading = true;
      
      // 加载OTA状态
      await this.loadOTAStatus();
      
      // 渲染OTA界面
      this.renderOTAInterface();
      
      // 启动自动检查（如果启用）
      if (this.otaConfig.autoCheck) {
        this.startAutoCheck();
      }
      
      this.handleSuccess('OTA管理器初始化完成', 'Load OTA data');
      
    } catch (error) {
      this.handleError(error, 'Load OTA data');
    } finally {
      this.state.loading = false;
    }
  }

  /**
   * 创建OTA管理界面
   */
  createOTAManagerUI() {
    const container = this.getElement('container');
    if (!container) return;
    
    container.innerHTML = `
      <div class="ota-manager">
        <!-- OTA状态面板 -->
        <div class="ota-panel">
          <div class="panel-header">
            <h3>固件信息</h3>
            <div class="version-status" id="version-status">
              <span class="status-indicator" id="update-indicator"></span>
            </div>
          </div>
          <div class="panel-content">
            <div class="version-info" id="version-info">
              <!-- 版本信息将动态生成 -->
            </div>
          </div>
        </div>
        
        <!-- 更新控制面板 -->
        <div class="ota-panel">
          <div class="panel-header">
            <h3>更新管理</h3>
            <button class="btn btn-secondary" id="refresh-status">刷新状态</button>
          </div>
          <div class="panel-content">
            <div class="update-controls" id="update-controls">
              <!-- 更新控制将动态生成 -->
            </div>
            
            <!-- 更新进度 -->
            <div class="update-progress" id="update-progress" style="display: none;">
              <div class="progress-header">
                <h4 id="progress-title">准备更新...</h4>
                <span id="progress-percentage">0%</span>
              </div>
              <div class="progress-bar-container">
                <div class="progress-bar" id="progress-bar"></div>
              </div>
              <div class="progress-details">
                <div class="progress-stage" id="progress-stage">准备中...</div>
                <div class="progress-time" id="progress-time">预计时间: 计算中...</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 更新历史面板 -->
        <div class="ota-panel">
          <div class="panel-header">
            <h3>更新历史</h3>
            <button class="btn btn-secondary" id="clear-history">清空历史</button>
          </div>
          <div class="panel-content">
            <div class="update-history" id="update-history">
              <!-- 更新历史将动态生成 -->
            </div>
          </div>
        </div>
        
        <!-- 设置面板 -->
        <div class="ota-panel">
          <div class="panel-header">
            <h3>OTA设置</h3>
          </div>
          <div class="panel-content">
            <div class="ota-settings">
              <div class="setting-item">
                <label class="setting-label">
                  <input type="checkbox" id="auto-check" ${this.otaConfig.autoCheck ? 'checked' : ''}>
                  <span class="setting-text">自动检查更新</span>
                </label>
              </div>
              <div class="setting-item">
                <label class="setting-label">
                  <input type="checkbox" id="require-auth" ${this.otaConfig.requireAuth ? 'checked' : ''}>
                  <span class="setting-text">更新前需要认证</span>
                </label>
              </div>
              <div class="setting-item">
                <label class="setting-label">
                  <input type="checkbox" id="backup-before-update" ${this.otaConfig.backupBeforeUpdate ? 'checked' : ''}>
                  <span class="setting-text">更新前自动备份</span>
                </label>
              </div>
              <div class="setting-item">
                <label class="setting-label">检查间隔:</label>
                <select id="check-interval">
                  <option value="1800000" ${this.otaConfig.checkInterval === 1800000 ? 'selected' : ''}>30分钟</option>
                  <option value="3600000" ${this.otaConfig.checkInterval === 3600000 ? 'selected' : ''}>1小时</option>
                  <option value="7200000" ${this.otaConfig.checkInterval === 7200000 ? 'selected' : ''}>2小时</option>
                  <option value="21600000" ${this.otaConfig.checkInterval === 21600000 ? 'selected' : ''}>6小时</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 认证对话框 -->
        <div class="modal" id="auth-modal" style="display: none;">
          <div class="modal-content">
            <div class="modal-header">
              <h3>OTA认证</h3>
              <button class="modal-close" id="auth-modal-close">×</button>
            </div>
            <div class="modal-body">
              <div class="auth-form">
                <div class="form-group">
                  <label for="auth-password">管理员密码</label>
                  <input type="password" id="auth-password" class="form-input" placeholder="请输入管理员密码">
                </div>
                <div class="auth-warning">
                  <span class="warning-icon">⚠️</span>
                  <span class="warning-text">固件更新过程中请勿断电或重启设备</span>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button class="btn btn-secondary" id="auth-cancel">取消</button>
              <button class="btn btn-primary" id="auth-confirm">确认更新</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 设置事件委托
   */
  setupEventDelegation() {
    const container = this.getElement('container');
    if (!container) return;
    
    // 使用事件委托处理所有点击事件
    container.addEventListener('click', (event) => {
      const target = event.target.closest('[data-action]');
      if (!target) return;
      
      const action = target.dataset.action;
      const updateType = target.dataset.updateType;
      
      switch (action) {
        case 'check-updates':
          this.checkForUpdates();
          break;
        case 'start-update':
          this.startUpdate(updateType);
          break;
        case 'cancel-update':
          this.cancelUpdate();
          break;
        case 'download-firmware':
          this.downloadFirmware();
          break;
      }
    });
    
    // 检查更新按钮
    const checkUpdateBtn = this.getElement('checkUpdateBtn');
    if (checkUpdateBtn) {
      checkUpdateBtn.addEventListener('click', () => {
        this.checkForUpdates();
      });
    }
    
    // 刷新状态按钮
    const refreshBtn = container.querySelector('#refresh-status');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.loadOTAStatus();
      });
    }
    
    // 设置变化监听
    const autoCheckbox = container.querySelector('#auto-check');
    const authCheckbox = container.querySelector('#require-auth');
    const backupCheckbox = container.querySelector('#backup-before-update');
    const intervalSelect = container.querySelector('#check-interval');
    
    if (autoCheckbox) {
      autoCheckbox.addEventListener('change', (event) => {
        this.updateSetting('autoCheck', event.target.checked);
      });
    }
    
    if (authCheckbox) {
      authCheckbox.addEventListener('change', (event) => {
        this.updateSetting('requireAuth', event.target.checked);
      });
    }
    
    if (backupCheckbox) {
      backupCheckbox.addEventListener('change', (event) => {
        this.updateSetting('backupBeforeUpdate', event.target.checked);
      });
    }
    
    if (intervalSelect) {
      intervalSelect.addEventListener('change', (event) => {
        this.updateSetting('checkInterval', parseInt(event.target.value));
      });
    }
    
    // 认证对话框事件
    const authModal = container.querySelector('#auth-modal');
    if (authModal) {
      const closeBtn = authModal.querySelector('#auth-modal-close');
      const cancelBtn = authModal.querySelector('#auth-cancel');
      const confirmBtn = authModal.querySelector('#auth-confirm');
      
      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          this.hideAuthModal();
        });
      }
      
      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
          this.hideAuthModal();
        });
      }
      
      if (confirmBtn) {
        confirmBtn.addEventListener('click', () => {
          this.performAuthentication();
        });
      }
    }
  }

  /**
   * 加载OTA状态
   */
  async loadOTAStatus() {
    try {
      const response = await this.requestESP32('/api/ota/status', {
        method: 'GET'
      });
      
      if (response.success && response.data) {
        this.otaStatus = {
          ...this.otaStatus,
          ...response.data,
          lastCheckTime: Date.now()
        };
        
        this.renderVersionInfo();
        this.renderUpdateControls();
      }
    } catch (error) {
      this.handleError(error, 'Load OTA status');
    }
  }

  /**
   * 检查更新
   */
  async checkForUpdates() {
    try {
      this.state.loading = true;
      
      const response = await this.requestESP32('/api/ota/status', {
        method: 'GET'
      });
      
      if (response.success && response.data) {
        this.otaStatus = {
          ...this.otaStatus,
          ...response.data,
          lastCheckTime: Date.now()
        };
        
        this.renderVersionInfo();
        this.renderUpdateControls();
        
        if (this.otaStatus.updateAvailable) {
          this.handleSuccess('发现新版本可用', 'Check updates');
        } else {
          this.handleSuccess('当前已是最新版本', 'Check updates');
        }
      }
      
    } catch (error) {
      this.handleError(error, 'Check updates');
    } finally {
      this.state.loading = false;
    }
  }

  /**
   * 开始更新
   */
  async startUpdate(updateType) {
    if (this.otaConfig.requireAuth && !this.authState.isAuthenticated) {
      this.showAuthModal(updateType);
      return;
    }
    
    try {
      this.updateState = {
        type: updateType,
        stage: 'preparing',
        progress: 0,
        startTime: Date.now(),
        estimatedTime: 0,
        error: null
      };
      
      this.otaStatus.isUpdating = true;
      this.showUpdateProgress();
      
      const endpoint = updateType === 'firmware' ? '/api/ota/firmware' : '/api/ota/filesystem';
      
      const response = await this.requestESP32(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          backup: this.otaConfig.backupBeforeUpdate
        })
      });
      
      if (response.success) {
        this.handleSuccess(`${updateType === 'firmware' ? '固件' : '文件系统'}更新已开始`, 'Start update');
      }
      
    } catch (error) {
      this.handleError(error, 'Start update');
      this.hideUpdateProgress();
    }
  }

  /**
   * 渲染版本信息
   */
  renderVersionInfo() {
    const versionInfo = document.getElementById('version-info');
    const updateIndicator = document.getElementById('update-indicator');
    
    if (!versionInfo) return;
    
    // 更新状态指示器
    if (updateIndicator) {
      if (this.otaStatus.updateAvailable) {
        updateIndicator.className = 'status-indicator update-available';
        updateIndicator.title = '有新版本可用';
      } else {
        updateIndicator.className = 'status-indicator up-to-date';
        updateIndicator.title = '已是最新版本';
      }
    }
    
    versionInfo.innerHTML = `
      <div class="version-grid">
        <div class="version-item">
          <div class="version-label">当前版本</div>
          <div class="version-value">${this.otaStatus.currentVersion || '未知'}</div>
        </div>
        <div class="version-item">
          <div class="version-label">最新版本</div>
          <div class="version-value">${this.otaStatus.latestVersion || '检查中...'}</div>
        </div>
        <div class="version-item">
          <div class="version-label">最后检查</div>
          <div class="version-value">${this.formatLastCheck()}</div>
        </div>
        <div class="version-item">
          <div class="version-label">更新状态</div>
          <div class="version-value ${this.otaStatus.updateAvailable ? 'update-available' : 'up-to-date'}">
            ${this.otaStatus.updateAvailable ? '有更新可用' : '已是最新'}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染更新控制
   */
  renderUpdateControls() {
    const updateControls = document.getElementById('update-controls');
    if (!updateControls) return;
    
    if (this.otaStatus.isUpdating) {
      updateControls.innerHTML = `
        <div class="updating-notice">
          <span class="updating-icon">🔄</span>
          <span class="updating-text">更新进行中，请勿断电...</span>
          <button class="btn btn-danger" data-action="cancel-update">取消更新</button>
        </div>
      `;
    } else if (this.otaStatus.updateAvailable) {
      updateControls.innerHTML = `
        <div class="update-available">
          <div class="update-notice">
            <span class="update-icon">🆕</span>
            <span class="update-text">发现新版本 ${this.otaStatus.latestVersion}</span>
          </div>
          <div class="update-actions">
            <button class="btn btn-primary" data-action="start-update" data-update-type="firmware">
              <span class="icon">⬆️</span>
              更新固件
            </button>
            <button class="btn btn-secondary" data-action="start-update" data-update-type="filesystem">
              <span class="icon">📁</span>
              更新文件系统
            </button>
            <button class="btn btn-secondary" data-action="download-firmware">
              <span class="icon">⬇️</span>
              下载固件
            </button>
          </div>
        </div>
      `;
    } else {
      updateControls.innerHTML = `
        <div class="up-to-date">
          <div class="up-to-date-notice">
            <span class="check-icon">✅</span>
            <span class="check-text">当前已是最新版本</span>
          </div>
          <div class="check-actions">
            <button class="btn btn-secondary" data-action="check-updates">
              <span class="icon">🔍</span>
              检查更新
            </button>
          </div>
        </div>
      `;
    }
  }

  /**
   * 显示更新进度
   */
  showUpdateProgress() {
    const progressContainer = document.getElementById('update-progress');
    if (progressContainer) {
      progressContainer.style.display = 'block';
      this.updateProgressDisplay();
    }
  }

  /**
   * 隐藏更新进度
   */
  hideUpdateProgress() {
    const progressContainer = document.getElementById('update-progress');
    if (progressContainer) {
      progressContainer.style.display = 'none';
    }
    
    this.otaStatus.isUpdating = false;
    this.renderUpdateControls();
  }

  /**
   * 更新进度显示
   */
  updateProgressDisplay() {
    const titleElement = document.getElementById('progress-title');
    const percentageElement = document.getElementById('progress-percentage');
    const progressBar = document.getElementById('progress-bar');
    const stageElement = document.getElementById('progress-stage');
    const timeElement = document.getElementById('progress-time');
    
    if (titleElement) {
      const typeText = this.updateState.type === 'firmware' ? '固件' : '文件系统';
      titleElement.textContent = `${typeText}更新进行中`;
    }
    
    if (percentageElement) {
      percentageElement.textContent = `${this.updateState.progress}%`;
    }
    
    if (progressBar) {
      progressBar.style.width = `${this.updateState.progress}%`;
    }
    
    if (stageElement) {
      const stageTexts = {
        'preparing': '准备更新...',
        'downloading': '下载更新包...',
        'installing': '安装更新...',
        'verifying': '验证更新...',
        'complete': '更新完成'
      };
      stageElement.textContent = stageTexts[this.updateState.stage] || '处理中...';
    }
    
    if (timeElement && this.updateState.estimatedTime > 0) {
      const remaining = Math.max(0, this.updateState.estimatedTime - (Date.now() - this.updateState.startTime));
      timeElement.textContent = `预计剩余: ${this.formatDuration(remaining)}`;
    }
  }

  /**
   * 处理更新进度
   */
  handleUpdateProgress(data) {
    this.updateState.progress = data.progress || 0;
    this.updateState.stage = data.stage || this.updateState.stage;
    this.updateState.estimatedTime = data.estimatedTime || this.updateState.estimatedTime;
    
    this.updateProgressDisplay();
  }

  /**
   * 处理更新完成
   */
  handleUpdateComplete(data) {
    this.updateState.stage = 'complete';
    this.updateState.progress = 100;
    
    this.updateProgressDisplay();
    
    setTimeout(() => {
      this.hideUpdateProgress();
      this.loadOTAStatus();
    }, 2000);
    
    this.handleSuccess('更新完成，系统将重启', 'Update complete');
  }

  /**
   * 处理更新错误
   */
  handleUpdateError(data) {
    this.updateState.error = data.error;
    this.hideUpdateProgress();
    this.handleError(new Error(data.error), 'Update error');
  }

  /**
   * 显示认证对话框
   */
  showAuthModal(updateType) {
    this.pendingUpdateType = updateType;
    
    const authModal = document.getElementById('auth-modal');
    if (authModal) {
      authModal.style.display = 'flex';
      
      const passwordInput = authModal.querySelector('#auth-password');
      if (passwordInput) {
        passwordInput.focus();
      }
    }
  }

  /**
   * 隐藏认证对话框
   */
  hideAuthModal() {
    const authModal = document.getElementById('auth-modal');
    if (authModal) {
      authModal.style.display = 'none';
      
      const passwordInput = authModal.querySelector('#auth-password');
      if (passwordInput) {
        passwordInput.value = '';
      }
    }
    
    this.pendingUpdateType = null;
  }

  /**
   * 执行认证
   */
  async performAuthentication() {
    const passwordInput = document.getElementById('auth-password');
    if (!passwordInput || !passwordInput.value) {
      this.handleError(new Error('请输入密码'), 'Authentication');
      return;
    }
    
    try {
      const response = await this.requestESP32('/api/ota/login', {
        method: 'POST',
        body: JSON.stringify({
          password: passwordInput.value
        })
      });
      
      if (response.success && response.data.token) {
        this.authState = {
          isAuthenticated: true,
          sessionToken: response.data.token,
          expiresAt: Date.now() + (response.data.expiresIn * 1000)
        };
        
        this.hideAuthModal();
        this.handleSuccess('认证成功', 'Authentication');
        
        // 继续执行更新
        if (this.pendingUpdateType) {
          this.startUpdate(this.pendingUpdateType);
        }
      }
      
    } catch (error) {
      this.handleError(error, 'Authentication');
    }
  }

  /**
   * 更新设置
   */
  updateSetting(key, value) {
    this.otaConfig[key] = value;
    
    // 如果是自动检查设置变化
    if (key === 'autoCheck') {
      if (value) {
        this.startAutoCheck();
      } else {
        this.stopAutoCheck();
      }
    }
    
    // 如果是检查间隔变化
    if (key === 'checkInterval') {
      if (this.otaConfig.autoCheck) {
        this.stopAutoCheck();
        this.startAutoCheck();
      }
    }
    
    console.log(`✅ OTA setting updated: ${key} = ${value}`);
  }

  /**
   * 启动自动检查
   */
  startAutoCheck() {
    if (this.autoCheckTimer) {
      clearInterval(this.autoCheckTimer);
    }
    
    this.autoCheckTimer = setInterval(() => {
      this.checkForUpdates();
    }, this.otaConfig.checkInterval);
    
    console.log('📅 OTA auto check started');
  }

  /**
   * 停止自动检查
   */
  stopAutoCheck() {
    if (this.autoCheckTimer) {
      clearInterval(this.autoCheckTimer);
      this.autoCheckTimer = null;
    }
    
    console.log('📅 OTA auto check stopped');
  }

  /**
   * 格式化最后检查时间
   */
  formatLastCheck() {
    if (!this.otaStatus.lastCheckTime) {
      return '从未检查';
    }
    
    const now = Date.now();
    const diff = now - this.otaStatus.lastCheckTime;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    return `${Math.floor(diff / 86400000)}天前`;
  }

  /**
   * 格式化持续时间
   */
  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 渲染OTA界面
   */
  renderOTAInterface() {
    this.renderVersionInfo();
    this.renderUpdateControls();
  }

  /**
   * 销毁模块时清理资源
   */
  destroy() {
    // 停止自动检查
    this.stopAutoCheck();
    
    // 调用父类销毁方法
    super.destroy();
  }
}

// 导出OTAManager类
window.OTAManager = OTAManager;
