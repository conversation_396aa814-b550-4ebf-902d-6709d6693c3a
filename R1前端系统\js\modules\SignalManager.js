/**
 * R1系统 - SignalManager信号管理模块
 * 基于：R1前端系统架构标准文档.md SignalManager参考实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 信号管理API
 * 
 * 功能特性：
 * - 完全解耦：纯事件通信，零直接依赖
 * - 性能优化：直接属性访问，事件委托，渲染缓存
 * - 架构合规：继承BaseModule，实现所有必需方法
 * - 功能完整：信号管理、学习、导入导出、批量操作
 */

class SignalManager extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'SignalManager');
    
    // 业务数据（推荐直接属性，性能更好）
    this.signals = new Map();          // 信号数据
    this.selectedSignals = new Set();  // 选中信号
    this.categories = new Set();       // 信号分类
    
    // 业务状态
    this.currentView = 'grid';         // 当前视图：grid/list
    this.isMultiSelectMode = false;    // 多选模式
    this.searchQuery = '';             // 搜索查询
    this.currentFilter = 'all';        // 当前过滤器
    
    // 信号学习状态
    this.learningState = {
      isLearning: false,
      hasUnsavedSignal: false,
      learnedData: null,
      learningTimeout: null
    };
    
    // UI配置
    this.uiConfig = {
      itemsPerPage: 20,
      currentPage: 1,
      sortBy: 'name',
      sortOrder: 'asc',
      showCategories: true
    };
    
    // 渲染缓存
    this.renderCache = {
      lastRenderTime: 0,
      renderedSignals: new Set(),
      needsFullRender: true
    };
    
    console.log('✅ SignalManager constructed');
  }

  /**
   * 事件监听器设置 - 必需实现
   */
  async setupEventListeners() {
    await super.setupEventListeners();
    
    // 信号相关事件
    this.eventBus.on('signal.learn.start', (data) => {
      this.startSignalLearning(data);
    });
    
    this.eventBus.on('signal.learn.complete', (data) => {
      this.handleLearnedSignal(data);
    });
    
    this.eventBus.on('signal.emit.request', (data) => {
      this.emitSignal(data.signalId);
    });
    
    // 控制中心请求信号列表
    this.eventBus.on('control.signals.request', (data) => {
      this.sendSignalsToControl(data);
    });
    
    // 定时器请求信号列表
    this.eventBus.on('timer.signals.request', (data) => {
      this.sendSignalsToTimer(data);
    });
    
    // 系统级事件
    this.eventBus.on('system.export.request', (data) => {
      if (data.type === 'signals') {
        this.exportSignals();
      }
    });
    
    this.eventBus.on('system.import.request', (data) => {
      if (data.type === 'signals') {
        this.importSignals(data.file);
      }
    });
    
    console.log('📡 SignalManager: Event listeners setup complete');
  }

  /**
   * UI设置 - 必需实现
   */
  async setupUI() {
    console.log('🎨 SignalManager: Setting up UI...');
    
    // 缓存UI元素
    this.cacheElement('container', '#signal-manager-content');
    this.cacheElement('learnBtn', '#learn-signal-btn');
    this.cacheElement('importBtn', '#import-signals-btn');
    this.cacheElement('exportBtn', '#export-signals-btn');
    
    // 创建信号管理界面
    this.createSignalManagerUI();
    
    // 设置事件委托（推荐模式）
    this.setupEventDelegation();
    
    console.log('✅ SignalManager: UI setup complete');
  }

  /**
   * 数据加载 - 必需实现
   */
  async loadModuleData() {
    console.log('📊 SignalManager: Loading signal data...');
    
    try {
      this.state.loading = true;
      
      // 从ESP32加载信号列表
      const response = await this.requestESP32('/api/signals', {
        method: 'GET'
      });
      
      if (response.success && response.data.signals) {
        this.processSignalData(response.data.signals);
        this.handleSuccess(`加载了 ${this.signals.size} 个信号`, 'Load signals');
      }
      
      // 加载信号分类
      await this.loadSignalCategories();
      
      // 渲染信号列表
      this.renderSignalList();
      
    } catch (error) {
      this.handleError(error, 'Load signal data');
    } finally {
      this.state.loading = false;
    }
  }

  /**
   * 创建信号管理界面
   */
  createSignalManagerUI() {
    const container = this.getElement('container');
    if (!container) return;
    
    container.innerHTML = `
      <div class="signal-manager">
        <!-- 工具栏 -->
        <div class="signal-toolbar">
          <div class="toolbar-left">
            <div class="search-box">
              <input type="text" id="signal-search" placeholder="搜索信号..." class="search-input">
              <button class="search-btn" id="search-btn">🔍</button>
            </div>
            <select id="signal-filter" class="filter-select">
              <option value="all">全部信号</option>
              <option value="recent">最近使用</option>
              <option value="favorites">收藏夹</option>
            </select>
          </div>
          <div class="toolbar-right">
            <button class="btn btn-secondary" id="view-toggle">
              <span class="icon">📋</span>
              列表视图
            </button>
            <button class="btn btn-secondary" id="multi-select-toggle">
              <span class="icon">☑️</span>
              多选模式
            </button>
          </div>
        </div>
        
        <!-- 信号分类 -->
        <div class="signal-categories" id="signal-categories">
          <!-- 分类标签将动态生成 -->
        </div>
        
        <!-- 信号列表 -->
        <div class="signal-list-container">
          <div class="signal-list" id="signal-list">
            <!-- 信号项将动态生成 -->
          </div>
          
          <!-- 分页控件 -->
          <div class="pagination" id="signal-pagination">
            <!-- 分页按钮将动态生成 -->
          </div>
        </div>
        
        <!-- 批量操作栏 -->
        <div class="batch-actions" id="batch-actions" style="display: none;">
          <div class="batch-info">
            <span id="selected-count">0</span> 个信号已选中
          </div>
          <div class="batch-buttons">
            <button class="btn btn-primary" id="batch-emit">批量发送</button>
            <button class="btn btn-secondary" id="batch-export">批量导出</button>
            <button class="btn btn-danger" id="batch-delete">批量删除</button>
          </div>
        </div>
      </div>
      
      <!-- 信号学习对话框 -->
      <div class="modal" id="learn-signal-modal" style="display: none;">
        <div class="modal-content">
          <div class="modal-header">
            <h3>学习新信号</h3>
            <button class="modal-close" id="learn-modal-close">×</button>
          </div>
          <div class="modal-body">
            <div class="learn-form">
              <div class="form-group">
                <label for="signal-name">信号名称</label>
                <input type="text" id="signal-name" class="form-input" placeholder="请输入信号名称">
              </div>
              <div class="form-group">
                <label for="signal-category">信号分类</label>
                <select id="signal-category" class="form-select">
                  <option value="">选择分类</option>
                </select>
              </div>
              <div class="learn-status" id="learn-status">
                <div class="status-icon">📡</div>
                <div class="status-text">请按下遥控器按键...</div>
                <div class="status-progress">
                  <div class="progress-bar" id="learn-progress"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn btn-secondary" id="learn-cancel">取消</button>
            <button class="btn btn-primary" id="learn-save" disabled>保存信号</button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 设置事件委托
   */
  setupEventDelegation() {
    const container = this.getElement('container');
    if (!container) return;
    
    // 使用事件委托处理所有点击事件
    container.addEventListener('click', (event) => {
      const target = event.target.closest('[data-action]');
      if (!target) return;
      
      const action = target.dataset.action;
      const signalId = target.dataset.signalId;
      
      switch (action) {
        case 'emit-signal':
          this.emitSignal(signalId);
          break;
        case 'edit-signal':
          this.editSignal(signalId);
          break;
        case 'delete-signal':
          this.deleteSignal(signalId);
          break;
        case 'select-signal':
          this.toggleSignalSelection(signalId);
          break;
        case 'toggle-favorite':
          this.toggleSignalFavorite(signalId);
          break;
      }
    });
    
    // 搜索功能
    const searchInput = container.querySelector('#signal-search');
    if (searchInput) {
      searchInput.addEventListener('input', (event) => {
        this.searchQuery = event.target.value;
        this.debounceSearch();
      });
    }
    
    // 过滤器
    const filterSelect = container.querySelector('#signal-filter');
    if (filterSelect) {
      filterSelect.addEventListener('change', (event) => {
        this.currentFilter = event.target.value;
        this.renderSignalList();
      });
    }
    
    // 视图切换
    const viewToggle = container.querySelector('#view-toggle');
    if (viewToggle) {
      viewToggle.addEventListener('click', () => {
        this.toggleView();
      });
    }
    
    // 多选模式切换
    const multiSelectToggle = container.querySelector('#multi-select-toggle');
    if (multiSelectToggle) {
      multiSelectToggle.addEventListener('click', () => {
        this.toggleMultiSelectMode();
      });
    }
    
    // 学习信号按钮
    const learnBtn = this.getElement('learnBtn');
    if (learnBtn) {
      learnBtn.addEventListener('click', () => {
        this.showLearnSignalModal();
      });
    }
    
    // 导入导出按钮
    const importBtn = this.getElement('importBtn');
    const exportBtn = this.getElement('exportBtn');
    
    if (importBtn) {
      importBtn.addEventListener('click', () => {
        this.showImportDialog();
      });
    }
    
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        this.exportSignals();
      });
    }
  }

  /**
   * 处理信号数据
   */
  processSignalData(signalsArray) {
    this.signals.clear();
    this.categories.clear();
    
    signalsArray.forEach(signal => {
      this.signals.set(signal.id, signal);
      if (signal.category) {
        this.categories.add(signal.category);
      }
    });
    
    // 更新渲染缓存
    this.renderCache.needsFullRender = true;
    
    console.log(`✅ Processed ${this.signals.size} signals, ${this.categories.size} categories`);
  }

  /**
   * 渲染信号列表
   */
  renderSignalList() {
    if (!this.renderCache.needsFullRender && 
        Date.now() - this.renderCache.lastRenderTime < 100) {
      return; // 防止频繁渲染
    }
    
    const signalList = document.getElementById('signal-list');
    if (!signalList) return;
    
    // 获取过滤后的信号
    const filteredSignals = this.getFilteredSignals();
    
    // 分页处理
    const startIndex = (this.uiConfig.currentPage - 1) * this.uiConfig.itemsPerPage;
    const endIndex = startIndex + this.uiConfig.itemsPerPage;
    const pageSignals = filteredSignals.slice(startIndex, endIndex);
    
    // 渲染信号项
    signalList.innerHTML = pageSignals.map(signal => 
      this.renderSignalItem(signal)
    ).join('');
    
    // 渲染分页
    this.renderPagination(filteredSignals.length);
    
    // 更新渲染缓存
    this.renderCache.lastRenderTime = Date.now();
    this.renderCache.needsFullRender = false;
    this.renderCache.renderedSignals = new Set(pageSignals.map(s => s.id));
    
    console.log(`✅ Rendered ${pageSignals.length} signals`);
  }

  /**
   * 渲染单个信号项
   */
  renderSignalItem(signal) {
    const isSelected = this.selectedSignals.has(signal.id);
    const isFavorite = signal.isFavorite || false;
    
    return `
      <div class="signal-item ${isSelected ? 'selected' : ''}" data-signal-id="${signal.id}">
        ${this.isMultiSelectMode ? `
          <div class="signal-checkbox">
            <input type="checkbox" ${isSelected ? 'checked' : ''} 
                   data-action="select-signal" data-signal-id="${signal.id}">
          </div>
        ` : ''}
        
        <div class="signal-icon">
          <span class="protocol-icon">${this.getProtocolIcon(signal.protocol)}</span>
        </div>
        
        <div class="signal-info">
          <div class="signal-name">${signal.name}</div>
          <div class="signal-details">
            <span class="signal-protocol">${signal.protocol}</span>
            ${signal.category ? `<span class="signal-category">${signal.category}</span>` : ''}
            <span class="signal-usage">使用 ${signal.usageCount || 0} 次</span>
          </div>
        </div>
        
        <div class="signal-actions">
          <button class="action-btn favorite-btn ${isFavorite ? 'active' : ''}" 
                  data-action="toggle-favorite" data-signal-id="${signal.id}" 
                  title="收藏">
            ⭐
          </button>
          <button class="action-btn emit-btn" 
                  data-action="emit-signal" data-signal-id="${signal.id}" 
                  title="发送信号">
            📡
          </button>
          <button class="action-btn edit-btn" 
                  data-action="edit-signal" data-signal-id="${signal.id}" 
                  title="编辑">
            ✏️
          </button>
          <button class="action-btn delete-btn" 
                  data-action="delete-signal" data-signal-id="${signal.id}" 
                  title="删除">
            🗑️
          </button>
        </div>
      </div>
    `;
  }

  /**
   * 获取协议图标
   */
  getProtocolIcon(protocol) {
    const icons = {
      'NEC': '📺',
      'Sony': '🎮',
      'RC5': '📻',
      'RC6': '📡',
      'Samsung': '📱',
      'LG': '🖥️',
      'Raw': '🔧'
    };
    return icons[protocol] || '📡';
  }

  /**
   * 获取过滤后的信号
   */
  getFilteredSignals() {
    let filtered = Array.from(this.signals.values());
    
    // 搜索过滤
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(signal => 
        signal.name.toLowerCase().includes(query) ||
        signal.protocol.toLowerCase().includes(query) ||
        (signal.category && signal.category.toLowerCase().includes(query))
      );
    }
    
    // 分类过滤
    switch (this.currentFilter) {
      case 'recent':
        filtered = filtered.filter(signal => signal.lastUsed > Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'favorites':
        filtered = filtered.filter(signal => signal.isFavorite);
        break;
    }
    
    // 排序
    filtered.sort((a, b) => {
      const aVal = a[this.uiConfig.sortBy];
      const bVal = b[this.uiConfig.sortBy];
      
      if (this.uiConfig.sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });
    
    return filtered;
  }

  /**
   * 发送信号到控制中心
   */
  sendSignalsToControl(data) {
    const signalList = Array.from(this.signals.values()).map(signal => ({
      id: signal.id,
      name: signal.name,
      protocol: signal.protocol,
      category: signal.category
    }));
    
    this.emitEvent('control.signals.response', {
      requestId: data.requestId,
      signals: signalList,
      timestamp: Date.now()
    });
  }

  /**
   * 发送信号到定时器
   */
  sendSignalsToTimer(data) {
    const signalList = Array.from(this.signals.values()).map(signal => ({
      id: signal.id,
      name: signal.name,
      protocol: signal.protocol
    }));
    
    this.emitEvent('timer.signals.response', {
      requestId: data.requestId,
      signals: signalList,
      timestamp: Date.now()
    });
  }

  /**
   * 发送信号
   */
  async emitSignal(signalId) {
    if (!signalId) return;
    
    try {
      const signal = this.signals.get(signalId);
      if (!signal) {
        throw new Error('信号不存在');
      }
      
      // 发送信号到ESP32
      const response = await this.requestESP32(`/api/signals/${signalId}/emit`, {
        method: 'POST'
      });
      
      if (response.success) {
        // 更新使用统计
        signal.usageCount = (signal.usageCount || 0) + 1;
        signal.lastUsed = Date.now();
        
        this.handleSuccess(`信号 "${signal.name}" 发送成功`, 'Emit signal');
        
        // 发布信号发送事件
        this.emitEvent('signal.emitted', {
          signalId: signal.id,
          signalName: signal.name,
          timestamp: Date.now()
        });
      }
      
    } catch (error) {
      this.handleError(error, 'Emit signal');
    }
  }

  /**
   * 搜索防抖
   */
  debounceSearch() {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    
    this.searchTimeout = setTimeout(() => {
      this.renderSignalList();
    }, 300);
  }

  /**
   * 切换视图模式
   */
  toggleView() {
    this.currentView = this.currentView === 'grid' ? 'list' : 'grid';
    
    const signalList = document.getElementById('signal-list');
    const viewToggle = document.getElementById('view-toggle');
    
    if (signalList) {
      signalList.className = `signal-list ${this.currentView}-view`;
    }
    
    if (viewToggle) {
      const icon = viewToggle.querySelector('.icon');
      const text = viewToggle.querySelector('span:last-child') || viewToggle;
      
      if (this.currentView === 'grid') {
        icon.textContent = '📋';
        text.textContent = '列表视图';
      } else {
        icon.textContent = '⊞';
        text.textContent = '网格视图';
      }
    }
  }

  /**
   * 切换多选模式
   */
  toggleMultiSelectMode() {
    this.isMultiSelectMode = !this.isMultiSelectMode;
    this.selectedSignals.clear();
    
    // 重新渲染列表
    this.renderSignalList();
    
    // 显示/隐藏批量操作栏
    const batchActions = document.getElementById('batch-actions');
    if (batchActions) {
      batchActions.style.display = this.isMultiSelectMode ? 'flex' : 'none';
    }
    
    // 更新按钮状态
    const multiSelectToggle = document.getElementById('multi-select-toggle');
    if (multiSelectToggle) {
      multiSelectToggle.classList.toggle('active', this.isMultiSelectMode);
    }
  }

  /**
   * 切换信号选择状态
   */
  toggleSignalSelection(signalId) {
    if (this.selectedSignals.has(signalId)) {
      this.selectedSignals.delete(signalId);
    } else {
      this.selectedSignals.add(signalId);
    }
    
    // 更新选中计数
    const selectedCount = document.getElementById('selected-count');
    if (selectedCount) {
      selectedCount.textContent = this.selectedSignals.size;
    }
    
    // 更新信号项样式
    const signalItem = document.querySelector(`[data-signal-id="${signalId}"]`);
    if (signalItem) {
      signalItem.classList.toggle('selected', this.selectedSignals.has(signalId));
      
      const checkbox = signalItem.querySelector('input[type="checkbox"]');
      if (checkbox) {
        checkbox.checked = this.selectedSignals.has(signalId);
      }
    }
  }

  /**
   * 切换信号收藏状态
   */
  async toggleSignalFavorite(signalId) {
    try {
      const signal = this.signals.get(signalId);
      if (!signal) return;
      
      const newFavoriteState = !signal.isFavorite;
      
      // 更新ESP32
      const response = await this.requestESP32(`/api/signals/${signalId}`, {
        method: 'PUT',
        body: JSON.stringify({
          ...signal,
          isFavorite: newFavoriteState
        })
      });
      
      if (response.success) {
        // 更新本地状态
        signal.isFavorite = newFavoriteState;
        
        // 更新UI
        const favoriteBtn = document.querySelector(`[data-signal-id="${signalId}"][data-action="toggle-favorite"]`);
        if (favoriteBtn) {
          favoriteBtn.classList.toggle('active', newFavoriteState);
        }
        
        this.handleSuccess(
          `信号 "${signal.name}" ${newFavoriteState ? '已添加到' : '已从'}收藏夹${newFavoriteState ? '' : '移除'}`,
          'Toggle favorite'
        );
      }
      
    } catch (error) {
      this.handleError(error, 'Toggle favorite');
    }
  }
}

// 导出SignalManager类
window.SignalManager = SignalManager;
