/**
 * R1系统 - SystemMonitor系统监控模块
 * 基于：R1前端系统架构标准文档.md BaseModule架构标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 系统管理API
 * 
 * 功能特性：
 * - 系统状态监控
 * - 性能指标显示
 * - 硬件信息查看
 * - 系统日志管理
 * - 错误统计分析
 */

class SystemMonitor extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'SystemMonitor');
    
    // 监控数据
    this.systemStatus = {
      uptime: 0,
      freeHeap: 0,
      totalHeap: 0,
      cpuUsage: 0,
      temperature: 0,
      wifiSignal: 0,
      lastUpdate: 0
    };
    
    this.performanceData = {
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      memoryUsage: [],
      cpuHistory: []
    };
    
    this.hardwareInfo = {
      chipModel: '',
      chipRevision: 0,
      flashSize: 0,
      psramSize: 0,
      macAddress: '',
      ipAddress: ''
    };
    
    this.systemLogs = [];
    
    // 监控配置
    this.monitorConfig = {
      autoRefresh: true,
      refreshInterval: 5000,
      maxLogEntries: 1000,
      maxHistoryPoints: 50
    };
    
    // 监控状态
    this.monitorState = {
      isMonitoring: false,
      refreshTimer: null,
      lastRefreshTime: 0
    };
    
    console.log('✅ SystemMonitor constructed');
  }

  /**
   * 事件监听器设置 - 必需实现
   */
  async setupEventListeners() {
    await super.setupEventListeners();
    
    // 监听系统错误事件
    this.eventBus.on('system.error', (data) => {
      this.handleSystemError(data);
    });
    
    // 监听模块错误事件
    this.eventBus.on('module.error', (data) => {
      this.handleModuleError(data);
    });
    
    // 监听ESP32连接状态变化
    this.eventBus.on('esp32.connected', (data) => {
      this.handleConnectionChange(true, data);
    });
    
    this.eventBus.on('esp32.disconnected', (data) => {
      this.handleConnectionChange(false, data);
    });
    
    // 监听性能事件
    this.eventBus.on('system.performance.update', (data) => {
      this.handlePerformanceUpdate(data);
    });
    
    console.log('📡 SystemMonitor: Event listeners setup complete');
  }

  /**
   * UI设置 - 必需实现
   */
  async setupUI() {
    console.log('🎨 SystemMonitor: Setting up UI...');
    
    // 缓存UI元素
    this.cacheElement('container', '#system-monitor-content');
    this.cacheElement('exportLogsBtn', '#export-logs-btn');
    
    // 创建系统监控界面
    this.createSystemMonitorUI();
    
    // 设置事件委托
    this.setupEventDelegation();
    
    console.log('✅ SystemMonitor: UI setup complete');
  }

  /**
   * 数据加载 - 必需实现
   */
  async loadModuleData() {
    console.log('📊 SystemMonitor: Loading system data...');
    
    try {
      this.state.loading = true;
      
      // 加载系统状态
      await this.loadSystemStatus();
      
      // 加载性能数据
      await this.loadPerformanceData();
      
      // 加载硬件信息
      await this.loadHardwareInfo();
      
      // 加载系统日志
      await this.loadSystemLogs();
      
      // 渲染所有面板
      this.renderAllPanels();
      
      // 启动自动刷新
      if (this.monitorConfig.autoRefresh) {
        this.startAutoRefresh();
      }
      
      this.handleSuccess('系统监控数据加载完成', 'Load system data');
      
    } catch (error) {
      this.handleError(error, 'Load system data');
    } finally {
      this.state.loading = false;
    }
  }

  /**
   * 创建系统监控界面
   */
  createSystemMonitorUI() {
    const container = this.getElement('container');
    if (!container) return;
    
    container.innerHTML = `
      <div class="system-monitor">
        <!-- 监控控制栏 -->
        <div class="monitor-controls">
          <div class="controls-left">
            <button class="btn btn-primary" id="refresh-all">
              <span class="icon">🔄</span>
              刷新数据
            </button>
            <label class="switch-label">
              <input type="checkbox" id="auto-refresh" ${this.monitorConfig.autoRefresh ? 'checked' : ''}>
              <span class="switch-slider"></span>
              自动刷新
            </label>
          </div>
          <div class="controls-right">
            <span class="last-update">最后更新: <span id="last-update-time">--:--:--</span></span>
          </div>
        </div>
        
        <!-- 系统状态面板 -->
        <div class="monitor-panel">
          <div class="panel-header">
            <h3>系统状态</h3>
            <div class="status-indicator" id="system-status-indicator"></div>
          </div>
          <div class="panel-content">
            <div class="status-grid" id="system-status-grid">
              <!-- 状态项将动态生成 -->
            </div>
          </div>
        </div>
        
        <!-- 性能监控面板 -->
        <div class="monitor-panel">
          <div class="panel-header">
            <h3>性能监控</h3>
            <button class="btn btn-secondary" id="clear-performance">清空历史</button>
          </div>
          <div class="panel-content">
            <div class="performance-charts" id="performance-charts">
              <!-- 性能图表将动态生成 -->
            </div>
            <div class="performance-stats" id="performance-stats">
              <!-- 性能统计将动态生成 -->
            </div>
          </div>
        </div>
        
        <!-- 硬件信息面板 -->
        <div class="monitor-panel">
          <div class="panel-header">
            <h3>硬件信息</h3>
            <button class="btn btn-secondary" id="refresh-hardware">刷新</button>
          </div>
          <div class="panel-content">
            <div class="hardware-info" id="hardware-info">
              <!-- 硬件信息将动态生成 -->
            </div>
          </div>
        </div>
        
        <!-- 系统日志面板 -->
        <div class="monitor-panel">
          <div class="panel-header">
            <h3>系统日志</h3>
            <div class="log-controls">
              <select id="log-level-filter">
                <option value="all">全部级别</option>
                <option value="ERROR">错误</option>
                <option value="WARN">警告</option>
                <option value="INFO">信息</option>
                <option value="DEBUG">调试</option>
              </select>
              <button class="btn btn-secondary" id="clear-logs">清空日志</button>
            </div>
          </div>
          <div class="panel-content">
            <div class="log-container" id="log-container">
              <!-- 日志条目将动态生成 -->
            </div>
          </div>
        </div>
        
        <!-- 错误统计面板 -->
        <div class="monitor-panel">
          <div class="panel-header">
            <h3>错误统计</h3>
            <button class="btn btn-secondary" id="reset-error-stats">重置统计</button>
          </div>
          <div class="panel-content">
            <div class="error-stats" id="error-stats">
              <!-- 错误统计将动态生成 -->
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 设置事件委托
   */
  setupEventDelegation() {
    const container = this.getElement('container');
    if (!container) return;
    
    // 刷新按钮
    const refreshBtn = container.querySelector('#refresh-all');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.refreshAllData();
      });
    }
    
    // 自动刷新开关
    const autoRefreshToggle = container.querySelector('#auto-refresh');
    if (autoRefreshToggle) {
      autoRefreshToggle.addEventListener('change', (event) => {
        this.toggleAutoRefresh(event.target.checked);
      });
    }
    
    // 清空性能历史
    const clearPerformanceBtn = container.querySelector('#clear-performance');
    if (clearPerformanceBtn) {
      clearPerformanceBtn.addEventListener('click', () => {
        this.clearPerformanceHistory();
      });
    }
    
    // 刷新硬件信息
    const refreshHardwareBtn = container.querySelector('#refresh-hardware');
    if (refreshHardwareBtn) {
      refreshHardwareBtn.addEventListener('click', () => {
        this.loadHardwareInfo();
      });
    }
    
    // 日志级别过滤
    const logLevelFilter = container.querySelector('#log-level-filter');
    if (logLevelFilter) {
      logLevelFilter.addEventListener('change', (event) => {
        this.filterLogs(event.target.value);
      });
    }
    
    // 清空日志
    const clearLogsBtn = container.querySelector('#clear-logs');
    if (clearLogsBtn) {
      clearLogsBtn.addEventListener('click', () => {
        this.clearLogs();
      });
    }
    
    // 重置错误统计
    const resetErrorStatsBtn = container.querySelector('#reset-error-stats');
    if (resetErrorStatsBtn) {
      resetErrorStatsBtn.addEventListener('click', () => {
        this.resetErrorStats();
      });
    }
    
    // 导出日志按钮
    const exportLogsBtn = this.getElement('exportLogsBtn');
    if (exportLogsBtn) {
      exportLogsBtn.addEventListener('click', () => {
        this.exportLogs();
      });
    }
  }

  /**
   * 加载系统状态
   */
  async loadSystemStatus() {
    try {
      const response = await this.requestESP32('/api/system/status', {
        method: 'GET'
      });
      
      if (response.success && response.data) {
        this.systemStatus = {
          ...this.systemStatus,
          ...response.data,
          lastUpdate: Date.now()
        };
        
        this.renderSystemStatus();
      }
    } catch (error) {
      console.warn('⚠️ Failed to load system status:', error.message);
    }
  }

  /**
   * 加载性能数据
   */
  async loadPerformanceData() {
    try {
      const response = await this.requestESP32('/api/system/performance', {
        method: 'GET'
      });
      
      if (response.success && response.data) {
        this.updatePerformanceData(response.data);
        this.renderPerformanceCharts();
      }
    } catch (error) {
      console.warn('⚠️ Failed to load performance data:', error.message);
    }
  }

  /**
   * 加载硬件信息
   */
  async loadHardwareInfo() {
    try {
      const response = await this.requestESP32('/api/system/hardware', {
        method: 'GET'
      });
      
      if (response.success && response.data) {
        this.hardwareInfo = {
          ...this.hardwareInfo,
          ...response.data
        };
        
        this.renderHardwareInfo();
      }
    } catch (error) {
      console.warn('⚠️ Failed to load hardware info:', error.message);
    }
  }

  /**
   * 加载系统日志
   */
  async loadSystemLogs() {
    try {
      const response = await this.requestESP32('/api/system/logs', {
        method: 'GET'
      });
      
      if (response.success && response.data.logs) {
        this.systemLogs = response.data.logs;
        this.renderSystemLogs();
      }
    } catch (error) {
      console.warn('⚠️ Failed to load system logs:', error.message);
    }
  }

  /**
   * 渲染系统状态
   */
  renderSystemStatus() {
    const statusGrid = document.getElementById('system-status-grid');
    const statusIndicator = document.getElementById('system-status-indicator');
    
    if (!statusGrid) return;
    
    // 更新状态指示器
    if (statusIndicator) {
      const isHealthy = this.systemStatus.freeHeap > 50000 && this.systemStatus.cpuUsage < 80;
      statusIndicator.className = `status-indicator ${isHealthy ? 'healthy' : 'warning'}`;
      statusIndicator.title = isHealthy ? '系统运行正常' : '系统负载较高';
    }
    
    // 渲染状态网格
    statusGrid.innerHTML = `
      <div class="status-item">
        <div class="status-label">运行时间</div>
        <div class="status-value">${this.formatUptime(this.systemStatus.uptime)}</div>
      </div>
      <div class="status-item">
        <div class="status-label">可用内存</div>
        <div class="status-value">${this.formatBytes(this.systemStatus.freeHeap)}</div>
      </div>
      <div class="status-item">
        <div class="status-label">总内存</div>
        <div class="status-value">${this.formatBytes(this.systemStatus.totalHeap)}</div>
      </div>
      <div class="status-item">
        <div class="status-label">CPU使用率</div>
        <div class="status-value">${this.systemStatus.cpuUsage}%</div>
      </div>
      <div class="status-item">
        <div class="status-label">芯片温度</div>
        <div class="status-value">${this.systemStatus.temperature}°C</div>
      </div>
      <div class="status-item">
        <div class="status-label">WiFi信号</div>
        <div class="status-value">${this.systemStatus.wifiSignal}dBm</div>
      </div>
    `;
    
    // 更新最后更新时间
    const lastUpdateElement = document.getElementById('last-update-time');
    if (lastUpdateElement) {
      lastUpdateElement.textContent = new Date(this.systemStatus.lastUpdate).toLocaleTimeString();
    }
  }

  /**
   * 更新性能数据
   */
  updatePerformanceData(newData) {
    this.performanceData = {
      ...this.performanceData,
      ...newData
    };
    
    // 添加到历史记录
    const timestamp = Date.now();
    
    this.performanceData.memoryUsage.push({
      timestamp,
      value: ((this.systemStatus.totalHeap - this.systemStatus.freeHeap) / this.systemStatus.totalHeap) * 100
    });
    
    this.performanceData.cpuHistory.push({
      timestamp,
      value: this.systemStatus.cpuUsage
    });
    
    // 限制历史记录长度
    if (this.performanceData.memoryUsage.length > this.monitorConfig.maxHistoryPoints) {
      this.performanceData.memoryUsage.shift();
    }
    
    if (this.performanceData.cpuHistory.length > this.monitorConfig.maxHistoryPoints) {
      this.performanceData.cpuHistory.shift();
    }
  }

  /**
   * 渲染性能图表
   */
  renderPerformanceCharts() {
    const chartsContainer = document.getElementById('performance-charts');
    const statsContainer = document.getElementById('performance-stats');
    
    if (!chartsContainer || !statsContainer) return;
    
    // 简单的文本图表（在实际项目中可以使用Chart.js等库）
    chartsContainer.innerHTML = `
      <div class="chart-container">
        <h4>内存使用率趋势</h4>
        <div class="simple-chart" id="memory-chart">
          ${this.renderSimpleChart(this.performanceData.memoryUsage, '%')}
        </div>
      </div>
      <div class="chart-container">
        <h4>CPU使用率趋势</h4>
        <div class="simple-chart" id="cpu-chart">
          ${this.renderSimpleChart(this.performanceData.cpuHistory, '%')}
        </div>
      </div>
    `;
    
    // 渲染性能统计
    statsContainer.innerHTML = `
      <div class="stat-item">
        <div class="stat-label">总请求数</div>
        <div class="stat-value">${this.performanceData.requestCount}</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">错误数</div>
        <div class="stat-value">${this.performanceData.errorCount}</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">平均响应时间</div>
        <div class="stat-value">${this.performanceData.averageResponseTime}ms</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">错误率</div>
        <div class="stat-value">${this.calculateErrorRate()}%</div>
      </div>
    `;
  }

  /**
   * 渲染简单图表
   */
  renderSimpleChart(data, unit) {
    if (data.length === 0) {
      return '<div class="no-data">暂无数据</div>';
    }
    
    const maxValue = Math.max(...data.map(d => d.value));
    const minValue = Math.min(...data.map(d => d.value));
    const range = maxValue - minValue || 1;
    
    return data.map((point, index) => {
      const height = ((point.value - minValue) / range) * 100;
      const time = new Date(point.timestamp).toLocaleTimeString();
      
      return `
        <div class="chart-bar" style="height: ${height}%" title="${time}: ${point.value}${unit}">
          <div class="bar-value">${point.value}</div>
        </div>
      `;
    }).join('');
  }

  /**
   * 渲染硬件信息
   */
  renderHardwareInfo() {
    const hardwareContainer = document.getElementById('hardware-info');
    if (!hardwareContainer) return;
    
    hardwareContainer.innerHTML = `
      <div class="hardware-grid">
        <div class="hardware-item">
          <div class="hardware-label">芯片型号</div>
          <div class="hardware-value">${this.hardwareInfo.chipModel}</div>
        </div>
        <div class="hardware-item">
          <div class="hardware-label">芯片版本</div>
          <div class="hardware-value">Rev ${this.hardwareInfo.chipRevision}</div>
        </div>
        <div class="hardware-item">
          <div class="hardware-label">Flash大小</div>
          <div class="hardware-value">${this.formatBytes(this.hardwareInfo.flashSize)}</div>
        </div>
        <div class="hardware-item">
          <div class="hardware-label">PSRAM大小</div>
          <div class="hardware-value">${this.formatBytes(this.hardwareInfo.psramSize)}</div>
        </div>
        <div class="hardware-item">
          <div class="hardware-label">MAC地址</div>
          <div class="hardware-value">${this.hardwareInfo.macAddress}</div>
        </div>
        <div class="hardware-item">
          <div class="hardware-label">IP地址</div>
          <div class="hardware-value">${this.hardwareInfo.ipAddress}</div>
        </div>
      </div>
    `;
  }

  /**
   * 启动自动刷新
   */
  startAutoRefresh() {
    if (this.monitorState.refreshTimer) {
      clearInterval(this.monitorState.refreshTimer);
    }
    
    this.monitorState.isMonitoring = true;
    this.monitorState.refreshTimer = setInterval(() => {
      this.refreshAllData();
    }, this.monitorConfig.refreshInterval);
    
    console.log('📊 Auto refresh started');
  }

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    if (this.monitorState.refreshTimer) {
      clearInterval(this.monitorState.refreshTimer);
      this.monitorState.refreshTimer = null;
    }
    
    this.monitorState.isMonitoring = false;
    console.log('📊 Auto refresh stopped');
  }

  /**
   * 切换自动刷新
   */
  toggleAutoRefresh(enabled) {
    this.monitorConfig.autoRefresh = enabled;
    
    if (enabled) {
      this.startAutoRefresh();
    } else {
      this.stopAutoRefresh();
    }
  }

  /**
   * 刷新所有数据
   */
  async refreshAllData() {
    try {
      await Promise.all([
        this.loadSystemStatus(),
        this.loadPerformanceData()
      ]);
      
      this.monitorState.lastRefreshTime = Date.now();
      
    } catch (error) {
      this.handleError(error, 'Refresh all data');
    }
  }

  /**
   * 渲染所有面板
   */
  renderAllPanels() {
    this.renderSystemStatus();
    this.renderPerformanceCharts();
    this.renderHardwareInfo();
    this.renderSystemLogs();
  }

  /**
   * 格式化运行时间
   */
  formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  }

  /**
   * 格式化字节数
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 计算错误率
   */
  calculateErrorRate() {
    if (this.performanceData.requestCount === 0) return 0;
    return ((this.performanceData.errorCount / this.performanceData.requestCount) * 100).toFixed(2);
  }

  /**
   * 处理系统错误
   */
  handleSystemError(data) {
    this.addLogEntry('ERROR', data.error, data.operation);
    this.performanceData.errorCount++;
  }

  /**
   * 处理模块错误
   */
  handleModuleError(data) {
    this.addLogEntry('ERROR', `${data.moduleName}: ${data.error}`, data.operation);
  }

  /**
   * 处理连接状态变化
   */
  handleConnectionChange(connected, data) {
    const message = connected ? 'ESP32连接已建立' : 'ESP32连接已断开';
    this.addLogEntry('INFO', message, 'Connection');
  }

  /**
   * 添加日志条目
   */
  addLogEntry(level, message, operation) {
    const logEntry = {
      timestamp: Date.now(),
      level,
      message,
      operation,
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    
    this.systemLogs.unshift(logEntry);
    
    // 限制日志数量
    if (this.systemLogs.length > this.monitorConfig.maxLogEntries) {
      this.systemLogs = this.systemLogs.slice(0, this.monitorConfig.maxLogEntries);
    }
    
    // 重新渲染日志
    this.renderSystemLogs();
  }

  /**
   * 渲染系统日志
   */
  renderSystemLogs() {
    const logContainer = document.getElementById('log-container');
    if (!logContainer) return;
    
    const filteredLogs = this.getFilteredLogs();
    
    if (filteredLogs.length === 0) {
      logContainer.innerHTML = '<div class="no-logs">暂无日志</div>';
      return;
    }
    
    logContainer.innerHTML = filteredLogs.map(log => `
      <div class="log-entry ${log.level.toLowerCase()}">
        <div class="log-timestamp">${new Date(log.timestamp).toLocaleString()}</div>
        <div class="log-level">${log.level}</div>
        <div class="log-operation">${log.operation}</div>
        <div class="log-message">${log.message}</div>
      </div>
    `).join('');
  }

  /**
   * 获取过滤后的日志
   */
  getFilteredLogs() {
    const levelFilter = document.getElementById('log-level-filter');
    const selectedLevel = levelFilter ? levelFilter.value : 'all';
    
    if (selectedLevel === 'all') {
      return this.systemLogs;
    }
    
    return this.systemLogs.filter(log => log.level === selectedLevel);
  }

  /**
   * 过滤日志
   */
  filterLogs(level) {
    this.renderSystemLogs();
  }

  /**
   * 清空日志
   */
  clearLogs() {
    if (confirm('确定要清空所有日志吗？')) {
      this.systemLogs = [];
      this.renderSystemLogs();
      this.handleSuccess('系统日志已清空', 'Clear logs');
    }
  }

  /**
   * 导出日志
   */
  exportLogs() {
    try {
      const logData = {
        exportTime: new Date().toISOString(),
        systemInfo: this.hardwareInfo,
        logs: this.systemLogs
      };
      
      const dataStr = JSON.stringify(logData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `system-logs-${new Date().toISOString().slice(0, 10)}.json`;
      link.click();
      
      this.handleSuccess('系统日志导出成功', 'Export logs');
      
    } catch (error) {
      this.handleError(error, 'Export logs');
    }
  }

  /**
   * 销毁模块时清理资源
   */
  destroy() {
    // 停止自动刷新
    this.stopAutoRefresh();
    
    // 调用父类销毁方法
    super.destroy();
  }
}

// 导出SystemMonitor类
window.SystemMonitor = SystemMonitor;
