/**
 * R1系统 - TimerManager定时器管理模块
 * 基于：R1前端系统架构标准文档.md BaseModule架构标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 定时器管理API
 *
 * 功能特性：
 * - 定时器创建和管理
 * - 定时任务调度
 * - 重复定时器支持
 * - 实时状态监控
 * - 信号关联管理
 */

class TimerManager extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'TimerManager');

    // 业务数据
    this.timers = new Map();              // 定时器数据
    this.availableSignals = new Map();    // 可用信号列表
    this.activeTimers = new Set();        // 活跃定时器

    // 定时器状态
    this.timerState = {
      isCreating: false,
      editingTimer: null,
      selectedTimers: new Set(),
      currentView: 'list'
    };

    // UI配置
    this.uiConfig = {
      timeFormat: '24h',
      showSeconds: false,
      autoRefresh: true,
      refreshInterval: 30000
    };

    // 实时时间显示（技术约束例外）
    this.timeDisplay = {
      currentTime: new Date(),
      updateTimer: null,
      isRunning: false
    };

    console.log('✅ TimerManager constructed');
  }

  /**
   * 事件监听器设置 - 必需实现
   */
  async setupEventListeners() {
    await super.setupEventListeners();

    // 监听信号管理器的信号列表响应
    this.eventBus.on('timer.signals.response', (data) => {
      this.handleSignalsResponse(data);
    });

    // 监听定时器触发事件
    this.eventBus.on('timer.triggered', (data) => {
      this.handleTimerTriggered(data);
    });

    // 监听定时器状态变化
    this.eventBus.on('timer.status.changed', (data) => {
      this.handleTimerStatusChanged(data);
    });

    // 监听系统时间同步
    this.eventBus.on('system.time.sync', (data) => {
      this.handleTimeSync(data);
    });

    console.log('📡 TimerManager: Event listeners setup complete');
  }

  /**
   * UI设置 - 必需实现
   */
  async setupUI() {
    console.log('🎨 TimerManager: Setting up UI...');

    // 缓存UI元素
    this.cacheElement('container', '#timer-manager-content');
    this.cacheElement('addTimerBtn', '#add-timer-btn');

    // 创建定时器管理界面
    this.createTimerManagerUI();

    // 设置事件委托
    this.setupEventDelegation();

    // 启动实时时间显示（技术约束例外）
    this.startTimeDisplay();

    console.log('✅ TimerManager: UI setup complete');
  }

  /**
   * 数据加载 - 必需实现
   */
  async loadModuleData() {
    console.log('📊 TimerManager: Loading timer data...');

    try {
      this.state.loading = true;

      // 从ESP32加载定时器列表
      const response = await this.requestESP32('/api/timers', {
        method: 'GET'
      });

      if (response.success && response.data.timers) {
        this.processTimerData(response.data.timers);
        this.handleSuccess(`加载了 ${this.timers.size} 个定时器`, 'Load timers');
      }

      // 请求信号列表
      this.requestSignalList();

      // 渲染定时器列表
      this.renderTimerList();

    } catch (error) {
      this.handleError(error, 'Load timer data');
    } finally {
      this.state.loading = false;
    }
  }

  /**
   * 创建定时器管理界面
   */
  createTimerManagerUI() {
    const container = this.getElement('container');
    if (!container) return;

    container.innerHTML = `
      <div class="timer-manager">
        <!-- 时间显示和状态栏 -->
        <div class="timer-status-bar">
          <div class="current-time-display">
            <div class="time-label">当前时间</div>
            <div class="time-value" id="current-time">--:--:--</div>
            <div class="date-value" id="current-date">----/--/--</div>
          </div>
          <div class="timer-stats">
            <div class="stat-item">
              <span class="stat-label">总定时器:</span>
              <span class="stat-value" id="total-timers">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">活跃:</span>
              <span class="stat-value" id="active-timers">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">下次触发:</span>
              <span class="stat-value" id="next-trigger">无</span>
            </div>
          </div>
        </div>

        <!-- 定时器工具栏 -->
        <div class="timer-toolbar">
          <div class="toolbar-left">
            <div class="filter-group">
              <select id="timer-filter" class="filter-select">
                <option value="all">全部定时器</option>
                <option value="active">活跃定时器</option>
                <option value="inactive">非活跃定时器</option>
                <option value="repeating">重复定时器</option>
                <option value="once">单次定时器</option>
              </select>
            </div>
          </div>
          <div class="toolbar-right">
            <button class="btn btn-secondary" id="batch-enable">批量启用</button>
            <button class="btn btn-secondary" id="batch-disable">批量禁用</button>
            <button class="btn btn-danger" id="batch-delete">批量删除</button>
          </div>
        </div>

        <!-- 定时器列表 -->
        <div class="timer-list-container">
          <div class="timer-list" id="timer-list">
            <!-- 定时器项将动态生成 -->
          </div>
        </div>

        <!-- 创建/编辑定时器对话框 -->
        <div class="modal" id="timer-modal" style="display: none;">
          <div class="modal-content large">
            <div class="modal-header">
              <h3 id="timer-modal-title">创建定时器</h3>
              <button class="modal-close" id="timer-modal-close">×</button>
            </div>
            <div class="modal-body">
              <div class="timer-form">
                <div class="form-row">
                  <div class="form-group">
                    <label for="timer-name">定时器名称</label>
                    <input type="text" id="timer-name" class="form-input" placeholder="请输入定时器名称">
                  </div>
                  <div class="form-group">
                    <label for="timer-enabled">启用状态</label>
                    <label class="switch">
                      <input type="checkbox" id="timer-enabled" checked>
                      <span class="slider"></span>
                    </label>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="timer-time">触发时间</label>
                    <div class="time-input-group">
                      <input type="number" id="timer-hour" class="time-input" min="0" max="23" placeholder="时">
                      <span class="time-separator">:</span>
                      <input type="number" id="timer-minute" class="time-input" min="0" max="59" placeholder="分">
                      <span class="time-separator">:</span>
                      <input type="number" id="timer-second" class="time-input" min="0" max="59" placeholder="秒" value="0">
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <label>重复设置</label>
                  <div class="repeat-options">
                    <label class="radio-option">
                      <input type="radio" name="repeat-type" value="once" checked>
                      <span>仅一次</span>
                    </label>
                    <label class="radio-option">
                      <input type="radio" name="repeat-type" value="daily">
                      <span>每天</span>
                    </label>
                    <label class="radio-option">
                      <input type="radio" name="repeat-type" value="weekly">
                      <span>每周</span>
                    </label>
                    <label class="radio-option">
                      <input type="radio" name="repeat-type" value="custom">
                      <span>自定义</span>
                    </label>
                  </div>
                </div>

                <div class="form-group" id="weekdays-group" style="display: none;">
                  <label>星期选择</label>
                  <div class="weekdays-selector">
                    <label class="weekday-option">
                      <input type="checkbox" value="1">
                      <span>周一</span>
                    </label>
                    <label class="weekday-option">
                      <input type="checkbox" value="2">
                      <span>周二</span>
                    </label>
                    <label class="weekday-option">
                      <input type="checkbox" value="3">
                      <span>周三</span>
                    </label>
                    <label class="weekday-option">
                      <input type="checkbox" value="4">
                      <span>周四</span>
                    </label>
                    <label class="weekday-option">
                      <input type="checkbox" value="5">
                      <span>周五</span>
                    </label>
                    <label class="weekday-option">
                      <input type="checkbox" value="6">
                      <span>周六</span>
                    </label>
                    <label class="weekday-option">
                      <input type="checkbox" value="0">
                      <span>周日</span>
                    </label>
                  </div>
                </div>

                <div class="form-group">
                  <label for="timer-signal">关联信号</label>
                  <select id="timer-signal" class="form-select">
                    <option value="">选择信号</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="timer-description">描述</label>
                  <textarea id="timer-description" class="form-textarea" placeholder="可选的定时器描述"></textarea>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button class="btn btn-secondary" id="timer-cancel">取消</button>
              <button class="btn btn-primary" id="timer-save">保存定时器</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 设置事件委托
   */
  setupEventDelegation() {
    const container = this.getElement('container');
    if (!container) return;

    // 使用事件委托处理所有点击事件
    container.addEventListener('click', (event) => {
      const target = event.target.closest('[data-action]');
      if (!target) return;

      const action = target.dataset.action;
      const timerId = target.dataset.timerId;

      switch (action) {
        case 'toggle-timer':
          this.toggleTimer(timerId);
          break;
        case 'edit-timer':
          this.editTimer(timerId);
          break;
        case 'delete-timer':
          this.deleteTimer(timerId);
          break;
        case 'select-timer':
          this.toggleTimerSelection(timerId);
          break;
        case 'test-timer':
          this.testTimer(timerId);
          break;
      }
    });

    // 添加定时器按钮
    const addTimerBtn = this.getElement('addTimerBtn');
    if (addTimerBtn) {
      addTimerBtn.addEventListener('click', () => {
        this.showCreateTimerModal();
      });
    }

    // 定时器模态框事件
    const timerModal = container.querySelector('#timer-modal');
    if (timerModal) {
      // 关闭按钮
      const closeBtn = timerModal.querySelector('#timer-modal-close');
      const cancelBtn = timerModal.querySelector('#timer-cancel');

      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          this.hideTimerModal();
        });
      }

      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
          this.hideTimerModal();
        });
      }

      // 保存按钮
      const saveBtn = timerModal.querySelector('#timer-save');
      if (saveBtn) {
        saveBtn.addEventListener('click', () => {
          this.saveTimer();
        });
      }

      // 重复类型变化
      const repeatRadios = timerModal.querySelectorAll('input[name="repeat-type"]');
      repeatRadios.forEach(radio => {
        radio.addEventListener('change', () => {
          this.handleRepeatTypeChange(radio.value);
        });
      });
    }

    // 过滤器
    const filterSelect = container.querySelector('#timer-filter');
    if (filterSelect) {
      filterSelect.addEventListener('change', (event) => {
        this.filterTimers(event.target.value);
      });
    }
  }

  /**
   * 启动实时时间显示（技术约束例外）
   * 注：这是唯一允许的轮询操作，用于实时时间显示
   */
  startTimeDisplay() {
    if (this.timeDisplay.isRunning) return;

    this.timeDisplay.isRunning = true;

    const updateTime = () => {
      this.timeDisplay.currentTime = new Date();
      this.updateTimeDisplay();
    };

    // 立即更新一次
    updateTime();

    // 每秒更新时间（技术约束例外 - 实时时间显示）
    this.timeDisplay.updateTimer = setInterval(updateTime, 1000);

    console.log('⏰ Real-time clock started (Technical constraint exception)');
  }

  /**
   * 更新时间显示
   */
  updateTimeDisplay() {
    const timeElement = document.getElementById('current-time');
    const dateElement = document.getElementById('current-date');

    if (timeElement) {
      const timeStr = this.timeDisplay.currentTime.toLocaleTimeString('zh-CN', {
        hour12: this.uiConfig.timeFormat === '12h',
        hour: '2-digit',
        minute: '2-digit',
        second: this.uiConfig.showSeconds ? '2-digit' : undefined
      });
      timeElement.textContent = timeStr;
    }

    if (dateElement) {
      const dateStr = this.timeDisplay.currentTime.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
      dateElement.textContent = dateStr;
    }
  }

  /**
   * 请求信号列表
   */
  requestSignalList() {
    const requestId = `timer_req_${Date.now()}`;

    // 发送请求事件到信号管理器
    this.emitEvent('timer.signals.request', {
      requestId: requestId,
      timestamp: Date.now()
    });
  }

  /**
   * 处理信号列表响应
   */
  handleSignalsResponse(data) {
    if (!data.signals) return;

    // 更新可用信号列表
    this.availableSignals.clear();
    data.signals.forEach(signal => {
      this.availableSignals.set(signal.id, signal);
    });

    // 更新信号选择器
    this.updateSignalSelector();

    console.log(`✅ Received ${data.signals.length} signals from SignalManager`);
  }

  /**
   * 更新信号选择器
   */
  updateSignalSelector() {
    const signalSelect = document.getElementById('timer-signal');
    if (!signalSelect) return;

    // 清空现有选项（保留默认选项）
    signalSelect.innerHTML = '<option value="">选择信号</option>';

    // 添加信号选项
    for (const [signalId, signal] of this.availableSignals) {
      const option = document.createElement('option');
      option.value = signalId;
      option.textContent = `${signal.name} (${signal.protocol})`;
      signalSelect.appendChild(option);
    }
  }

  /**
   * 处理定时器数据
   */
  processTimerData(timersArray) {
    this.timers.clear();
    this.activeTimers.clear();

    timersArray.forEach(timer => {
      this.timers.set(timer.id, timer);
      if (timer.enabled) {
        this.activeTimers.add(timer.id);
      }
    });

    // 更新统计显示
    this.updateTimerStats();

    console.log(`✅ Processed ${this.timers.size} timers, ${this.activeTimers.size} active`);
  }

  /**
   * 渲染定时器列表
   */
  renderTimerList() {
    const timerList = document.getElementById('timer-list');
    if (!timerList) return;

    const timersArray = Array.from(this.timers.values());

    if (timersArray.length === 0) {
      timerList.innerHTML = `
        <div class="empty-state">
          <div class="empty-icon">⏰</div>
          <div class="empty-text">暂无定时器</div>
          <button class="btn btn-primary" id="create-first-timer">创建第一个定时器</button>
        </div>
      `;
      return;
    }

    timerList.innerHTML = timersArray.map(timer =>
      this.renderTimerItem(timer)
    ).join('');
  }

  /**
   * 渲染单个定时器项
   */
  renderTimerItem(timer) {
    const signal = this.availableSignals.get(timer.signalId);
    const nextTrigger = this.calculateNextTrigger(timer);

    return `
      <div class="timer-item ${timer.enabled ? 'enabled' : 'disabled'}" data-timer-id="${timer.id}">
        <div class="timer-checkbox">
          <input type="checkbox" data-action="select-timer" data-timer-id="${timer.id}">
        </div>

        <div class="timer-info">
          <div class="timer-header">
            <div class="timer-name">${timer.name}</div>
            <div class="timer-status">
              <span class="status-badge ${timer.enabled ? 'enabled' : 'disabled'}">
                ${timer.enabled ? '启用' : '禁用'}
              </span>
            </div>
          </div>

          <div class="timer-details">
            <div class="timer-time">
              <span class="time-icon">🕐</span>
              <span class="time-value">${this.formatTime(timer.hour, timer.minute, timer.second)}</span>
            </div>

            <div class="timer-repeat">
              <span class="repeat-icon">🔄</span>
              <span class="repeat-value">${this.formatRepeatType(timer)}</span>
            </div>

            ${signal ? `
              <div class="timer-signal">
                <span class="signal-icon">📡</span>
                <span class="signal-value">${signal.name}</span>
              </div>
            ` : ''}

            ${nextTrigger ? `
              <div class="timer-next">
                <span class="next-icon">⏭️</span>
                <span class="next-value">${nextTrigger}</span>
              </div>
            ` : ''}
          </div>

          ${timer.description ? `
            <div class="timer-description">${timer.description}</div>
          ` : ''}
        </div>

        <div class="timer-actions">
          <button class="action-btn toggle-btn ${timer.enabled ? 'enabled' : 'disabled'}"
                  data-action="toggle-timer" data-timer-id="${timer.id}"
                  title="${timer.enabled ? '禁用' : '启用'}定时器">
            ${timer.enabled ? '⏸️' : '▶️'}
          </button>
          <button class="action-btn test-btn"
                  data-action="test-timer" data-timer-id="${timer.id}"
                  title="测试定时器">
            🧪
          </button>
          <button class="action-btn edit-btn"
                  data-action="edit-timer" data-timer-id="${timer.id}"
                  title="编辑定时器">
            ✏️
          </button>
          <button class="action-btn delete-btn"
                  data-action="delete-timer" data-timer-id="${timer.id}"
                  title="删除定时器">
            🗑️
          </button>
        </div>
      </div>
    `;
  }

  /**
   * 格式化时间显示
   */
  formatTime(hour, minute, second) {
    const h = hour.toString().padStart(2, '0');
    const m = minute.toString().padStart(2, '0');
    const s = second.toString().padStart(2, '0');

    if (this.uiConfig.showSeconds) {
      return `${h}:${m}:${s}`;
    } else {
      return `${h}:${m}`;
    }
  }

  /**
   * 格式化重复类型
   */
  formatRepeatType(timer) {
    if (!timer.isRepeating) {
      return '仅一次';
    }

    if (timer.weekdays === 127) { // 0b1111111 - 每天
      return '每天';
    }

    const weekdayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const selectedDays = [];

    for (let i = 0; i < 7; i++) {
      if (timer.weekdays & (1 << i)) {
        selectedDays.push(weekdayNames[i]);
      }
    }

    return selectedDays.join(', ');
  }

  /**
   * 计算下次触发时间
   */
  calculateNextTrigger(timer) {
    if (!timer.enabled || !timer.nextTriggerTime) {
      return null;
    }

    const now = Date.now();
    const triggerTime = timer.nextTriggerTime;

    if (triggerTime <= now) {
      return '即将触发';
    }

    const diff = triggerTime - now;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}天后`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟后`;
    } else {
      return `${minutes}分钟后`;
    }
  }

  /**
   * 更新定时器统计
   */
  updateTimerStats() {
    const totalElement = document.getElementById('total-timers');
    const activeElement = document.getElementById('active-timers');
    const nextTriggerElement = document.getElementById('next-trigger');

    if (totalElement) {
      totalElement.textContent = this.timers.size.toString();
    }

    if (activeElement) {
      activeElement.textContent = this.activeTimers.size.toString();
    }

    if (nextTriggerElement) {
      const nextTimer = this.findNextTriggerTimer();
      if (nextTimer) {
        const nextTrigger = this.calculateNextTrigger(nextTimer);
        nextTriggerElement.textContent = nextTrigger || '计算中...';
      } else {
        nextTriggerElement.textContent = '无';
      }
    }
  }

  /**
   * 查找下次触发的定时器
   */
  findNextTriggerTimer() {
    let nextTimer = null;
    let earliestTime = Infinity;

    for (const timer of this.timers.values()) {
      if (timer.enabled && timer.nextTriggerTime && timer.nextTriggerTime < earliestTime) {
        earliestTime = timer.nextTriggerTime;
        nextTimer = timer;
      }
    }

    return nextTimer;
  }

  /**
   * 显示创建定时器模态框
   */
  showCreateTimerModal() {
    this.timerState.editingTimer = null;
    this.resetTimerForm();

    const modal = document.getElementById('timer-modal');
    const title = document.getElementById('timer-modal-title');

    if (title) {
      title.textContent = '创建定时器';
    }

    if (modal) {
      modal.style.display = 'flex';
    }
  }

  /**
   * 隐藏定时器模态框
   */
  hideTimerModal() {
    const modal = document.getElementById('timer-modal');
    if (modal) {
      modal.style.display = 'none';
    }

    this.timerState.editingTimer = null;
  }

  /**
   * 重置定时器表单
   */
  resetTimerForm() {
    const form = document.querySelector('.timer-form');
    if (!form) return;

    // 重置基本字段
    const nameInput = form.querySelector('#timer-name');
    const enabledInput = form.querySelector('#timer-enabled');
    const hourInput = form.querySelector('#timer-hour');
    const minuteInput = form.querySelector('#timer-minute');
    const secondInput = form.querySelector('#timer-second');
    const signalSelect = form.querySelector('#timer-signal');
    const descriptionInput = form.querySelector('#timer-description');

    if (nameInput) nameInput.value = '';
    if (enabledInput) enabledInput.checked = true;
    if (hourInput) hourInput.value = '';
    if (minuteInput) minuteInput.value = '';
    if (secondInput) secondInput.value = '0';
    if (signalSelect) signalSelect.value = '';
    if (descriptionInput) descriptionInput.value = '';

    // 重置重复类型
    const onceRadio = form.querySelector('input[name="repeat-type"][value="once"]');
    if (onceRadio) {
      onceRadio.checked = true;
      this.handleRepeatTypeChange('once');
    }

    // 重置星期选择
    const weekdayCheckboxes = form.querySelectorAll('.weekday-option input[type="checkbox"]');
    weekdayCheckboxes.forEach(checkbox => {
      checkbox.checked = false;
    });
  }

  /**
   * 处理重复类型变化
   */
  handleRepeatTypeChange(repeatType) {
    const weekdaysGroup = document.getElementById('weekdays-group');
    const weekdayCheckboxes = document.querySelectorAll('.weekday-option input[type="checkbox"]');

    if (!weekdaysGroup) return;

    switch (repeatType) {
      case 'once':
        weekdaysGroup.style.display = 'none';
        weekdayCheckboxes.forEach(cb => cb.checked = false);
        break;

      case 'daily':
        weekdaysGroup.style.display = 'block';
        weekdayCheckboxes.forEach(cb => cb.checked = true);
        break;

      case 'weekly':
      case 'custom':
        weekdaysGroup.style.display = 'block';
        // 不自动选择，让用户手动选择
        break;
    }
  }

  /**
   * 保存定时器
   */
  async saveTimer() {
    try {
      const timerData = this.collectTimerFormData();

      if (!this.validateTimerData(timerData)) {
        return;
      }

      let response;

      if (this.timerState.editingTimer) {
        // 更新现有定时器
        response = await this.requestESP32(`/api/timers/${this.timerState.editingTimer.id}`, {
          method: 'PUT',
          body: JSON.stringify(timerData)
        });
      } else {
        // 创建新定时器
        response = await this.requestESP32('/api/timers', {
          method: 'POST',
          body: JSON.stringify(timerData)
        });
      }

      if (response.success) {
        this.handleSuccess(
          this.timerState.editingTimer ? '定时器更新成功' : '定时器创建成功',
          'Save timer'
        );

        // 重新加载定时器列表
        await this.loadModuleData();

        // 关闭模态框
        this.hideTimerModal();
      }

    } catch (error) {
      this.handleError(error, 'Save timer');
    }
  }

  /**
   * 收集定时器表单数据
   */
  collectTimerFormData() {
    const form = document.querySelector('.timer-form');
    if (!form) return null;

    const name = form.querySelector('#timer-name').value.trim();
    const enabled = form.querySelector('#timer-enabled').checked;
    const hour = parseInt(form.querySelector('#timer-hour').value) || 0;
    const minute = parseInt(form.querySelector('#timer-minute').value) || 0;
    const second = parseInt(form.querySelector('#timer-second').value) || 0;
    const signalId = form.querySelector('#timer-signal').value;
    const description = form.querySelector('#timer-description').value.trim();

    // 获取重复类型
    const repeatType = form.querySelector('input[name="repeat-type"]:checked').value;
    const isRepeating = repeatType !== 'once';

    // 获取星期掩码
    let weekdays = 0;
    if (isRepeating) {
      const weekdayCheckboxes = form.querySelectorAll('.weekday-option input[type="checkbox"]:checked');
      weekdayCheckboxes.forEach(checkbox => {
        const dayValue = parseInt(checkbox.value);
        weekdays |= (1 << dayValue);
      });
    }

    return {
      name,
      enabled,
      hour,
      minute,
      second,
      signalId: signalId || null,
      description,
      isRepeating,
      weekdays
    };
  }

  /**
   * 验证定时器数据
   */
  validateTimerData(data) {
    if (!data.name) {
      this.handleError(new Error('请输入定时器名称'), 'Validate timer');
      return false;
    }

    if (data.hour < 0 || data.hour > 23) {
      this.handleError(new Error('小时必须在0-23之间'), 'Validate timer');
      return false;
    }

    if (data.minute < 0 || data.minute > 59) {
      this.handleError(new Error('分钟必须在0-59之间'), 'Validate timer');
      return false;
    }

    if (data.second < 0 || data.second > 59) {
      this.handleError(new Error('秒数必须在0-59之间'), 'Validate timer');
      return false;
    }

    if (data.isRepeating && data.weekdays === 0) {
      this.handleError(new Error('重复定时器必须选择至少一天'), 'Validate timer');
      return false;
    }

    if (!data.signalId) {
      this.handleError(new Error('请选择要执行的信号'), 'Validate timer');
      return false;
    }

    return true;
  }

  /**
   * 切换定时器状态
   */
  async toggleTimer(timerId) {
    try {
      const response = await this.requestESP32(`/api/timers/${timerId}/toggle`, {
        method: 'POST'
      });

      if (response.success) {
        const timer = this.timers.get(timerId);
        if (timer) {
          timer.enabled = !timer.enabled;

          if (timer.enabled) {
            this.activeTimers.add(timerId);
          } else {
            this.activeTimers.delete(timerId);
          }

          // 重新渲染列表
          this.renderTimerList();
          this.updateTimerStats();

          this.handleSuccess(
            `定时器 "${timer.name}" 已${timer.enabled ? '启用' : '禁用'}`,
            'Toggle timer'
          );
        }
      }

    } catch (error) {
      this.handleError(error, 'Toggle timer');
    }
  }

  /**
   * 编辑定时器
   */
  editTimer(timerId) {
    const timer = this.timers.get(timerId);
    if (!timer) return;

    this.timerState.editingTimer = timer;
    this.populateTimerForm(timer);

    const modal = document.getElementById('timer-modal');
    const title = document.getElementById('timer-modal-title');

    if (title) {
      title.textContent = '编辑定时器';
    }

    if (modal) {
      modal.style.display = 'flex';
    }
  }

  /**
   * 填充定时器表单
   */
  populateTimerForm(timer) {
    const form = document.querySelector('.timer-form');
    if (!form) return;

    // 填充基本字段
    const nameInput = form.querySelector('#timer-name');
    const enabledInput = form.querySelector('#timer-enabled');
    const hourInput = form.querySelector('#timer-hour');
    const minuteInput = form.querySelector('#timer-minute');
    const secondInput = form.querySelector('#timer-second');
    const signalSelect = form.querySelector('#timer-signal');
    const descriptionInput = form.querySelector('#timer-description');

    if (nameInput) nameInput.value = timer.name;
    if (enabledInput) enabledInput.checked = timer.enabled;
    if (hourInput) hourInput.value = timer.hour;
    if (minuteInput) minuteInput.value = timer.minute;
    if (secondInput) secondInput.value = timer.second;
    if (signalSelect) signalSelect.value = timer.signalId || '';
    if (descriptionInput) descriptionInput.value = timer.description || '';

    // 设置重复类型
    let repeatType = 'once';
    if (timer.isRepeating) {
      if (timer.weekdays === 127) {
        repeatType = 'daily';
      } else {
        repeatType = 'custom';
      }
    }

    const repeatRadio = form.querySelector(`input[name="repeat-type"][value="${repeatType}"]`);
    if (repeatRadio) {
      repeatRadio.checked = true;
      this.handleRepeatTypeChange(repeatType);
    }

    // 设置星期选择
    if (timer.isRepeating) {
      const weekdayCheckboxes = form.querySelectorAll('.weekday-option input[type="checkbox"]');
      weekdayCheckboxes.forEach(checkbox => {
        const dayValue = parseInt(checkbox.value);
        checkbox.checked = (timer.weekdays & (1 << dayValue)) !== 0;
      });
    }
  }

  /**
   * 删除定时器
   */
  async deleteTimer(timerId) {
    const timer = this.timers.get(timerId);
    if (!timer) return;

    if (!confirm(`确定要删除定时器 "${timer.name}" 吗？`)) {
      return;
    }

    try {
      const response = await this.requestESP32(`/api/timers/${timerId}`, {
        method: 'DELETE'
      });

      if (response.success) {
        this.timers.delete(timerId);
        this.activeTimers.delete(timerId);

        this.renderTimerList();
        this.updateTimerStats();

        this.handleSuccess(`定时器 "${timer.name}" 已删除`, 'Delete timer');
      }

    } catch (error) {
      this.handleError(error, 'Delete timer');
    }
  }

  /**
   * 测试定时器
   */
  async testTimer(timerId) {
    const timer = this.timers.get(timerId);
    if (!timer || !timer.signalId) return;

    try {
      // 发送信号执行请求
      this.emitEvent('signal.emit.request', {
        signalId: timer.signalId,
        source: 'TimerTest',
        timestamp: Date.now()
      });

      this.handleSuccess(`定时器 "${timer.name}" 测试执行`, 'Test timer');

    } catch (error) {
      this.handleError(error, 'Test timer');
    }
  }

  /**
   * 处理定时器触发事件
   */
  handleTimerTriggered(data) {
    const timer = this.timers.get(data.timerId);
    if (timer) {
      console.log(`⏰ Timer "${timer.name}" triggered`);

      // 更新统计
      this.updateTimerStats();

      // 发布定时器触发事件
      this.emitEvent('timer.executed', {
        timerId: data.timerId,
        timerName: timer.name,
        signalId: timer.signalId,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 处理定时器状态变化
   */
  handleTimerStatusChanged(data) {
    const timer = this.timers.get(data.timerId);
    if (timer) {
      timer.enabled = data.enabled;
      timer.nextTriggerTime = data.nextTriggerTime;

      if (timer.enabled) {
        this.activeTimers.add(data.timerId);
      } else {
        this.activeTimers.delete(data.timerId);
      }

      // 重新渲染列表
      this.renderTimerList();
      this.updateTimerStats();
    }
  }

  /**
   * 销毁模块时清理资源
   */
  destroy() {
    // 停止实时时间显示
    if (this.timeDisplay.updateTimer) {
      clearInterval(this.timeDisplay.updateTimer);
      this.timeDisplay.updateTimer = null;
      this.timeDisplay.isRunning = false;
    }

    // 调用父类销毁方法
    super.destroy();
  }
}

// 导出TimerManager类
window.TimerManager = TimerManager;