# R1前端系统完整开发标准文档

## 📋 文档概述

本文档基于以下资料整理：
- **R1前端系统架构标准文档.md** - 核心架构设计
- **ESP32-S3红外控制系统-完整后端开发标准文档.md** - 后端API标准
- **重构后的代码/data** - 完整实现参考（特别是定时模块风格）

## 🎯 设计理念

### 核心原则
1. **事件驱动优先** - 避免无意义循环，所有功能通过事件触发
2. **按需资源分配** - 需要时分配，用完释放，避免资源浪费
3. **模块完全解耦** - 模块间零直接依赖，通过事件通信
4. **硬件性能优化** - 针对ESP32S3双核240MHz处理器和8MB内存优化

### 技术约束例外
- **实时时间显示轮询** - Web环境下定时器调度的唯一可行方案
- **统一定时器管理器** - 系统级调度引擎，100ms精度轮询
- **错误收集器** - 系统稳定性保障，必需的监控功能

## 🏗️ 系统架构

### 1. 核心架构（保持与现有架构文档一致）

#### 1.1 事件总线 (EventBus)
```javascript
class EventBus {
  constructor() {
    this.events = new Map();
    this.performance = {
      totalEvents: 0,
      totalListeners: 0,
      eventHistory: []
    };
  }
  
  // 核心方法
  on(event, callback) { /* 事件监听 */ }
  emit(event, data) { /* 事件发布 */ }
  off(event, callback) { /* 取消监听 */ }
}
```

#### 1.2 ESP32通信器 (ESP32Communicator)
```javascript
class ESP32Communicator {
  constructor(eventBus) {
    this.eventBus = eventBus;
    this.baseURL = this.detectESP32Address();
    this.requestQueue = [];
    this.isConnected = false;
  }
  
  // 核心方法
  async request(endpoint, options) { /* HTTP请求 */ }
  async autoDetectESP32IP() { /* 自动检测IP */ }
}
```

#### 1.3 模块基类 (BaseModule)
```javascript
class BaseModule {
  constructor(eventBus, esp32, moduleName) {
    this.eventBus = eventBus;
    this.esp32 = esp32;
    this.moduleName = moduleName;
    this.state = { loading: false, error: null };
    this.cachedElements = new Map();
  }
  
  // 必需实现的方法
  async setupEventListeners() { /* 事件监听设置 */ }
  async setupUI() { /* UI初始化 */ }
  async loadModuleData() { /* 数据加载 */ }
}
```

### 2. 系统模块架构

#### 2.1 七大核心模块
1. **DisplayManager** - 显示模块 📺
2. **SystemMonitor** - 监控模块 📊  
3. **SignalManager** - 信号管理模块 📡
4. **ControlCenter** - 控制模块 🎮
5. **TimerManager** - 定时模块 ⏰
6. **ConfigManager** - 配置管理模块 ⚙️
7. **OTAManager** - OTA模块 🔄

#### 2.2 模块通信规范
```javascript
// 事件发布标准格式
this.eventBus.emit('module.action.type', {
  source: this.moduleName,
  timestamp: Date.now(),
  data: { /* 具体数据 */ }
});

// 事件监听标准格式
this.eventBus.on('target.event', (data) => {
  this.handleEvent(data);
});
```

## 📁 文件结构标准

### 3.1 目录结构
```
R1前端系统/
├── index.html                 # 主页面
├── css/                       # 样式文件
│   ├── main.css              # 主要样式
│   ├── common-module.css     # 通用模块样式
│   └── modules.css           # 模块专用样式
├── js/                       # JavaScript文件
│   ├── config.js             # 系统配置
│   ├── main.js               # 应用启动
│   ├── core/                 # 核心组件
│   │   ├── event-bus.js      # 事件总线
│   │   ├── esp32-communicator.js # ESP32通信
│   │   └── base-module.js    # 模块基类
│   ├── modules/              # 功能模块
│   │   ├── display-manager.js
│   │   ├── system-monitor.js
│   │   ├── signal-manager.js
│   │   ├── control-center.js
│   │   ├── timer-manager.js
│   │   ├── config-manager.js
│   │   └── ota-manager.js
│   └── utils/                # 工具类
│       ├── unified-timer-manager.js
│       ├── dom-update-manager.js
│       └── virtual-scroll-list.js
└── README.md                 # 项目说明
```

### 3.2 命名规范
- **文件命名**: kebab-case (例: `signal-manager.js`)
- **类命名**: PascalCase (例: `SignalManager`)
- **方法命名**: camelCase (例: `setupEventListeners`)
- **CSS类命名**: kebab-case (例: `.signal-card`)
- **CSS变量**: kebab-case (例: `--primary-color`)

## 🎨 样式系统标准

### 4.1 CSS变量系统
```css
:root {
  /* 主色调 */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-light: rgba(59, 130, 246, 0.1);
  
  /* 背景色 */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  
  /* 文字色 */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  
  /* 间距系统 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* 圆角系统 */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}
```

### 4.2 模块样式规范
每个模块使用独立的CSS类前缀：
- **DisplayManager**: `.display-*`
- **SystemMonitor**: `.monitor-*`
- **SignalManager**: `.signal-*`
- **ControlCenter**: `.control-*`
- **TimerManager**: `.timer-*`
- **ConfigManager**: `.config-*`
- **OTAManager**: `.ota-*`

### 4.3 通用组件样式
```css
/* 按钮系统 */
.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.btn.primary {
  background: var(--primary-color);
  color: white;
}

.btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

/* 卡片系统 */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  transition: all var(--transition-fast);
}

.card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}
```

## 💻 JavaScript开发标准

### 5.1 模块开发模板（基于定时模块风格）
```javascript
/**
 * R1系统 - [模块名称]
 * 基于：R1前端系统架构标准文档.md BaseModule架构标准
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md [相关API]
 * 
 * 功能特性：
 * - [功能1]
 * - [功能2]
 * - [功能3]
 */

class ModuleName extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'ModuleName');
    
    // 模块数据
    this.moduleData = new Map();
    
    // 模块配置
    this.moduleConfig = {
      autoRefresh: true,
      refreshInterval: 5000
    };
    
    // 模块状态
    this.moduleState = {
      isActive: false,
      lastUpdateTime: 0
    };
    
    console.log('✅ ModuleName constructed');
  }

  /**
   * 事件监听器设置 - 必需实现
   */
  async setupEventListeners() {
    await super.setupEventListeners();
    
    // 监听相关事件
    this.eventBus.on('target.event', (data) => {
      this.handleEvent(data);
    });
    
    console.log('📡 ModuleName: Event listeners setup complete');
  }

  /**
   * UI设置 - 必需实现
   */
  async setupUI() {
    console.log('🎨 ModuleName: Setting up UI...');
    
    // 缓存UI元素
    this.cacheElement('container', '#module-container');
    
    // 创建界面
    this.createModuleUI();
    
    // 设置事件委托
    this.setupEventDelegation();
    
    console.log('✅ ModuleName: UI setup complete');
  }

  /**
   * 数据加载 - 必需实现
   */
  async loadModuleData() {
    console.log('📊 ModuleName: Loading data...');
    
    try {
      this.state.loading = true;
      
      // 加载数据
      await this.loadData();
      
      // 渲染界面
      this.renderInterface();
      
      this.handleSuccess('模块初始化完成', 'Load data');
      
    } catch (error) {
      this.handleError(error, 'Load data');
    } finally {
      this.state.loading = false;
    }
  }

  /**
   * 销毁模块时清理资源
   */
  destroy() {
    // 清理定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
    }
    
    // 调用父类销毁方法
    super.destroy();
  }
}

// 导出模块类
window.ModuleName = ModuleName;
```

### 5.2 统一定时器管理器使用规范
```javascript
// 添加定时器
window.UnifiedTimerManager.addTimer(
  'module-refresh',           // 定时器ID
  () => this.refreshData(),   // 回调函数
  5000,                       // 间隔时间(ms)
  true                        // 是否重复
);

// 删除定时器
window.UnifiedTimerManager.removeTimer('module-refresh');

// 暂停/恢复定时器
window.UnifiedTimerManager.pauseTimer('module-refresh');
window.UnifiedTimerManager.resumeTimer('module-refresh');
```

### 5.3 DOM更新管理器使用规范
```javascript
// 批量DOM更新
window.DOMUpdateManager.batchUpdate(() => {
  element1.textContent = 'new text';
  element2.style.display = 'block';
  element3.classList.add('active');
});

// 虚拟滚动列表
const virtualList = new VirtualScrollList({
  container: document.getElementById('list-container'),
  itemHeight: 60,
  renderItem: (item, index) => this.renderListItem(item, index)
});
```

## 🔧 API通信标准

### 6.1 ESP32 API端点规范
```javascript
// 标准API端点定义
const API_ENDPOINTS = {
  // 信号管理
  signals: {
    list: '/api/signals',
    create: '/api/signals',
    get: '/api/signals/{id}',
    update: '/api/signals/{id}',
    delete: '/api/signals/{id}',
    emit: '/api/signals/{id}/emit'
  },
  
  // 定时器管理
  timers: {
    list: '/api/timers',
    create: '/api/timers',
    toggle: '/api/timers/{id}/toggle'
  },
  
  // 系统管理
  system: {
    status: '/api/system/status',
    performance: '/api/system/performance',
    hardware: '/api/system/hardware'
  }
};
```

### 6.2 请求响应格式
```javascript
// 标准请求格式
const response = await this.requestESP32('/api/endpoint', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
});

// 标准响应格式
{
  "success": true,
  "data": { /* 响应数据 */ },
  "message": "操作成功",
  "timestamp": 1640995200000
}
```

## 📱 用户界面标准

### 7.1 响应式设计
```css
/* 移动端适配 */
@media (max-width: 768px) {
  .module-container {
    padding: var(--spacing-md);
  }
  
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 桌面端适配 */
@media (min-width: 1025px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}
```

### 7.2 交互反馈标准
```javascript
// 成功反馈
this.handleSuccess('操作成功', 'Operation');

// 错误反馈
this.handleError(error, 'Operation');

// 加载状态
this.state.loading = true;
// ... 执行操作
this.state.loading = false;
```

## 🚀 性能优化标准

### 8.1 内存管理
- 使用Map和Set替代普通对象进行数据存储
- 及时清理事件监听器和定时器
- 使用虚拟滚动处理大量数据

### 8.2 渲染优化
- 使用事件委托减少事件监听器数量
- 批量DOM更新避免重排重绘
- 使用CSS变量实现主题切换

### 8.3 网络优化
- 请求去重和缓存机制
- 自动重试和错误恢复
- 连接状态监控

## 🔍 调试和测试标准

### 9.1 日志规范
```javascript
// 模块构造
console.log('✅ ModuleName constructed');

// 事件监听
console.log('📡 ModuleName: Event listeners setup complete');

// UI设置
console.log('🎨 ModuleName: Setting up UI...');

// 数据加载
console.log('📊 ModuleName: Loading data...');

// 错误处理
console.error('❌ ModuleName: Error occurred:', error);
```

### 9.2 性能监控
```javascript
// 性能统计
const performanceStats = {
  moduleInitTime: Date.now() - startTime,
  memoryUsage: performance.memory?.usedJSHeapSize || 0,
  eventCount: this.eventBus.performance.totalEvents
};
```

## 📚 开发工作流

### 10.1 模块开发流程
1. **需求分析** - 确定模块功能和API需求
2. **架构设计** - 基于BaseModule设计模块结构
3. **UI设计** - 创建响应式界面和交互
4. **功能实现** - 实现核心业务逻辑
5. **测试验证** - 功能测试和性能测试
6. **文档更新** - 更新API文档和使用说明

### 10.2 代码审查标准
- ✅ 继承BaseModule并实现所有必需方法
- ✅ 使用事件驱动架构，避免直接依赖
- ✅ 遵循命名规范和代码风格
- ✅ 包含完整的错误处理
- ✅ 添加适当的日志和注释
- ✅ 实现资源清理机制

## 🎯 最佳实践

### 11.1 基于定时模块的优秀实践
1. **详细的注释说明** - 包含技术约束例外的说明
2. **完整的性能监控** - 统计执行次数、延迟等指标
3. **优雅的资源管理** - 自动启停、批量操作
4. **丰富的调试信息** - 详细的日志输出
5. **灵活的配置选项** - 支持暂停、恢复、重置

### 11.2 代码质量标准
- 单一职责原则
- 开闭原则
- 依赖倒置原则
- 接口隔离原则
- 最小知识原则

## 📋 附录A：模块详细规范

### A.1 DisplayManager 显示模块规范
```javascript
// 显示模块特有功能
class DisplayManager extends BaseModule {
  // 仪表盘组件管理
  dashboardWidgets: Map<string, Widget>

  // 实时数据更新
  updateRealtimeData(data)

  // 布局管理
  toggleLayout()
  switchLayout(layoutType)

  // 全屏控制
  toggleFullscreen()
}

// CSS类前缀: .display-*
.display-dashboard { /* 仪表盘容器 */ }
.display-widget { /* 组件卡片 */ }
.display-realtime { /* 实时数据 */ }
```

### A.2 SystemMonitor 监控模块规范
```javascript
// 监控模块特有功能
class SystemMonitor extends BaseModule {
  // 性能数据收集
  collectPerformanceData()

  // 系统状态监控
  monitorSystemStatus()

  // 日志管理
  manageLogs()

  // 错误统计
  trackErrors()
}

// CSS类前缀: .monitor-*
.monitor-chart { /* 性能图表 */ }
.monitor-status { /* 状态指示器 */ }
.monitor-log { /* 日志条目 */ }
```

### A.3 SignalManager 信号管理模块规范
```javascript
// 信号管理模块特有功能
class SignalManager extends BaseModule {
  // 信号CRUD操作
  createSignal(signalData)
  updateSignal(id, signalData)
  deleteSignal(id)

  // 信号学习
  learnSignal()

  // 批量操作
  batchEmitSignals(signalIds)
  batchDeleteSignals(signalIds)

  // 导入导出
  exportSignals()
  importSignals(file)
}

// CSS类前缀: .signal-*
.signal-card { /* 信号卡片 */ }
.signal-grid { /* 网格布局 */ }
.signal-batch { /* 批量操作 */ }
```

### A.4 ControlCenter 控制模块规范
```javascript
// 控制中心模块特有功能
class ControlCenter extends BaseModule {
  // 快速控制
  quickControl()

  // 批量控制
  batchControl(signals, options)

  // 任务管理
  createTask(taskData)
  monitorTasks()

  // 最近使用
  updateRecentSignals()
}

// CSS类前缀: .control-*
.control-quick { /* 快速控制面板 */ }
.control-batch { /* 批量控制 */ }
.control-task { /* 任务监控 */ }
```

### A.5 TimerManager 定时模块规范（参考实现）
```javascript
// 定时管理模块特有功能
class TimerManager extends BaseModule {
  // 定时器CRUD
  createTimer(timerData)
  updateTimer(id, timerData)
  deleteTimer(id)

  // 定时器控制
  toggleTimer(id)
  testTimer(id)

  // 时间显示
  updateCurrentTime()

  // 定时器调度
  scheduleTimer(timer)
}

// CSS类前缀: .timer-*
.timer-card { /* 定时器卡片 */ }
.timer-form { /* 定时器表单 */ }
.timer-status { /* 状态显示 */ }
```

### A.6 ConfigManager 配置管理模块规范
```javascript
// 配置管理模块特有功能
class ConfigManager extends BaseModule {
  // 配置管理
  loadConfig(category)
  saveConfig(category, data)
  resetConfig(category)

  // 导入导出
  exportConfig(category)
  importConfig(file)

  // 配置验证
  validateConfig(data)

  // 自动保存
  enableAutoSave()
  disableAutoSave()
}

// CSS类前缀: .config-*
.config-panel { /* 配置面板 */ }
.config-tab { /* 配置标签 */ }
.config-item { /* 配置项 */ }
```

### A.7 OTAManager OTA模块规范
```javascript
// OTA管理模块特有功能
class OTAManager extends BaseModule {
  // 版本管理
  checkForUpdates()
  getCurrentVersion()

  // 更新控制
  startUpdate(type)
  cancelUpdate()

  // 进度监控
  monitorProgress()

  // 安全认证
  authenticate(password)
}

// CSS类前缀: .ota-*
.ota-version { /* 版本信息 */ }
.ota-progress { /* 更新进度 */ }
.ota-auth { /* 认证对话框 */ }
```

## 📋 附录B：工具类规范

### B.1 UnifiedTimerManager 统一定时器管理器
```javascript
// 全局定时器管理器（基于重构后代码的优秀实现）
class UnifiedTimerManager {
  // 核心方法
  addTimer(id, callback, interval, repeat = false)
  removeTimer(id)
  pauseTimer(id)
  resumeTimer(id)
  resetTimer(id)

  // 批量操作
  addTimers(timerConfigs)
  removeTimers(ids)
  clear()

  // 性能监控
  getPerformanceStats()
  getTimerStates()
  optimize()

  // 生命周期
  start()
  stop()
  destroy()
}

// 使用示例
window.UnifiedTimerManager.addTimer('refresh-data', () => {
  this.refreshData();
}, 5000, true);
```

### B.2 DOMUpdateManager DOM更新管理器
```javascript
// DOM批量更新管理器
class DOMUpdateManager {
  // 批量更新
  batchUpdate(updateFunction)

  // 延迟更新
  scheduleUpdate(updateFunction, delay = 16)

  // 虚拟滚动
  createVirtualList(options)

  // 性能监控
  getUpdateStats()
}

// 使用示例
window.DOMUpdateManager.batchUpdate(() => {
  element1.textContent = 'new text';
  element2.style.display = 'block';
  element3.classList.add('active');
});
```

### B.3 VirtualScrollList 虚拟滚动列表
```javascript
// 虚拟滚动列表（处理大量数据）
class VirtualScrollList {
  constructor(options) {
    this.container = options.container;
    this.itemHeight = options.itemHeight;
    this.renderItem = options.renderItem;
    this.data = [];
    this.visibleRange = { start: 0, end: 0 };
  }

  // 核心方法
  setData(data)
  scrollToIndex(index)
  refresh()
  destroy()
}

// 使用示例
const virtualList = new VirtualScrollList({
  container: document.getElementById('signal-list'),
  itemHeight: 80,
  renderItem: (signal, index) => this.renderSignalItem(signal, index)
});
```

## 📋 附录C：CSS架构详细规范

### C.1 CSS文件组织结构
```
css/
├── main.css              # 主样式文件
│   ├── CSS变量定义
│   ├── 全局重置样式
│   ├── 基础组件样式
│   └── 响应式断点
├── common-module.css     # 通用模块样式
│   ├── 模块容器样式
│   ├── 通用卡片样式
│   ├── 通用按钮样式
│   └── 通用表单样式
└── modules.css           # 模块专用样式
    ├── .display-* 样式
    ├── .monitor-* 样式
    ├── .signal-* 样式
    ├── .control-* 样式
    ├── .timer-* 样式
    ├── .config-* 样式
    └── .ota-* 样式
```

### C.2 CSS变量系统完整定义
```css
:root {
  /* 颜色系统 */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-light: rgba(59, 130, 246, 0.1);
  --primary-dark: #1d4ed8;

  --secondary-color: #64748b;
  --secondary-hover: #475569;
  --secondary-light: rgba(100, 116, 139, 0.1);

  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;

  /* 背景色系统 */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  --bg-overlay: rgba(0, 0, 0, 0.8);

  /* 文字色系统 */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  --text-white: #ffffff;
  --text-black: #000000;

  /* 边框色系统 */
  --border-color: #334155;
  --border-light: #475569;
  --border-dark: #1e293b;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* 间距系统 */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 3rem;      /* 48px */

  /* 圆角系统 */
  --radius-sm: 0.25rem;     /* 4px */
  --radius-md: 0.5rem;      /* 8px */
  --radius-lg: 0.75rem;     /* 12px */
  --radius-xl: 1rem;        /* 16px */
  --radius-2xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;

  /* 字体系统 */
  --font-size-xs: 0.75rem;  /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-md: 1rem;     /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem;  /* 20px */
  --font-size-2xl: 1.5rem;  /* 24px */

  /* 过渡动画系统 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Z-index系统 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}
```

### C.3 响应式断点系统
```css
/* 移动端优先的响应式设计 */

/* 超小屏幕 (手机竖屏) */
@media (max-width: 575.98px) {
  :root {
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
  }

  .container {
    padding: var(--spacing-sm);
  }
}

/* 小屏幕 (手机横屏) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* 中等屏幕 (平板) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .sidebar {
    width: 200px;
  }
}

/* 大屏幕 (桌面) */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 超大屏幕 (大桌面) */
@media (min-width: 1200px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
  }
}
```

## 📋 附录D：API通信详细规范

### D.1 完整API端点定义
```javascript
const API_ENDPOINTS = {
  // 信号管理API
  signals: {
    list: '/api/signals',
    create: '/api/signals',
    get: '/api/signals/{id}',
    update: '/api/signals/{id}',
    delete: '/api/signals/{id}',
    emit: '/api/signals/{id}/emit',
    learn: '/api/signals/learn',
    import: '/api/signals/import',
    export: '/api/signals/export',
    batch: '/api/signals/batch',
    search: '/api/signals/search'
  },

  // 定时器管理API
  timers: {
    list: '/api/timers',
    create: '/api/timers',
    get: '/api/timers/{id}',
    update: '/api/timers/{id}',
    delete: '/api/timers/{id}',
    toggle: '/api/timers/{id}/toggle',
    test: '/api/timers/{id}/test'
  },

  // 任务管理API
  tasks: {
    list: '/api/tasks',
    create: '/api/tasks',
    get: '/api/tasks/{id}',
    update: '/api/tasks/{id}',
    delete: '/api/tasks/{id}',
    start: '/api/tasks/{id}/start',
    stop: '/api/tasks/{id}/stop',
    pause: '/api/tasks/{id}/pause',
    resume: '/api/tasks/{id}/resume'
  },

  // 系统管理API
  system: {
    status: '/api/system/status',
    performance: '/api/system/performance',
    hardware: '/api/system/hardware',
    reset: '/api/system/reset',
    logs: '/api/system/logs',
    time: '/api/system/time'
  },

  // OTA管理API
  ota: {
    status: '/api/ota/status',
    login: '/api/ota/login',
    firmware: '/api/ota/firmware',
    filesystem: '/api/ota/filesystem',
    progress: '/api/ota/progress'
  },

  // 配置管理API
  config: {
    get: '/api/config',
    update: '/api/config',
    export: '/api/config/export',
    import: '/api/config/import',
    reset: '/api/config/reset',
    backup: '/api/config/backup',
    restore: '/api/config/restore'
  }
};
```

### D.2 请求响应标准格式
```javascript
// 标准请求格式
const requestOptions = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Session-ID': this.sessionId,
    'X-Request-ID': this.generateRequestId()
  },
  body: JSON.stringify({
    data: requestData,
    timestamp: Date.now(),
    source: this.moduleName
  })
};

// 标准响应格式
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": 1640995200000,
  "requestId": "req_123456789",
  "version": "1.0.0"
}

// 错误响应格式
{
  "success": false,
  "error": {
    "code": "SIGNAL_NOT_FOUND",
    "message": "信号不存在",
    "details": "Signal with ID 123 was not found"
  },
  "timestamp": 1640995200000,
  "requestId": "req_123456789"
}
```

### D.3 错误处理标准
```javascript
// 统一错误处理
async handleAPIRequest(endpoint, options) {
  try {
    const response = await this.requestESP32(endpoint, options);

    if (response.success) {
      return response.data;
    } else {
      throw new APIError(response.error);
    }
  } catch (error) {
    if (error instanceof NetworkError) {
      this.handleNetworkError(error);
    } else if (error instanceof APIError) {
      this.handleAPIError(error);
    } else {
      this.handleUnknownError(error);
    }
    throw error;
  }
}

// 错误类型定义
class APIError extends Error {
  constructor(errorData) {
    super(errorData.message);
    this.code = errorData.code;
    this.details = errorData.details;
  }
}

class NetworkError extends Error {
  constructor(message) {
    super(message);
    this.name = 'NetworkError';
  }
}
```

---

**本文档基于R1前端系统架构标准文档.md的核心架构，结合重构后代码的优秀实践，特别是定时模块的实现风格，形成完整的前端开发标准。文档涵盖了架构设计、文件结构、样式系统、JavaScript开发、API通信、性能优化等全方位的开发规范。**
