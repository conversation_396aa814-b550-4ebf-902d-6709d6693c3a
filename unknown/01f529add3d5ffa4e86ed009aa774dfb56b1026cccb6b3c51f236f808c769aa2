/*
 * ESP32-S3 IR System - Complete Refactored Version
 * 
 * 完全重构的ESP32-S3红外控制系统
 * 
 * 特性:
 * - 彻底的前后端职责分离
 * - PSRAM优雅降级机制
 * - 智能内存管理
 * - 完整的RESTful API
 * - 纯UI层前端
 * 
 * 硬件要求:
 * - ESP32-S3开发板
 * - OPI PSRAM (可选，支持优雅降级)
 * - 红外发射器和接收器
 * 
 * 作者: AI Assistant
 * 版本: 2.0.0 (完全重构版)
 * 日期: 2024-12-24
 */

// ==================== 系统包含文件 ====================
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>

// ==================== 核心系统组件 ====================
#include "src/core/SystemManager.h"
#include "src/core/PSRAMManager.h"
#include "config/system-config.h"

// ==================== 全局变量 ====================
SystemManager* g_systemManager = nullptr;

// ==================== 系统初始化 ====================
void setup() {
    // 1. 基础硬件初始化
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("========================================");
    Serial.println("🚀 ESP32-S3 IR System v2.0 Starting...");
    Serial.println("📋 Complete Refactored Architecture");
    Serial.println("========================================");
    
    // 2. PSRAM智能检测和初始化
    SystemMode systemMode = PSRAMManager::initialize();
    
    if (systemMode == SystemMode::HIGH_PERFORMANCE) {
        Serial.println("🚀 High Performance Mode Activated (PSRAM Available)");
    } else {
        Serial.println("⚡ Standard Mode Activated (RAM Only)");
    }
    
    // 3. 文件系统初始化
    if (!SPIFFS.begin(true)) {
        Serial.println("❌ SPIFFS initialization failed");
        Serial.println("⚠️  System will continue with limited functionality");
    } else {
        Serial.println("✅ SPIFFS initialized successfully");
    }
    
    // 4. 系统管理器创建和初始化
    g_systemManager = new SystemManager(systemMode);
    if (!g_systemManager || !g_systemManager->initialize()) {
        Serial.println("❌ CRITICAL ERROR: SystemManager initialization failed");
        Serial.println("🔄 System will restart in 5 seconds...");
        delay(5000);
        ESP.restart();
    }
    
    Serial.println("✅ System initialization completed successfully");
    Serial.println("========================================");
    
    // 5. 打印系统信息
    printSystemInfo();
}

// ==================== 主循环 ====================
void loop() {
    if (g_systemManager) {
        g_systemManager->handleLoop();
    }
    
    // 小延迟以避免看门狗触发
    delay(1);
}

// ==================== 系统信息打印 ====================
void printSystemInfo() {
    Serial.println("📊 System Information:");
    Serial.printf("   Chip Model: %s\n", ESP.getChipModel());
    Serial.printf("   Chip Revision: %d\n", ESP.getChipRevision());
    Serial.printf("   CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("   Flash Size: %d MB\n", ESP.getFlashChipSize() / (1024 * 1024));
    Serial.printf("   Free Heap: %d bytes\n", ESP.getFreeHeap());
    
    if (psramFound()) {
        Serial.printf("   PSRAM Size: %d MB\n", ESP.getPsramSize() / (1024 * 1024));
        Serial.printf("   Free PSRAM: %d bytes\n", ESP.getFreePsram());
    } else {
        Serial.println("   PSRAM: Not available");
    }
    
    if (g_systemManager) {
        Serial.printf("   System Mode: %s\n", g_systemManager->getSystemModeString());
        auto capacity = g_systemManager->getSystemCapacity();
        Serial.printf("   Max Signals: %d\n", capacity.maxSignals);
        Serial.printf("   Max Tasks: %d\n", capacity.maxTasks);
        Serial.printf("   Max Timers: %d\n", capacity.maxTimers);
        Serial.printf("   Buffer Size: %d bytes\n", capacity.bufferSize);
    }
    
    Serial.println("========================================");
}

// ==================== 错误处理 ====================
void handleCriticalError(const char* error) {
    Serial.printf("💥 CRITICAL ERROR: %s\n", error);
    Serial.println("🔄 System will restart in 10 seconds...");
    
    // 尝试保存错误日志
    if (g_systemManager) {
        g_systemManager->logError(error);
    }
    
    delay(10000);
    ESP.restart();
}

// ==================== 内存监控 ====================
void printMemoryStatus() {
    Serial.println("📊 Memory Status:");
    Serial.printf("   Free Heap: %d bytes (%.2f KB)\n", 
                 ESP.getFreeHeap(), ESP.getFreeHeap() / 1024.0);
    
    if (psramFound()) {
        Serial.printf("   Free PSRAM: %d bytes (%.2f KB)\n", 
                     ESP.getFreePsram(), ESP.getFreePsram() / 1024.0);
    }
    
    if (g_systemManager) {
        g_systemManager->printDetailedMemoryStatus();
    }
}

// ==================== 系统重启 ====================
void systemRestart() {
    Serial.println("🔄 System restart requested");
    
    if (g_systemManager) {
        g_systemManager->shutdown();
        delete g_systemManager;
        g_systemManager = nullptr;
    }
    
    delay(1000);
    ESP.restart();
}

// ==================== 工厂重置 ====================
void factoryReset() {
    Serial.println("🏭 Factory reset requested");
    
    if (g_systemManager) {
        g_systemManager->factoryReset();
    }
    
    // 清除SPIFFS
    SPIFFS.format();
    
    // 清除WiFi凭据
    WiFi.disconnect(true);
    
    Serial.println("✅ Factory reset completed - restarting...");
    delay(2000);
    ESP.restart();
}

// ==================== 调试功能 ====================
#ifdef DEBUG_MODE
void debugPrintTasks() {
    if (g_systemManager) {
        g_systemManager->debugPrintAllTasks();
    }
}

void debugPrintSignals() {
    if (g_systemManager) {
        g_systemManager->debugPrintAllSignals();
    }
}

void debugPrintSystemState() {
    if (g_systemManager) {
        g_systemManager->debugPrintSystemState();
    }
}
#endif

// ==================== 版本信息 ====================
const char* getSystemVersion() {
    return "2.0.0-refactored";
}

const char* getBuildDate() {
    return __DATE__ " " __TIME__;
}

const char* getSystemDescription() {
    return "ESP32-S3 IR Control System - Complete Refactored Architecture";
}
