/**
 * 文件系统管理器 - 实现文件
 * 负责文件系统操作、数据持久化、备份恢复
 */

#include "FileSystemManager.h"

// 文件路径常量定义
const char* FileSystemManager::SIGNALS_FILE = "/signals.json";
const char* FileSystemManager::CONFIG_FILE = "/config.json";
const char* FileSystemManager::LOGS_FILE = "/logs.json";
const char* FileSystemManager::HISTORY_FILE = "/history.json";
const char* FileSystemManager::BACKUP_DIR = "/backup";
const char* FileSystemManager::TEMP_DIR = "/temp";

// FileInfo类方法实现

JsonObject FileInfo::toJson(JsonDocument& doc) const {
    JsonObject obj = doc.createNestedObject();
    
    obj["path"] = path;
    obj["name"] = name;
    obj["size"] = size;
    obj["lastModified"] = lastModified;
    obj["type"] = static_cast<int>(type);
    obj["exists"] = exists;
    
    return obj;
}

// FileSystemManager类方法实现

FileSystemManager::FileSystemManager() 
    : initialized(false), lastBackupTime(0), totalFilesCreated(0), 
      totalFilesDeleted(0), autoBackupEnabled(false), debugEnabled(true) {
}

FileSystemManager::~FileSystemManager() {
    // 析构时执行最后一次备份
    if (autoBackupEnabled && initialized) {
        performMaintenance();
    }
}

bool FileSystemManager::init() {
    Serial.println("📁 初始化文件系统管理器...");
    
    if (!SPIFFS.begin(true)) {
        Serial.println("❌ SPIFFS初始化失败");
        return false;
    }
    
    // 创建必要的目录
    createDirectoryIfNotExists(BACKUP_DIR);
    createDirectoryIfNotExists(TEMP_DIR);
    
    // 检查文件系统状态
    size_t totalBytes = SPIFFS.totalBytes();
    size_t usedBytes = SPIFFS.usedBytes();
    
    Serial.printf("✅ 文件系统初始化成功: %d/%d bytes (%.1f%%)\n", 
                 usedBytes, totalBytes, (float)usedBytes/totalBytes*100);
    
    initialized = true;
    return true;
}

bool FileSystemManager::fileExists(const String& path) const {
    return SPIFFS.exists(path);
}

bool FileSystemManager::createFile(const String& path, const String& content) {
    if (!isValidPath(path)) {
        Serial.printf("❌ 无效的文件路径: %s\n", path.c_str());
        return false;
    }
    
    File file = SPIFFS.open(path, "w");
    if (!file) {
        Serial.printf("❌ 无法创建文件: %s\n", path.c_str());
        return false;
    }
    
    size_t bytesWritten = file.print(content);
    file.close();
    
    if (bytesWritten > 0) {
        totalFilesCreated++;
        if (debugEnabled) {
            Serial.printf("✅ 文件创建成功: %s (%d bytes)\n", path.c_str(), bytesWritten);
        }
        return true;
    }
    
    return false;
}

bool FileSystemManager::deleteFile(const String& path) {
    if (!fileExists(path)) {
        return false;
    }
    
    if (SPIFFS.remove(path)) {
        totalFilesDeleted++;
        if (debugEnabled) {
            Serial.printf("✅ 文件删除成功: %s\n", path.c_str());
        }
        return true;
    }
    
    return false;
}

String FileSystemManager::readFile(const String& path) const {
    if (!fileExists(path)) {
        return "";
    }
    
    File file = SPIFFS.open(path, "r");
    if (!file) {
        return "";
    }
    
    String content = file.readString();
    file.close();
    
    return content;
}

bool FileSystemManager::writeFile(const String& path, const String& content) {
    if (!isValidPath(path) || !isValidSize(content.length())) {
        return false;
    }
    
    File file = SPIFFS.open(path, "w");
    if (!file) {
        return false;
    }
    
    size_t bytesWritten = file.print(content);
    file.close();
    
    if (debugEnabled && bytesWritten > 0) {
        Serial.printf("✅ 文件写入成功: %s (%d bytes)\n", path.c_str(), bytesWritten);
    }
    
    return bytesWritten > 0;
}

bool FileSystemManager::readJsonFile(const String& path, JsonDocument& doc) const {
    String content = readFile(path);
    if (content.isEmpty()) {
        return false;
    }
    
    DeserializationError error = deserializeJson(doc, content);
    if (error) {
        Serial.printf("❌ JSON文件解析失败: %s (%s)\n", path.c_str(), error.c_str());
        return false;
    }
    
    return true;
}

bool FileSystemManager::writeJsonFile(const String& path, const JsonDocument& doc) {
    String content;
    serializeJson(doc, content);
    
    return writeFile(path, content);
}

FileInfo FileSystemManager::getFileInfo(const String& path) const {
    FileInfo info;
    info.path = path;
    info.name = path.substring(path.lastIndexOf('/') + 1);
    info.exists = fileExists(path);
    
    if (info.exists) {
        File file = SPIFFS.open(path, "r");
        if (file) {
            info.size = file.size();
            info.lastModified = file.getLastWrite();
            file.close();
        }
    }
    
    return info;
}

std::vector<String> FileSystemManager::listFiles(const String& path) const {
    std::vector<String> files;
    
    File root = SPIFFS.open(path);
    if (!root || !root.isDirectory()) {
        return files;
    }
    
    File file = root.openNextFile();
    while (file) {
        if (!file.isDirectory()) {
            files.push_back(String(file.name()));
        }
        file = root.openNextFile();
    }
    
    return files;
}

bool FileSystemManager::saveSignals(const JsonDocument& doc) {
    return writeJsonFile(SIGNALS_FILE, doc);
}

bool FileSystemManager::loadSignals(JsonDocument& doc) const {
    return readJsonFile(SIGNALS_FILE, doc);
}

bool FileSystemManager::saveConfig(const JsonDocument& doc) {
    return writeJsonFile(CONFIG_FILE, doc);
}

bool FileSystemManager::loadConfig(JsonDocument& doc) const {
    return readJsonFile(CONFIG_FILE, doc);
}

bool FileSystemManager::saveLogs(const JsonDocument& doc) {
    return writeJsonFile(LOGS_FILE, doc);
}

bool FileSystemManager::loadLogs(JsonDocument& doc) const {
    return readJsonFile(LOGS_FILE, doc);
}

bool FileSystemManager::saveHistory(const JsonDocument& doc) {
    return writeJsonFile(HISTORY_FILE, doc);
}

bool FileSystemManager::loadHistory(JsonDocument& doc) const {
    return readJsonFile(HISTORY_FILE, doc);
}

size_t FileSystemManager::getTotalBytes() const {
    return SPIFFS.totalBytes();
}

size_t FileSystemManager::getUsedBytes() const {
    return SPIFFS.usedBytes();
}

size_t FileSystemManager::getFreeBytes() const {
    return getTotalBytes() - getUsedBytes();
}

float FileSystemManager::getUsagePercentage() const {
    size_t total = getTotalBytes();
    if (total == 0) return 0.0;
    return (float)getUsedBytes() / total * 100.0;
}

JsonObject FileSystemManager::getFileSystemInfo() const {
    JsonDocument doc;
    JsonObject info = doc.to<JsonObject>();
    
    info["total_bytes"] = getTotalBytes();
    info["used_bytes"] = getUsedBytes();
    info["free_bytes"] = getFreeBytes();
    info["usage_percentage"] = getUsagePercentage();
    info["files_created"] = totalFilesCreated;
    info["files_deleted"] = totalFilesDeleted;
    info["auto_backup_enabled"] = autoBackupEnabled;
    info["initialized"] = initialized;
    
    return info;
}

bool FileSystemManager::cleanupTempFiles() {
    std::vector<String> tempFiles = listFiles(TEMP_DIR);
    int cleanedCount = 0;
    
    for (const String& file : tempFiles) {
        String fullPath = String(TEMP_DIR) + "/" + file;
        if (deleteFile(fullPath)) {
            cleanedCount++;
        }
    }
    
    if (debugEnabled && cleanedCount > 0) {
        Serial.printf("🧹 清理了 %d 个临时文件\n", cleanedCount);
    }
    
    return true;
}

void FileSystemManager::performMaintenance() {
    if (!initialized) return;
    
    Serial.println("🔧 执行文件系统维护...");
    
    // 清理临时文件
    cleanupTempFiles();
    
    // 检查存储空间
    float usage = getUsagePercentage();
    if (usage > 90.0) {
        Serial.printf("⚠️ 存储空间不足: %.1f%%\n", usage);
        // 可以在这里实现自动清理逻辑
    }
    
    Serial.println("✅ 文件系统维护完成");
}

bool FileSystemManager::createDirectoryIfNotExists(const String& path) {
    // SPIFFS不支持真正的目录，这里只是占位实现
    return true;
}

bool FileSystemManager::isValidPath(const String& path) const {
    return !path.isEmpty() && path.startsWith("/") && path.length() < 64;
}

bool FileSystemManager::isValidSize(size_t size) const {
    return size <= MAX_FILE_SIZE;
}

void FileSystemManager::printFileSystemInfo() const {
    Serial.println("=== 文件系统信息 ===");
    Serial.printf("总空间: %d bytes\n", getTotalBytes());
    Serial.printf("已用空间: %d bytes\n", getUsedBytes());
    Serial.printf("剩余空间: %d bytes\n", getFreeBytes());
    Serial.printf("使用率: %.1f%%\n", getUsagePercentage());
    Serial.printf("已创建文件: %d\n", totalFilesCreated);
    Serial.printf("已删除文件: %d\n", totalFilesDeleted);
    Serial.println("==================");
}
