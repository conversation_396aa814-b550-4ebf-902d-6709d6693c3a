#ifndef WS_MANAGER_H
#define WS_MANAGER_H

#include <Arduino.h>
#include <AsyncWebSocket.h>
#include <ArduinoJson.h>
#include "system-config.h"

// Forward declarations
class DataManager;
class IRController;
class TaskManager;

/**
 * WebSocket管理器类
 * 
 * 负责WebSocket连接管理和实时通信
 * 
 * 核心功能：
 * - WebSocket连接管理
 * - 实时消息广播
 * - 客户端状态同步
 * - 事件处理
 */
class WSManager {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     */
    WSManager();
    
    /**
     * 析构函数
     */
    ~WSManager();
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化WebSocket管理器
     * @param server AsyncWebServer指针
     * @return bool 初始化是否成功
     */
    bool initialize(AsyncWebServer* server);
    
    /**
     * 关闭WebSocket管理器
     */
    void shutdown();
    
    /**
     * 检查健康状态
     * @return bool 是否健康
     */
    bool isHealthy() const;
    
    /**
     * 设置依赖组件
     * @param dataManager 数据管理器
     * @param irController 红外控制器
     * @param taskManager 任务管理器
     */
    void setDependencies(DataManager* dataManager, IRController* irController, TaskManager* taskManager);
    
    // ==================== 连接管理 ====================
    
    /**
     * 获取连接数量
     * @return int 当前连接数
     */
    int getConnectionCount() const;
    
    /**
     * 获取连接信息
     * @return DynamicJsonDocument 连接信息
     */
    DynamicJsonDocument getConnectionInfo() const;
    
    /**
     * 断开指定客户端
     * @param clientId 客户端ID
     * @return bool 断开是否成功
     */
    bool disconnectClient(uint32_t clientId);
    
    /**
     * 断开所有客户端
     */
    void disconnectAllClients();
    
    // ==================== 消息发送 ====================
    
    /**
     * 广播消息给所有客户端
     * @param message JSON消息
     */
    void broadcastMessage(const DynamicJsonDocument& message);
    
    /**
     * 发送消息给指定客户端
     * @param clientId 客户端ID
     * @param message JSON消息
     * @return bool 发送是否成功
     */
    bool sendMessage(uint32_t clientId, const DynamicJsonDocument& message);
    
    /**
     * 发送文本消息
     * @param clientId 客户端ID
     * @param text 文本内容
     * @return bool 发送是否成功
     */
    bool sendTextMessage(uint32_t clientId, const String& text);
    
    /**
     * 广播文本消息
     * @param text 文本内容
     */
    void broadcastTextMessage(const String& text);
    
    // ==================== 状态同步 ====================
    
    /**
     * 同步系统状态给所有客户端
     */
    void syncSystemStatus();
    
    /**
     * 同步信号列表给所有客户端
     */
    void syncSignalList();
    
    /**
     * 同步任务状态给所有客户端
     */
    void syncTaskStatus();
    
    /**
     * 同步执行进度给所有客户端
     * @param progress 进度信息
     */
    void syncExecutionProgress(const DynamicJsonDocument& progress);
    
    // ==================== 事件处理 ====================
    
    /**
     * 处理WebSocket事件
     * @param server WebSocket服务器
     * @param client 客户端
     * @param type 事件类型
     * @param arg 参数
     * @param data 数据
     * @param len 数据长度
     */
    void handleWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                             AwsEventType type, void* arg, uint8_t* data, size_t len);
    
    /**
     * 处理客户端连接
     * @param client 客户端
     */
    void handleClientConnect(AsyncWebSocketClient* client);
    
    /**
     * 处理客户端断开
     * @param client 客户端
     */
    void handleClientDisconnect(AsyncWebSocketClient* client);
    
    /**
     * 处理客户端消息
     * @param client 客户端
     * @param data 消息数据
     * @param len 数据长度
     */
    void handleClientMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len);
    
    // ==================== 消息处理 ====================
    
    /**
     * 处理ping消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handlePingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);
    
    /**
     * 处理获取状态消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleGetStatusMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);
    
    /**
     * 处理执行信号消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleExecuteSignalMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);
    
    /**
     * 处理停止执行消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleStopExecutionMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);
    
    /**
     * 处理学习信号消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleLearnSignalMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);
    
    /**
     * 处理订阅事件消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleSubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);
    
    // ==================== 统计和监控 ====================
    
    /**
     * 获取统计信息
     * @return DynamicJsonDocument 统计信息
     */
    DynamicJsonDocument getStatistics() const;
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    /**
     * 清理过期连接
     */
    void cleanupExpiredConnections();
    
    // ==================== 调试功能 ====================
    
    /**
     * 启用调试模式
     * @param enable 是否启用
     */
    void enableDebugMode(bool enable);
    
    /**
     * 获取调试信息
     * @return String 调试信息
     */
    String getDebugInfo() const;

private:
    // ==================== 私有成员变量 ====================
    
    AsyncWebSocket* m_webSocket;        // WebSocket服务器
    bool m_initialized;                 // 是否已初始化
    bool m_debugMode;                   // 调试模式
    
    // 依赖组件
    DataManager* m_dataManager;
    IRController* m_irController;
    TaskManager* m_taskManager;
    
    // 客户端管理
    struct ClientInfo {
        uint32_t id;
        String ipAddress;
        unsigned long connectTime;
        unsigned long lastPingTime;
        bool isSubscribed;
        String subscriptionType;
    };
    
    std::vector<ClientInfo> m_clients;
    
    // 统计信息
    unsigned long m_totalConnections;   // 总连接数
    unsigned long m_totalMessages;      // 总消息数
    unsigned long m_totalBroadcasts;    // 总广播数
    unsigned long m_startTime;          // 启动时间
    
    // ==================== 私有方法 ====================
    
    /**
     * 创建响应消息
     * @param type 消息类型
     * @param success 是否成功
     * @param message 消息内容
     * @param data 附加数据
     * @return DynamicJsonDocument 响应消息
     */
    DynamicJsonDocument createResponse(const String& type, bool success, 
                                      const String& message, const DynamicJsonDocument& data = DynamicJsonDocument(0));
    
    /**
     * 验证消息格式
     * @param message 消息
     * @return bool 格式是否正确
     */
    bool validateMessageFormat(const DynamicJsonDocument& message);
    
    /**
     * 查找客户端信息
     * @param clientId 客户端ID
     * @return ClientInfo* 客户端信息指针
     */
    ClientInfo* findClientInfo(uint32_t clientId);
    
    /**
     * 添加客户端信息
     * @param client 客户端
     */
    void addClientInfo(AsyncWebSocketClient* client);
    
    /**
     * 移除客户端信息
     * @param clientId 客户端ID
     */
    void removeClientInfo(uint32_t clientId);
    
    /**
     * 记录调试信息
     * @param message 调试信息
     */
    void debugLog(const String& message);
    
    /**
     * 处理未知消息
     * @param client 客户端
     * @param message 消息内容
     */
    void handleUnknownMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message);
};

#endif // WS_MANAGER_H
