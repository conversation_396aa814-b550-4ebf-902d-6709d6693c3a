#include "IRController.h"
#include "DataManager.h"
#include "MemoryAllocator.h"

// ==================== 构造函数和析构函数 ====================
IRController::IRController()
    : m_initialized(false)
    , m_transmitterReady(false)
    , m_receiverReady(false)
    , m_isLearning(false)
    , m_isSending(false)
    , m_debugMode(false)
    , m_transmitPower(80)
    , m_learningStartTime(0)
    , m_learningTimeout(10000)
    , m_dataManager(nullptr)
    , m_irSender(IR_SEND_PIN)
    , m_learnBuffer(nullptr)
    , m_learnBufferSize(0)
    , m_learnDataLength(0)
    , m_totalSentSignals(0)
    , m_totalLearnedSignals(0)
    , m_lastSendTime(0)
    , m_lastLearnTime(0)
{
    Serial.println("📡 IRController created");
}

IRController::~IRController() {
    shutdown();
    cleanupHardware();
    if (m_learnBuffer) {
        MemoryAllocator::smartFree(m_learnBuffer);
        m_learnBuffer = nullptr;
    }
    Serial.println("📡 IRController destroyed");
}

// ==================== 系统生命周期 ====================
bool IRController::initialize() {
    if (m_initialized) {
        Serial.println("⚠️  IRController already initialized");
        return true;
    }
    
    Serial.println("📡 Initializing IRController...");
    
    // 1. 初始化硬件
    if (!initializeHardware()) {
        Serial.println("❌ Hardware initialization failed");
        return false;
    }
    
    // 2. 初始化学习缓冲区
    m_learnBufferSize = IR_RECV_BUFFER_SIZE;
    m_learnBuffer = (uint16_t*)MemoryAllocator::smartAlloc(m_learnBufferSize * sizeof(uint16_t));
    if (!m_learnBuffer) {
        Serial.println("❌ Failed to allocate learn buffer");
        return false;
    }
    
    // 3. 配置PWM
    if (!configurePWM(IR_SEND_FREQUENCY)) {
        Serial.println("❌ PWM configuration failed");
        return false;
    }
    
    m_initialized = true;
    Serial.println("✅ IRController initialization completed");
    return true;
}

void IRController::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("📡 Shutting down IRController...");
    
    // 停止所有操作
    stopSending();
    stopLearning();
    
    m_initialized = false;
    Serial.println("✅ IRController shutdown completed");
}

bool IRController::isHealthy() const {
    return m_initialized && m_transmitterReady && m_receiverReady;
}

void IRController::setDataManager(DataManager* dataManager) {
    m_dataManager = dataManager;
    Serial.println("📡 DataManager dependency set");
}

// ==================== 信号发送 ====================
bool IRController::sendSignal(const String& signalCode, const String& protocol) {
    if (!m_initialized || !m_transmitterReady) {
        Serial.println("❌ Transmitter not ready");
        return false;
    }
    
    if (m_isSending) {
        Serial.println("❌ Already sending signal");
        return false;
    }
    
    if (signalCode.isEmpty()) {
        Serial.println("❌ Empty signal code");
        return false;
    }
    
    Serial.printf("📡 Sending signal: %s (protocol: %s)\n", signalCode.c_str(), protocol.c_str());
    
    m_isSending = true;
    bool success = false;
    
    // 解析信号代码
    DynamicJsonDocument signalData = parseSignalCode(signalCode, protocol);
    if (signalData.isNull()) {
        Serial.println("❌ Failed to parse signal code");
        m_isSending = false;
        return false;
    }
    
    // 根据协议发送信号
    if (protocol == "NEC") {
        success = sendNECSignal(signalCode);
    } else if (protocol == "RC5") {
        success = sendRC5Signal(signalCode);
    } else if (protocol == "RC6") {
        success = sendRC6Signal(signalCode);
    } else if (protocol == "RAW") {
        // 处理原始数据
        if (signalData.containsKey("rawData")) {
            JsonArray rawArray = signalData["rawData"];
            uint16_t* rawData = (uint16_t*)malloc(rawArray.size() * sizeof(uint16_t));
            if (rawData) {
                for (size_t i = 0; i < rawArray.size(); i++) {
                    rawData[i] = rawArray[i];
                }
                success = sendRawSignal(rawData, rawArray.size());
                free(rawData);
            }
        }
    } else {
        Serial.printf("❌ Unsupported protocol: %s\n", protocol.c_str());
    }
    
    if (success) {
        m_totalSentSignals++;
        m_lastSendTime = millis();
        Serial.println("✅ Signal sent successfully");
    } else {
        Serial.println("❌ Signal send failed");
    }
    
    m_isSending = false;
    return success;
}

bool IRController::sendRawSignal(const uint16_t* rawData, size_t dataLength, uint16_t frequency) {
    if (!m_initialized || !m_transmitterReady) {
        return false;
    }
    
    if (!rawData || dataLength == 0) {
        return false;
    }
    
    Serial.printf("📡 Sending raw signal: %d pulses at %d Hz\n", dataLength, frequency);
    
    // 配置载波频率
    if (!configurePWM(frequency)) {
        return false;
    }
    
    // 发送原始数据
    for (size_t i = 0; i < dataLength; i++) {
        bool level = (i % 2 == 0); // 奇偶交替
        sendPulse(rawData[i], level);
    }
    
    // 确保结束时为低电平
    digitalWrite(IR_SEND_PIN, LOW);

    return true;
}

bool IRController::sendNECSignal(const String& signalCode) {
    if (!m_initialized || !m_transmitterReady) {
        return false;
    }

    // 解析NEC信号代码（十六进制格式）
    uint32_t code = strtoul(signalCode.c_str(), nullptr, 16);

    // 使用IRremoteESP8266库发送NEC信号
    m_irSender.sendNEC(code, 32);

    Serial.printf("📡 Sent NEC signal: 0x%08X\n", code);
    return true;
}

bool IRController::sendRC5Signal(const String& signalCode) {
    if (!m_initialized || !m_transmitterReady) {
        return false;
    }

    // 解析RC5信号代码（十六进制格式）
    uint16_t code = strtoul(signalCode.c_str(), nullptr, 16);

    // 使用IRremoteESP8266库发送RC5信号
    m_irSender.sendRC5(code, 13);

    Serial.printf("📡 Sent RC5 signal: 0x%04X\n", code);
    return true;
}

bool IRController::sendRC6Signal(const String& signalCode) {
    if (!m_initialized || !m_transmitterReady) {
        return false;
    }

    // 解析RC6信号代码（十六进制格式）
    uint32_t code = strtoul(signalCode.c_str(), nullptr, 16);

    // 使用IRremoteESP8266库发送RC6信号
    m_irSender.sendRC6(code, 20);

    Serial.printf("📡 Sent RC6 signal: 0x%08X\n", code);
    return true;
}

int IRController::batchSendSignals(const DynamicJsonDocument& signals, int interval) {
    if (!signals.is<JsonArray>()) {
        return 0;
    }

    JsonArrayConst signalArray = signals.as<JsonArrayConst>();
    int successCount = 0;
    
    Serial.printf("📡 Batch sending %d signals with %d ms interval\n", signalArray.size(), interval);
    
    for (JsonVariantConst signal : signalArray) {
        if (signal.containsKey("signalCode") && signal.containsKey("protocol")) {
            String signalCode = signal["signalCode"];
            String protocol = signal["protocol"];
            
            if (sendSignal(signalCode, protocol)) {
                successCount++;
            }
            
            if (interval > 0) {
                delay(interval);
            }
        }
    }
    
    Serial.printf("📡 Batch send completed: %d/%d successful\n", successCount, signalArray.size());
    return successCount;
}

void IRController::stopSending() {
    if (m_isSending) {
        m_isSending = false;
        digitalWrite(IR_SEND_PIN, LOW);
        Serial.println("📡 Sending stopped");
    }
}

// ==================== 信号学习 ====================
bool IRController::startLearning(unsigned long timeout) {
    if (!m_initialized || !m_receiverReady) {
        Serial.println("❌ Receiver not ready");
        return false;
    }
    
    if (m_isLearning) {
        Serial.println("❌ Already in learning mode");
        return false;
    }
    
    Serial.printf("📡 Starting learning mode (timeout: %lu ms)\n", timeout);
    
    m_isLearning = true;
    m_learningStartTime = millis();
    m_learningTimeout = timeout;
    m_learnDataLength = 0;
    
    // 清除学习缓冲区
    clearLearnBuffer();
    
    Serial.println("✅ Learning mode started");
    return true;
}

bool IRController::stopLearning() {
    if (m_isLearning) {
        m_isLearning = false;
        Serial.println("📡 Learning mode stopped");
        return true;
    }
    return false;
}

bool IRController::isLearning() const {
    if (!m_isLearning) {
        return false;
    }
    
    // 检查超时
    unsigned long elapsed = millis() - m_learningStartTime;
    return elapsed < m_learningTimeout;
}

DynamicJsonDocument IRController::getLearnedSignal() {
    DynamicJsonDocument result(1024);
    
    if (m_learnDataLength == 0) {
        result["success"] = false;
        result["error"] = "No signal learned";
        return result;
    }
    
    // 分析学习到的数据
    DynamicJsonDocument analysis = analyzeLearnedData();
    
    result["success"] = true;
    result["timestamp"] = millis();
    result["dataLength"] = m_learnDataLength;
    result["analysis"] = analysis;
    
    // 添加原始数据
    JsonArray rawArray = result.createNestedArray("rawData");
    for (size_t i = 0; i < m_learnDataLength; i++) {
        rawArray.add(m_learnBuffer[i]);
    }
    
    m_totalLearnedSignals++;
    m_lastLearnTime = millis();
    
    return result;
}

void IRController::clearLearnBuffer() {
    if (m_learnBuffer) {
        memset(m_learnBuffer, 0, m_learnBufferSize * sizeof(uint16_t));
        m_learnDataLength = 0;
    }
}

// ==================== 硬件状态 ====================
DynamicJsonDocument IRController::getHardwareStatus() const {
    DynamicJsonDocument status(512);

    status["initialized"] = m_initialized;
    status["transmitter_ready"] = m_transmitterReady;
    status["receiver_ready"] = m_receiverReady;
    status["transmit_power"] = m_transmitPower;
    status["is_learning"] = m_isLearning;
    status["is_sending"] = m_isSending;
    status["debug_mode"] = m_debugMode;
    status["total_sent"] = m_totalSentSignals;
    status["total_learned"] = m_totalLearnedSignals;
    status["last_send_time"] = m_lastSendTime;
    status["last_learn_time"] = m_lastLearnTime;

    return status;
}

bool IRController::isTransmitterReady() const {
    return m_transmitterReady;
}

bool IRController::isReceiverReady() const {
    return m_receiverReady;
}

int IRController::getSignalStrength() const {
    // 简化实现：返回模拟信号强度
    return m_receiverReady ? 75 : 0;
}

bool IRController::setTransmitPower(int power) {
    if (power < 0 || power > 100) {
        return false;
    }

    m_transmitPower = power;

    // 更新PWM占空比
    int dutyCycle = map(power, 0, 100, 0, 255);
    // 这里应该设置实际的PWM占空比

    Serial.printf("📡 Transmit power set to %d%%\n", power);
    return true;
}

int IRController::getTransmitPower() const {
    return m_transmitPower;
}

// ==================== 信号格式转换 ====================
DynamicJsonDocument IRController::parseSignalCode(const String& signalCode, const String& protocol) {
    DynamicJsonDocument result(512);

    result["protocol"] = protocol;
    result["original_code"] = signalCode;

    if (protocol == "NEC") {
        if (signalCode.startsWith("0x") && signalCode.length() == 10) {
            unsigned long code = strtoul(signalCode.c_str() + 2, nullptr, 16);
            result["address"] = (code >> 16) & 0xFFFF;
            result["command"] = code & 0xFFFF;
            result["valid"] = true;
        } else {
            result["valid"] = false;
            result["error"] = "Invalid NEC format";
        }
    } else if (protocol == "RC5") {
        int colonPos = signalCode.indexOf(':');
        if (colonPos > 0) {
            result["address"] = signalCode.substring(0, colonPos).toInt();
            result["command"] = signalCode.substring(colonPos + 1).toInt();
            result["valid"] = true;
        } else {
            result["valid"] = false;
            result["error"] = "Invalid RC5 format";
        }
    } else if (protocol == "RAW") {
        result["raw_data"] = signalCode;
        result["valid"] = true;
    } else {
        result["valid"] = false;
        result["error"] = "Unsupported protocol";
    }

    return result;
}

String IRController::formatSignalCode(const DynamicJsonDocument& signalData) {
    if (!signalData.containsKey("protocol")) {
        return "";
    }

    String protocol = signalData["protocol"];

    if (protocol == "NEC") {
        if (signalData.containsKey("address") && signalData.containsKey("command")) {
            unsigned long address = signalData["address"];
            unsigned long command = signalData["command"];
            unsigned long code = (address << 16) | command;
            return "0x" + String(code, HEX);
        }
    } else if (protocol == "RC5") {
        if (signalData.containsKey("address") && signalData.containsKey("command")) {
            return String(signalData["address"].as<int>()) + ":" + String(signalData["command"].as<int>());
        }
    } else if (protocol == "RAW") {
        if (signalData.containsKey("raw_data")) {
            return signalData["raw_data"];
        }
    }

    return "";
}

bool IRController::validateSignalCode(const String& signalCode, const String& protocol) {
    if (signalCode.isEmpty() || protocol.isEmpty()) {
        return false;
    }

    if (protocol == "NEC") {
        return signalCode.startsWith("0x") && signalCode.length() == 10;
    } else if (protocol == "RC5" || protocol == "RC6") {
        return signalCode.indexOf(':') > 0;
    } else if (protocol == "RAW") {
        return signalCode.length() > 0;
    }

    return false;
}

String IRController::convertSignalFormat(const String& signalCode, const String& fromProtocol, const String& toProtocol) {
    if (fromProtocol == toProtocol) {
        return signalCode;
    }

    // 简化转换：主要处理格式标准化
    if (toProtocol == "RAW") {
        return "RAW:" + signalCode;
    }

    return signalCode;
}

// ==================== 统计信息 ====================
DynamicJsonDocument IRController::getSendStatistics() const {
    DynamicJsonDocument stats(256);

    stats["total_sent"] = m_totalSentSignals;
    stats["last_send_time"] = m_lastSendTime;
    stats["transmit_power"] = m_transmitPower;
    stats["is_sending"] = m_isSending;

    return stats;
}

DynamicJsonDocument IRController::getLearnStatistics() const {
    DynamicJsonDocument stats(256);

    stats["total_learned"] = m_totalLearnedSignals;
    stats["last_learn_time"] = m_lastLearnTime;
    stats["is_learning"] = m_isLearning;
    stats["learn_buffer_size"] = m_learnBufferSize;
    stats["current_data_length"] = m_learnDataLength;

    return stats;
}

void IRController::resetStatistics() {
    m_totalSentSignals = 0;
    m_totalLearnedSignals = 0;
    m_lastSendTime = 0;
    m_lastLearnTime = 0;

    Serial.println("📡 Statistics reset");
}

// ==================== 调试功能 ====================
void IRController::enableDebugMode(bool enable) {
    m_debugMode = enable;
    Serial.printf("📡 Debug mode %s\n", enable ? "enabled" : "disabled");
}

String IRController::getDebugInfo() const {
    String info = "IRController Debug Info:\n";
    info += "  Initialized: " + String(m_initialized ? "Yes" : "No") + "\n";
    info += "  Transmitter Ready: " + String(m_transmitterReady ? "Yes" : "No") + "\n";
    info += "  Receiver Ready: " + String(m_receiverReady ? "Yes" : "No") + "\n";
    info += "  Learning: " + String(m_isLearning ? "Yes" : "No") + "\n";
    info += "  Sending: " + String(m_isSending ? "Yes" : "No") + "\n";
    info += "  Transmit Power: " + String(m_transmitPower) + "%\n";
    info += "  Total Sent: " + String(m_totalSentSignals) + "\n";
    info += "  Total Learned: " + String(m_totalLearnedSignals) + "\n";

    return info;
}

DynamicJsonDocument IRController::performSelfTest() {
    DynamicJsonDocument result(512);

    result["timestamp"] = millis();
    result["test_name"] = "IRController Self Test";

    bool allPassed = true;
    JsonArray tests = result.createNestedArray("tests");

    // 测试1：硬件初始化
    JsonObject test1 = tests.createNestedObject();
    test1["name"] = "Hardware Initialization";
    test1["passed"] = m_initialized;
    if (!m_initialized) allPassed = false;

    // 测试2：发射器状态
    JsonObject test2 = tests.createNestedObject();
    test2["name"] = "Transmitter Ready";
    test2["passed"] = m_transmitterReady;
    if (!m_transmitterReady) allPassed = false;

    // 测试3：接收器状态
    JsonObject test3 = tests.createNestedObject();
    test3["name"] = "Receiver Ready";
    test3["passed"] = m_receiverReady;
    if (!m_receiverReady) allPassed = false;

    // 测试4：内存分配
    JsonObject test4 = tests.createNestedObject();
    test4["name"] = "Memory Allocation";
    bool memoryOK = (m_learnBuffer != nullptr);
    test4["passed"] = memoryOK;
    if (!memoryOK) allPassed = false;

    result["overall_passed"] = allPassed;
    result["total_tests"] = tests.size();

    return result;
}

// ==================== 私有方法实现 ====================
bool IRController::initializeHardware() {
    Serial.println("📡 Initializing IR hardware...");

    // 初始化引脚
    INIT_IR_PINS();

    // 检查引脚配置
    if (IR_SEND_PIN < 0 || IR_RECV_PIN < 0) {
        Serial.println("❌ Invalid pin configuration");
        return false;
    }

    // 初始化PWM
    if (!configurePWM(IR_SEND_FREQUENCY)) {
        Serial.println("❌ PWM configuration failed");
        return false;
    }

    m_transmitterReady = true;
    m_receiverReady = true;

    Serial.println("✅ IR hardware initialized");
    return true;
}

void IRController::cleanupHardware() {
    // 停止PWM
    digitalWrite(IR_SEND_PIN, LOW);

    // 重置状态
    m_transmitterReady = false;
    m_receiverReady = false;

    Serial.println("📡 IR hardware cleaned up");
}

bool IRController::configurePWM(uint16_t frequency) {
    // 配置PWM用于载波生成
    ledcSetup(IR_SEND_CHANNEL, frequency, IR_SEND_RESOLUTION);
    ledcAttachPin(IR_SEND_PIN, IR_SEND_CHANNEL);

    // 初始状态为关闭
    ledcWrite(IR_SEND_CHANNEL, 0);

    Serial.printf("📡 PWM configured: %d Hz\n", frequency);
    return true;
}

void IRController::sendPulse(uint16_t duration, bool level) {
    if (level) {
        // 发送载波
        int dutyCycle = map(m_transmitPower, 0, 100, 0, (1 << IR_SEND_RESOLUTION) - 1);
        ledcWrite(IR_SEND_CHANNEL, dutyCycle);
    } else {
        // 停止载波
        ledcWrite(IR_SEND_CHANNEL, 0);
    }

    delayMicroseconds(duration);
}

bool IRController::detectSignal() {
    // 简化实现：检查接收引脚状态
    return digitalRead(IR_RECV_PIN) == LOW;
}

size_t IRController::readRawData() {
    if (!m_learnBuffer || !m_isLearning) {
        return 0;
    }

    // 简化实现：模拟读取原始数据
    // 实际实现需要使用中断和定时器来精确测量脉冲宽度

    size_t dataLength = 0;
    unsigned long startTime = millis();

    while (m_isLearning && dataLength < m_learnBufferSize &&
           (millis() - startTime) < 1000) { // 1秒超时

        if (detectSignal()) {
            // 模拟脉冲数据
            m_learnBuffer[dataLength++] = 500 + random(100);
            delay(1);
        }

        yield();
    }

    m_learnDataLength = dataLength;
    return dataLength;
}

DynamicJsonDocument IRController::analyzeLearnedData() {
    DynamicJsonDocument analysis(512);

    if (m_learnDataLength == 0) {
        analysis["valid"] = false;
        analysis["error"] = "No data to analyze";
        return analysis;
    }

    analysis["valid"] = true;
    analysis["data_length"] = m_learnDataLength;
    analysis["estimated_protocol"] = "RAW"; // 简化实现
    analysis["frequency"] = IR_SEND_FREQUENCY;

    // 计算基本统计
    uint16_t minPulse = 65535, maxPulse = 0;
    unsigned long totalDuration = 0;

    for (size_t i = 0; i < m_learnDataLength; i++) {
        uint16_t pulse = m_learnBuffer[i];
        if (pulse < minPulse) minPulse = pulse;
        if (pulse > maxPulse) maxPulse = pulse;
        totalDuration += pulse;
    }

    analysis["min_pulse"] = minPulse;
    analysis["max_pulse"] = maxPulse;
    analysis["total_duration"] = totalDuration;
    analysis["average_pulse"] = totalDuration / m_learnDataLength;

    return analysis;
}

void IRController::debugLog(const String& message) {
    if (m_debugMode) {
        Serial.printf("📡 DEBUG: %s\n", message.c_str());
    }
}
