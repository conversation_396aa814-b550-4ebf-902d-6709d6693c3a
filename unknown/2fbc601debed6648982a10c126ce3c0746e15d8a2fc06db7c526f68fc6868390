#!/usr/bin/env python3
"""
ESP32-S3 红外控制系统 - 后构建脚本
负责构建后的处理：固件信息生成、文件打包、部署准备
"""

import os
import shutil
import json
import hashlib
from datetime import datetime

Import("env")

def generate_firmware_info():
    """生成固件信息"""
    print("📝 生成固件信息...")
    
    # 获取固件文件路径
    firmware_path = env.get("PROG_PATH")
    if not firmware_path or not os.path.exists(firmware_path):
        print("⚠️ 固件文件不存在，跳过信息生成")
        return
    
    # 计算固件哈希
    with open(firmware_path, 'rb') as f:
        firmware_hash = hashlib.sha256(f.read()).hexdigest()
    
    # 固件信息
    firmware_info = {
        "name": "ESP32-S3 红外控制系统",
        "version": "1.0.0",
        "build_date": datetime.now().isoformat(),
        "platform": "ESP32-S3",
        "framework": "Arduino",
        "file_size": os.path.getsize(firmware_path),
        "sha256": firmware_hash,
        "features": {
            "ir_learning": True,
            "ir_transmit": True,
            "web_interface": True,
            "websocket": True,
            "spiffs": True,
            "psram": True,
            "ota_update": True
        },
        "hardware": {
            "ir_send_pin": 4,
            "ir_recv_pin": 15,
            "status_led_pin": 2,
            "flash_size": "16MB",
            "psram_size": "8MB"
        },
        "api_endpoints": [
            "GET /api/signals",
            "POST /api/signals/delete",
            "POST /api/signals/batch-delete", 
            "POST /api/signals/update",
            "POST /api/signals/send",
            "POST /api/signals/learn/start",
            "POST /api/signals/learn/stop",
            "POST /api/signals/learn/save",
            "POST /api/signals/import",
            "POST /api/signals/import/text",
            "POST /api/signals/import/execute",
            "POST /api/signals/import/text/execute",
            "GET /api/system/logs",
            "POST /api/system/logs",
            "POST /api/system/error-log",
            "GET /api/system/stats",
            "GET /api/control/history",
            "POST /api/control/history"
        ],
        "websocket_events": [
            "signal.learned",
            "learning.error", 
            "signal.sent",
            "signal.failed",
            "system.status",
            "esp32.connected"
        ]
    }
    
    # 写入固件信息文件
    info_file = firmware_path.replace('.bin', '_info.json')
    with open(info_file, 'w', encoding='utf-8') as f:
        json.dump(firmware_info, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 固件信息已生成: {info_file}")
    print(f"   文件大小: {firmware_info['file_size']} bytes")
    print(f"   SHA256: {firmware_hash[:16]}...")

def create_release_package():
    """创建发布包"""
    print("📦 创建发布包...")
    
    project_dir = env.get("PROJECT_DIR")
    build_dir = env.get("BUILD_DIR")
    firmware_path = env.get("PROG_PATH")
    
    if not firmware_path or not os.path.exists(firmware_path):
        print("⚠️ 固件文件不存在，跳过发布包创建")
        return
    
    # 创建发布目录
    release_dir = os.path.join(project_dir, "release")
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制固件文件
    firmware_name = "esp32_s3_ir_system.bin"
    shutil.copy2(firmware_path, os.path.join(release_dir, firmware_name))
    
    # 复制固件信息
    info_file = firmware_path.replace('.bin', '_info.json')
    if os.path.exists(info_file):
        shutil.copy2(info_file, os.path.join(release_dir, "firmware_info.json"))
    
    # 复制SPIFFS镜像（如果存在）
    spiffs_path = os.path.join(build_dir, "spiffs.bin")
    if os.path.exists(spiffs_path):
        shutil.copy2(spiffs_path, os.path.join(release_dir, "spiffs.bin"))
    
    # 创建README文件
    readme_content = """# ESP32-S3 红外控制系统 - 发布包

## 文件说明

- `esp32_s3_ir_system.bin` - 主固件文件
- `spiffs.bin` - 文件系统镜像（包含Web界面）
- `firmware_info.json` - 固件详细信息
- `flash_instructions.txt` - 烧录说明

## 烧录说明

### 使用 esptool.py 烧录

```bash
# 安装 esptool
pip install esptool

# 烧录固件
esptool.py --chip esp32s3 --port COM3 --baud 921600 write_flash 0x0 esp32_s3_ir_system.bin

# 烧录文件系统（可选）
esptool.py --chip esp32s3 --port COM3 --baud 921600 write_flash 0x290000 spiffs.bin
```

### 使用 PlatformIO 烧录

```bash
# 烧录固件
pio run --target upload

# 烧录文件系统
pio run --target uploadfs
```

## 硬件连接

- 红外发射器: GPIO 4
- 红外接收器: GPIO 15  
- 状态LED: GPIO 2

## 首次使用

1. 烧录固件后，ESP32-S3会创建WiFi热点 "ESP32-IR-System"
2. 连接热点，密码: 12345678
3. 浏览器访问: http://***********
4. 在设置中配置WiFi连接

## 功能特性

- 红外信号学习和发射
- Web界面控制
- 实时WebSocket通信
- 信号数据持久化存储
- 批量操作支持
- 文件导入导出
- 系统监控和日志

## 技术支持

如有问题请查看项目文档或提交Issue。
"""
    
    with open(os.path.join(release_dir, "README.md"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 创建烧录说明
    flash_instructions = """ESP32-S3 红外控制系统 - 烧录说明

=== 准备工作 ===
1. 安装 Python 3.7+
2. 安装 esptool: pip install esptool
3. 连接ESP32-S3开发板到电脑
4. 确认串口号（Windows: COM3, Linux/Mac: /dev/ttyUSB0）

=== 烧录步骤 ===
1. 进入下载模式：
   - 按住BOOT按钮
   - 按一下RESET按钮
   - 松开BOOT按钮

2. 烧录固件：
   esptool.py --chip esp32s3 --port COM3 --baud 921600 write_flash 0x0 esp32_s3_ir_system.bin

3. 烧录文件系统（可选）：
   esptool.py --chip esp32s3 --port COM3 --baud 921600 write_flash 0x290000 spiffs.bin

4. 重启设备：
   - 按一下RESET按钮

=== 验证烧录 ===
1. 打开串口监视器（波特率115200）
2. 应该看到系统启动信息
3. 查找WiFi热点 "ESP32-IR-System"
4. 连接热点并访问 http://***********

=== 故障排除 ===
- 烧录失败：检查串口号、波特率、连接线
- 无法连接：确认进入下载模式
- 启动异常：检查固件完整性、重新烧录
"""
    
    with open(os.path.join(release_dir, "flash_instructions.txt"), 'w', encoding='utf-8') as f:
        f.write(flash_instructions)
    
    print(f"✅ 发布包已创建: {release_dir}")

def generate_build_report():
    """生成构建报告"""
    print("📊 生成构建报告...")
    
    firmware_path = env.get("PROG_PATH")
    build_dir = env.get("BUILD_DIR")
    
    # 收集构建信息
    build_report = {
        "build_time": datetime.now().isoformat(),
        "platform": env.get("PIOPLATFORM"),
        "board": env.get("BOARD"),
        "framework": env.get("PIOFRAMEWORK"),
        "build_type": env.get("BUILD_TYPE", "release"),
        "files": {}
    }
    
    # 固件文件信息
    if firmware_path and os.path.exists(firmware_path):
        build_report["files"]["firmware"] = {
            "path": firmware_path,
            "size": os.path.getsize(firmware_path),
            "size_kb": round(os.path.getsize(firmware_path) / 1024, 2)
        }
    
    # SPIFFS文件信息
    spiffs_path = os.path.join(build_dir, "spiffs.bin")
    if os.path.exists(spiffs_path):
        build_report["files"]["spiffs"] = {
            "path": spiffs_path,
            "size": os.path.getsize(spiffs_path),
            "size_kb": round(os.path.getsize(spiffs_path) / 1024, 2)
        }
    
    # 写入构建报告
    report_file = os.path.join(build_dir, "build_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(build_report, f, indent=2)
    
    print(f"✅ 构建报告已生成: {report_file}")
    
    # 打印摘要
    if "firmware" in build_report["files"]:
        fw_size = build_report["files"]["firmware"]["size_kb"]
        print(f"   固件大小: {fw_size} KB")
    
    if "spiffs" in build_report["files"]:
        spiffs_size = build_report["files"]["spiffs"]["size_kb"]
        print(f"   SPIFFS大小: {spiffs_size} KB")

def cleanup_build_files():
    """清理构建文件"""
    print("🧹 清理临时文件...")
    
    build_dir = env.get("BUILD_DIR")
    temp_extensions = ['.tmp', '.temp', '.bak']
    
    cleaned_count = 0
    for root, dirs, files in os.walk(build_dir):
        for file in files:
            if any(file.endswith(ext) for ext in temp_extensions):
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    cleaned_count += 1
                except:
                    pass
    
    if cleaned_count > 0:
        print(f"✅ 清理了 {cleaned_count} 个临时文件")
    else:
        print("✅ 无需清理")

def main():
    """主函数"""
    print("\n🏁 ESP32-S3 红外控制系统 - 后构建脚本")
    print("=" * 50)
    
    try:
        # 执行后构建任务
        generate_firmware_info()
        create_release_package()
        generate_build_report()
        cleanup_build_files()
        
        print("\n✅ 后构建完成！")
        print("📦 发布包位置: release/")
        
    except Exception as e:
        print(f"\n❌ 后构建失败: {e}")
        # 不退出，允许构建继续

if __name__ == "__main__":
    main()
