/**
 * 文件系统管理器 - 头文件
 * 负责文件系统操作、数据持久化、备份恢复
 */

#ifndef FILE_SYSTEM_MANAGER_H
#define FILE_SYSTEM_MANAGER_H

#include <Arduino.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>

// 文件类型枚举
enum FileType {
    FILE_SIGNALS,       // 信号数据文件
    FILE_CONFIG,        // 配置文件
    FILE_LOGS,          // 日志文件
    FILE_HISTORY,       // 历史记录文件
    FILE_BACKUP,        // 备份文件
    FILE_TEMP           // 临时文件
};

// 文件信息结构
struct FileInfo {
    String path;            // 文件路径
    String name;            // 文件名
    size_t size;            // 文件大小
    time_t lastModified;    // 最后修改时间
    FileType type;          // 文件类型
    bool exists;            // 文件是否存在
    
    FileInfo() : size(0), lastModified(0), type(FILE_TEMP), exists(false) {}
    
    // 转换为JSON
    JsonObject toJson(JsonDocument& doc) const;
};

class FileSystemManager {
private:
    // 文件路径常量
    static const char* SIGNALS_FILE;
    static const char* CONFIG_FILE;
    static const char* LOGS_FILE;
    static const char* HISTORY_FILE;
    static const char* BACKUP_DIR;
    static const char* TEMP_DIR;
    
    // 配置参数
    static const size_t MAX_FILE_SIZE = 64 * 1024;     // 64KB
    static const size_t MAX_BACKUP_COUNT = 5;          // 最大备份数量
    static const size_t MAX_LOG_SIZE = 32 * 1024;      // 32KB
    static const unsigned long BACKUP_INTERVAL = 86400000; // 24小时备份间隔
    
    // 状态管理
    bool initialized;
    unsigned long lastBackupTime;
    size_t totalFilesCreated;
    size_t totalFilesDeleted;
    
    // 内部方法
    bool createDirectoryIfNotExists(const String& path);
    bool isValidPath(const String& path) const;
    bool isValidSize(size_t size) const;
    String generateBackupPath(const String& originalPath) const;
    String generateTempPath(const String& baseName) const;
    bool rotateBackups(const String& filePath);
    void updateFileStats();
    
public:
    FileSystemManager();
    ~FileSystemManager();
    
    // 初始化和配置
    bool init();
    bool format();
    bool isInitialized() const { return initialized; }
    
    // 基础文件操作
    bool fileExists(const String& path) const;
    bool createFile(const String& path, const String& content = "");
    bool deleteFile(const String& path);
    bool copyFile(const String& srcPath, const String& destPath);
    bool moveFile(const String& srcPath, const String& destPath);
    
    // 文件读写
    String readFile(const String& path) const;
    bool writeFile(const String& path, const String& content);
    bool appendFile(const String& path, const String& content);
    
    // JSON文件操作
    bool readJsonFile(const String& path, JsonDocument& doc) const;
    bool writeJsonFile(const String& path, const JsonDocument& doc);
    bool updateJsonFile(const String& path, const JsonObject& updates);
    
    // 目录操作
    bool createDirectory(const String& path);
    bool deleteDirectory(const String& path);
    std::vector<String> listFiles(const String& path = "/") const;
    std::vector<FileInfo> getFileInfoList(const String& path = "/") const;
    
    // 文件信息
    FileInfo getFileInfo(const String& path) const;
    size_t getFileSize(const String& path) const;
    time_t getLastModified(const String& path) const;
    
    // 备份和恢复
    bool createBackup(const String& filePath);
    bool restoreBackup(const String& filePath, int backupIndex = 0);
    std::vector<String> listBackups(const String& filePath) const;
    bool deleteOldBackups(const String& filePath);
    
    // 自动备份
    void enableAutoBackup(bool enable = true) { autoBackupEnabled = enable; }
    void handleAutoBackup();
    bool isAutoBackupDue() const;
    
    // 系统文件管理
    bool saveSignals(const JsonDocument& doc);
    bool loadSignals(JsonDocument& doc) const;
    bool saveConfig(const JsonDocument& doc);
    bool loadConfig(JsonDocument& doc) const;
    bool saveLogs(const JsonDocument& doc);
    bool loadLogs(JsonDocument& doc) const;
    bool saveHistory(const JsonDocument& doc);
    bool loadHistory(JsonDocument& doc) const;
    
    // 文件系统状态
    size_t getTotalBytes() const;
    size_t getUsedBytes() const;
    size_t getFreeBytes() const;
    float getUsagePercentage() const;
    JsonObject getFileSystemInfo() const;
    
    // 清理和维护
    bool cleanupTempFiles();
    bool cleanupOldLogs();
    bool optimizeStorage();
    void performMaintenance();
    
    // 导入导出
    bool exportData(const String& exportPath) const;
    bool importData(const String& importPath);
    String exportToString() const;
    bool importFromString(const String& data);
    
    // 统计信息
    size_t getFilesCreated() const { return totalFilesCreated; }
    size_t getFilesDeleted() const { return totalFilesDeleted; }
    JsonObject getStatistics() const;
    
    // 调试和诊断
    void enableDebug(bool enable = true) { debugEnabled = enable; }
    void printFileSystemInfo() const;
    void printFileList() const;
    bool checkFileSystemIntegrity() const;
    
private:
    bool autoBackupEnabled;
    bool debugEnabled;
};

#endif // FILE_SYSTEM_MANAGER_H
