#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include "system-config.h"

// Forward declarations
class DataManager;
class IRController;
class TaskManager;
class WSManager;
class NetworkSecurity;

/**
 * Web服务器管理器类
 * 
 * 负责HTTP服务器的管理和API路由
 * 
 * 核心功能：
 * - HTTP服务器管理
 * - API路由处理
 * - 静态文件服务
 * - 请求处理
 */
class WebServerManager {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     */
    WebServerManager();
    
    /**
     * 析构函数
     */
    ~WebServerManager();
    
    // ==================== 系统生命周期 ====================
    
    /**
     * 初始化Web服务器
     * @return bool 初始化是否成功
     */
    bool initialize();
    
    /**
     * 关闭Web服务器
     */
    void shutdown();
    
    /**
     * 检查健康状态
     * @return bool 是否健康
     */
    bool isHealthy() const;
    
    /**
     * 设置依赖组件
     * @param dataManager 数据管理器
     * @param irController 红外控制器
     * @param taskManager 任务管理器
     * @param wsManager WebSocket管理器
     * @param networkSecurity 网络安全管理器
     */
    void setDependencies(DataManager* dataManager, IRController* irController, 
                        TaskManager* taskManager, WSManager* wsManager, 
                        NetworkSecurity* networkSecurity);
    
    // ==================== 服务器控制 ====================
    
    /**
     * 启动服务器
     * @param port 端口号
     * @return bool 启动是否成功
     */
    bool start(uint16_t port = 80);
    
    /**
     * 停止服务器
     */
    void stop();
    
    /**
     * 重启服务器
     * @return bool 重启是否成功
     */
    bool restart();
    
    /**
     * 检查服务器是否运行
     * @return bool 是否运行
     */
    bool isRunning() const;
    
    /**
     * 获取服务器端口
     * @return uint16_t 端口号
     */
    uint16_t getPort() const;
    
    // ==================== 路由管理 ====================
    
    /**
     * 设置路由
     */
    void setupRoutes();
    
    /**
     * 设置静态文件路由
     */
    void setupStaticRoutes();
    
    /**
     * 设置API路由
     */
    void setupAPIRoutes();
    
    /**
     * 设置WebSocket路由
     */
    void setupWebSocketRoutes();
    
    // ==================== 请求处理 ====================
    
    /**
     * 处理根路径请求
     * @param request 请求对象
     */
    void handleRoot(AsyncWebServerRequest* request);
    
    /**
     * 处理404错误
     * @param request 请求对象
     */
    void handleNotFound(AsyncWebServerRequest* request);
    
    /**
     * 处理CORS预检请求
     * @param request 请求对象
     */
    void handleCORS(AsyncWebServerRequest* request);
    
    // ==================== API处理器 ====================
    
    /**
     * 处理系统状态API
     * @param request 请求对象
     */
    void handleSystemStatus(AsyncWebServerRequest* request);
    
    /**
     * 处理信号相关API
     * @param request 请求对象
     */
    void handleSignalsAPI(AsyncWebServerRequest* request);
    
    /**
     * 处理任务相关API
     * @param request 请求对象
     */
    void handleTasksAPI(AsyncWebServerRequest* request);
    
    /**
     * 处理定时器相关API
     * @param request 请求对象
     */
    void handleTimersAPI(AsyncWebServerRequest* request);
    
    /**
     * 处理红外控制API
     * @param request 请求对象
     */
    void handleIRControlAPI(AsyncWebServerRequest* request);
    
    /**
     * 处理系统配置API
     * @param request 请求对象
     */
    void handleConfigAPI(AsyncWebServerRequest* request);

    // ==================== 扩展API处理器 ====================

    /**
     * 处理系统错误日志API
     * @param request 请求对象
     */
    void handleSystemErrorLog(AsyncWebServerRequest* request);

    /**
     * 处理批量请求API
     * @param request 请求对象
     */
    void handleBatchRequest(AsyncWebServerRequest* request);

    /**
     * 处理信号删除API
     * @param request 请求对象
     */
    void handleSignalDelete(AsyncWebServerRequest* request);

    /**
     * 处理信号批量删除API
     * @param request 请求对象
     */
    void handleSignalBatchDelete(AsyncWebServerRequest* request);

    /**
     * 处理信号更新API
     * @param request 请求对象
     */
    void handleSignalUpdate(AsyncWebServerRequest* request);

    /**
     * 处理信号发送API
     * @param request 请求对象
     */
    void handleSignalSend(AsyncWebServerRequest* request);

    /**
     * 处理信号学习开始API
     * @param request 请求对象
     */
    void handleSignalLearnStart(AsyncWebServerRequest* request);

    /**
     * 处理信号学习停止API
     * @param request 请求对象
     */
    void handleSignalLearnStop(AsyncWebServerRequest* request);

    /**
     * 处理信号学习保存API
     * @param request 请求对象
     */
    void handleSignalLearnSave(AsyncWebServerRequest* request);

    /**
     * 处理信号导入API
     * @param request 请求对象
     */
    void handleSignalImport(AsyncWebServerRequest* request);

    /**
     * 处理信号文本导入API
     * @param request 请求对象
     */
    void handleSignalImportText(AsyncWebServerRequest* request);

    /**
     * 处理信号导入执行API
     * @param request 请求对象
     */
    void handleSignalImportExecute(AsyncWebServerRequest* request);

    /**
     * 处理信号文本导入执行API
     * @param request 请求对象
     */
    void handleSignalImportTextExecute(AsyncWebServerRequest* request);

    /**
     * 处理控制历史获取API
     * @param request 请求对象
     */
    void handleControlHistoryGet(AsyncWebServerRequest* request);

    /**
     * 处理控制历史保存API
     * @param request 请求对象
     */
    void handleControlHistoryPost(AsyncWebServerRequest* request);

    // ==================== 文件上传处理 ====================
    
    /**
     * 处理文件上传
     * @param request 请求对象
     * @param filename 文件名
     * @param index 数据索引
     * @param data 数据
     * @param len 数据长度
     * @param final 是否为最后一块数据
     */
    void handleFileUpload(AsyncWebServerRequest* request, const String& filename, 
                         size_t index, uint8_t* data, size_t len, bool final);
    
    /**
     * 处理固件更新
     * @param request 请求对象
     * @param filename 文件名
     * @param index 数据索引
     * @param data 数据
     * @param len 数据长度
     * @param final 是否为最后一块数据
     */
    void handleFirmwareUpdate(AsyncWebServerRequest* request, const String& filename,
                             size_t index, uint8_t* data, size_t len, bool final);
    
    // ==================== 响应辅助方法 ====================
    
    /**
     * 发送JSON响应
     * @param request 请求对象
     * @param json JSON文档
     * @param code HTTP状态码
     */
    void sendJSONResponse(AsyncWebServerRequest* request, const DynamicJsonDocument& json, int code = 200);
    
    /**
     * 发送错误响应
     * @param request 请求对象
     * @param message 错误消息
     * @param code HTTP状态码
     */
    void sendErrorResponse(AsyncWebServerRequest* request, const String& message, int code = 400);
    
    /**
     * 发送成功响应
     * @param request 请求对象
     * @param message 成功消息
     * @param data 附加数据
     */
    void sendSuccessResponse(AsyncWebServerRequest* request, const String& message, const DynamicJsonDocument& data = DynamicJsonDocument(0));
    
    // ==================== 安全和认证 ====================
    
    /**
     * 验证请求权限
     * @param request 请求对象
     * @return bool 是否有权限
     */
    bool validatePermission(AsyncWebServerRequest* request);
    
    /**
     * 验证API密钥
     * @param request 请求对象
     * @return bool API密钥是否有效
     */
    bool validateAPIKey(AsyncWebServerRequest* request);
    
    /**
     * 检查请求频率限制
     * @param request 请求对象
     * @return bool 是否超过频率限制
     */
    bool checkRateLimit(AsyncWebServerRequest* request);
    
    // ==================== 统计和监控 ====================
    
    /**
     * 获取服务器统计信息
     * @return DynamicJsonDocument 统计信息
     */
    DynamicJsonDocument getServerStatistics() const;
    
    /**
     * 记录请求
     * @param request 请求对象
     * @param responseCode 响应代码
     */
    void logRequest(AsyncWebServerRequest* request, int responseCode);
    
    /**
     * 获取活跃连接数
     * @return int 连接数
     */
    int getActiveConnections() const;
    
    // ==================== 调试功能 ====================
    
    /**
     * 启用调试模式
     * @param enable 是否启用
     */
    void enableDebugMode(bool enable);
    
    /**
     * 获取调试信息
     * @return String 调试信息
     */
    String getDebugInfo() const;

private:
    // ==================== 私有成员变量 ====================
    
    AsyncWebServer* m_server;           // Web服务器实例
    bool m_initialized;                 // 是否已初始化
    bool m_running;                     // 是否正在运行
    uint16_t m_port;                    // 服务器端口
    bool m_debugMode;                   // 调试模式
    
    // 依赖组件
    DataManager* m_dataManager;
    IRController* m_irController;
    TaskManager* m_taskManager;
    WSManager* m_wsManager;
    NetworkSecurity* m_networkSecurity;
    
    // 统计信息
    unsigned long m_totalRequests;      // 总请求数
    unsigned long m_successfulRequests; // 成功请求数
    unsigned long m_failedRequests;     // 失败请求数
    unsigned long m_startTime;          // 启动时间
    
    // ==================== 私有方法 ====================
    
    /**
     * 初始化服务器实例
     * @return bool 初始化是否成功
     */
    bool initializeServer();
    
    /**
     * 清理服务器资源
     */
    void cleanupServer();
    
    /**
     * 设置CORS头
     * @param response 响应对象
     */
    void setCORSHeaders(AsyncWebServerResponse* response);
    
    /**
     * 解析请求参数
     * @param request 请求对象
     * @return DynamicJsonDocument 解析后的参数
     */
    DynamicJsonDocument parseRequestParams(AsyncWebServerRequest* request);
    
    /**
     * 验证请求格式
     * @param request 请求对象
     * @return bool 格式是否正确
     */
    bool validateRequestFormat(AsyncWebServerRequest* request);
    
    /**
     * 记录错误
     * @param error 错误信息
     */
    void logError(const String& error);
};

#endif // WEB_SERVER_MANAGER_H
