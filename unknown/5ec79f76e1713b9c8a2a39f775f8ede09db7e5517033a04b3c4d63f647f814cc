#include "WSManager.h"

// ==================== 构造函数和析构函数 ====================
WSManager::WSManager(int maxConnections)
    : m_initialized(false)
    , m_maxConnections(maxConnections)
    , m_webSocket(nullptr)
    , m_totalConnections(0)
    , m_totalMessages(0)
    , m_totalBroadcasts(0)
    , m_connectionErrors(0)
{
    Serial.printf("🔌 WSManager created with max connections: %d\n", m_maxConnections);
}

WSManager::~WSManager() {
    shutdown();
    if (m_webSocket) {
        delete m_webSocket;
        m_webSocket = nullptr;
    }
    Serial.println("🔌 WSManager destroyed");
}

// ==================== 系统生命周期 ====================
bool WSManager::initialize() {
    if (m_initialized) {
        Serial.println("⚠️  WSManager already initialized");
        return true;
    }
    
    Serial.println("🔌 Initializing WSManager...");
    
    // 1. 创建WebSocket服务器实例
    m_webSocket = new AsyncWebSocket(WEBSOCKET_PATH);
    if (!m_webSocket) {
        logError("Failed to create WebSocket instance");
        return false;
    }
    
    // 2. 设置事件处理器
    setupEventHandlers();
    
    m_initialized = true;
    Serial.println("✅ WSManager initialization completed");
    return true;
}

void WSManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("🔌 Shutting down WSManager...");
    
    // 断开所有连接
    disconnectAll();
    
    m_initialized = false;
    Serial.println("✅ WSManager shutdown completed");
}

bool WSManager::isHealthy() const {
    return m_initialized && m_webSocket;
}

void WSManager::handleLoop() {
    if (!m_initialized || !m_webSocket) {
        return;
    }
    
    // 第一阶段：基本循环处理
    m_webSocket->cleanupClients();
}

// ==================== 连接管理 ====================
int WSManager::getConnectionCount() const {
    if (!m_webSocket) {
        return 0;
    }
    return m_webSocket->count();
}

DynamicJsonDocument WSManager::getConnectionStatus() {
    DynamicJsonDocument status(512);
    
    status["initialized"] = m_initialized;
    status["current_connections"] = getConnectionCount();
    status["max_connections"] = m_maxConnections;
    status["total_connections"] = m_totalConnections;
    status["total_messages"] = m_totalMessages;
    status["total_broadcasts"] = m_totalBroadcasts;
    status["connection_errors"] = m_connectionErrors;
    
    return status;
}

void WSManager::disconnectAll() {
    if (m_webSocket) {
        m_webSocket->closeAll();
        Serial.println("🔌 All WebSocket connections closed");
    }
}

void WSManager::disconnectClient(uint32_t clientId) {
    if (m_webSocket) {
        m_webSocket->close(clientId);
        Serial.printf("🔌 Client %u disconnected\n", clientId);
    }
}

// ==================== 消息发送 ====================
void WSManager::broadcastMessage(const String& message) {
    if (!m_webSocket) {
        return;
    }
    
    m_webSocket->textAll(message);
    updateStatistics("broadcast");
    
    Serial.printf("🔌 Broadcast message to %d clients\n", getConnectionCount());
}

void WSManager::broadcastJSON(const DynamicJsonDocument& jsonMessage) {
    String message;
    serializeJson(jsonMessage, message);
    broadcastMessage(message);
}

void WSManager::sendToClient(uint32_t clientId, const String& message) {
    if (!m_webSocket) {
        return;
    }
    
    m_webSocket->text(clientId, message);
    updateStatistics("message");
    
    Serial.printf("🔌 Message sent to client %u\n", clientId);
}

void WSManager::sendJSONToClient(uint32_t clientId, const DynamicJsonDocument& jsonMessage) {
    String message;
    serializeJson(jsonMessage, message);
    sendToClient(clientId, message);
}

// ==================== 系统事件通知 ====================
void WSManager::notifyTaskStarted(const DynamicJsonDocument& taskData) {
    DynamicJsonDocument message = createSystemMessage("task_started", taskData);
    broadcastJSON(message);
}

void WSManager::notifyTaskProgress(const DynamicJsonDocument& progressData) {
    DynamicJsonDocument message = createSystemMessage("task_progress", progressData);
    broadcastJSON(message);
}

void WSManager::notifyTaskCompleted(const DynamicJsonDocument& resultData) {
    DynamicJsonDocument message = createSystemMessage("task_completed", resultData);
    broadcastJSON(message);
}

void WSManager::notifyTaskPaused(const DynamicJsonDocument& pauseData) {
    DynamicJsonDocument message = createSystemMessage("task_paused", pauseData);
    broadcastJSON(message);
}

void WSManager::notifySignalSending(const DynamicJsonDocument& signalData) {
    DynamicJsonDocument message = createSystemMessage("signal_sending", signalData);
    broadcastJSON(message);
}

void WSManager::notifySignalLearned(const DynamicJsonDocument& learnData) {
    DynamicJsonDocument message = createSystemMessage("signal_learned", learnData);
    broadcastJSON(message);
}

void WSManager::notifyLearningStatusChanged(const DynamicJsonDocument& statusData) {
    DynamicJsonDocument message = createSystemMessage("learning_status_changed", statusData);
    broadcastJSON(message);
}

void WSManager::notifySystemStatusChanged(const DynamicJsonDocument& statusData) {
    DynamicJsonDocument message = createSystemMessage("system_status_changed", statusData);
    broadcastJSON(message);
}

void WSManager::notifySystemError(const DynamicJsonDocument& errorData) {
    DynamicJsonDocument message = createSystemMessage("system_error", errorData);
    broadcastJSON(message);
}

// ==================== 统计信息 ====================
DynamicJsonDocument WSManager::getStatistics() {
    DynamicJsonDocument stats(512);
    
    stats["total_connections"] = m_totalConnections;
    stats["current_connections"] = getConnectionCount();
    stats["total_messages"] = m_totalMessages;
    stats["total_broadcasts"] = m_totalBroadcasts;
    stats["connection_errors"] = m_connectionErrors;
    stats["max_connections"] = m_maxConnections;
    
    return stats;
}

void WSManager::resetStatistics() {
    m_totalConnections = 0;
    m_totalMessages = 0;
    m_totalBroadcasts = 0;
    m_connectionErrors = 0;
    Serial.println("🔌 WebSocket statistics reset");
}

// ==================== 私有方法 ====================
void WSManager::setupEventHandlers() {
    if (!m_webSocket) {
        return;
    }
    
    m_webSocket->onEvent([this](AsyncWebSocket* server, AsyncWebSocketClient* client, 
                               AwsEventType type, void* arg, uint8_t* data, size_t len) {
        handleWebSocketEvent(server, client, type, arg, data, len);
    });
    
    Serial.println("🔌 WebSocket event handlers configured");
}

void WSManager::handleWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                                   AwsEventType type, void* arg, uint8_t* data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT:
            handleClientConnect(client);
            break;
            
        case WS_EVT_DISCONNECT:
            handleClientDisconnect(client);
            break;
            
        case WS_EVT_DATA:
            handleClientMessage(client, data, len);
            break;
            
        case WS_EVT_PONG:
            Serial.printf("🔌 Pong received from client %u\n", client->id());
            break;
            
        case WS_EVT_ERROR:
            handleClientError(client);
            break;
    }
}

void WSManager::handleClientConnect(AsyncWebSocketClient* client) {
    if (!validateClient(client)) {
        client->close();
        updateStatistics("error");
        return;
    }
    
    updateStatistics("connect");
    
    Serial.printf("🔌 Client %u connected from %s\n", 
                 client->id(), client->remoteIP().toString().c_str());
    
    // 发送欢迎消息
    DynamicJsonDocument welcome(256);
    welcome["type"] = "welcome";
    welcome["client_id"] = client->id();
    welcome["server_time"] = millis();
    
    sendJSONToClient(client->id(), welcome);
}

void WSManager::handleClientDisconnect(AsyncWebSocketClient* client) {
    Serial.printf("🔌 Client %u disconnected\n", client->id());
}

void WSManager::handleClientMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len) {
    String message = String((char*)data, len);

    Serial.printf("🔌 Message from client %u: %s\n", client->id(), message.c_str());

    // 解析JSON消息
    DynamicJsonDocument doc(1024);
    DeserializationError error = deserializeJson(doc, message);

    if (error) {
        Serial.printf("🔌 JSON parsing error: %s\n", error.c_str());

        // 发送错误响应
        DynamicJsonDocument errorResponse(256);
        errorResponse["type"] = "error";
        errorResponse["message"] = "Invalid JSON format";
        errorResponse["error_code"] = "PARSE_ERROR";

        sendJSONToClient(client->id(), errorResponse);
        updateStatistics("error");
        return;
    }

    // 处理不同类型的消息
    String messageType = doc["type"] | "";

    if (messageType == "ping") {
        handlePingMessage(client, doc);
    } else if (messageType == "subscribe") {
        handleSubscribeMessage(client, doc);
    } else if (messageType == "unsubscribe") {
        handleUnsubscribeMessage(client, doc);
    } else if (messageType == "get_status") {
        handleGetStatusMessage(client, doc);
    } else if (messageType == "send_signal") {
        handleSendSignalMessage(client, doc);
    } else if (messageType == "start_learning") {
        handleStartLearningMessage(client, doc);
    } else if (messageType == "stop_learning") {
        handleStopLearningMessage(client, doc);
    } else {
        Serial.printf("🔌 Unknown message type: %s\n", messageType.c_str());

        // 发送未知消息类型错误
        DynamicJsonDocument errorResponse(256);
        errorResponse["type"] = "error";
        errorResponse["message"] = "Unknown message type: " + messageType;
        errorResponse["error_code"] = "UNKNOWN_MESSAGE_TYPE";

        sendJSONToClient(client->id(), errorResponse);
    }

    updateStatistics("message");
}

void WSManager::handleClientError(AsyncWebSocketClient* client) {
    Serial.printf("🔌 Error with client %u\n", client->id());
    updateStatistics("error");
}

DynamicJsonDocument WSManager::createSystemMessage(const String& type, const DynamicJsonDocument& payload) {
    DynamicJsonDocument message(1024);
    
    message["type"] = type;
    message["timestamp"] = millis();
    message["payload"] = payload;
    
    return message;
}

bool WSManager::validateClient(AsyncWebSocketClient* client) {
    // 第一阶段：基本验证
    if (getConnectionCount() >= m_maxConnections) {
        logError("Maximum connections reached");
        return false;
    }
    
    return true;
}

void WSManager::logError(const String& error) {
    Serial.printf("❌ WSManager Error: %s\n", error.c_str());
}

void WSManager::updateStatistics(const String& type) {
    if (type == "connect") {
        m_totalConnections++;
    } else if (type == "message") {
        m_totalMessages++;
    } else if (type == "broadcast") {
        m_totalBroadcasts++;
    } else if (type == "error") {
        m_connectionErrors++;
    }
}

// ==================== WebSocket消息处理方法实现 ====================
void WSManager::handlePingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    // 响应ping消息
    DynamicJsonDocument pongResponse(256);
    pongResponse["type"] = "pong";
    pongResponse["timestamp"] = millis();
    pongResponse["client_id"] = client->id();

    sendJSONToClient(client->id(), pongResponse);

    Serial.printf("🔌 Ping-Pong with client %u\n", client->id());
}

void WSManager::handleSubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    // 处理订阅消息
    String channel = message["channel"] | "";

    if (channel.isEmpty()) {
        DynamicJsonDocument errorResponse(256);
        errorResponse["type"] = "error";
        errorResponse["message"] = "Channel name is required for subscription";
        errorResponse["error_code"] = "MISSING_CHANNEL";

        sendJSONToClient(client->id(), errorResponse);
        return;
    }

    // 第四阶段简化：记录订阅但不实际实现频道管理
    Serial.printf("🔌 Client %u subscribed to channel: %s\n", client->id(), channel.c_str());

    DynamicJsonDocument response(256);
    response["type"] = "subscribed";
    response["channel"] = channel;
    response["client_id"] = client->id();

    sendJSONToClient(client->id(), response);
}

void WSManager::handleUnsubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    // 处理取消订阅消息
    String channel = message["channel"] | "";

    Serial.printf("🔌 Client %u unsubscribed from channel: %s\n", client->id(), channel.c_str());

    DynamicJsonDocument response(256);
    response["type"] = "unsubscribed";
    response["channel"] = channel;
    response["client_id"] = client->id();

    sendJSONToClient(client->id(), response);
}

void WSManager::handleGetStatusMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    // 发送系统状态
    DynamicJsonDocument statusResponse(512);
    statusResponse["type"] = "status_response";
    statusResponse["timestamp"] = millis();
    statusResponse["system_status"] = "running";
    statusResponse["uptime"] = millis();
    statusResponse["free_heap"] = ESP.getFreeHeap();
    statusResponse["connected_clients"] = getConnectionCount();

    if (PSRAMManager::isPSRAMAvailable()) {
        statusResponse["free_psram"] = PSRAMManager::getFreePSRAM();
        statusResponse["psram_mode"] = PSRAMManager::getSystemModeString(PSRAMManager::detectSystemMode());
    }

    sendJSONToClient(client->id(), statusResponse);
}

void WSManager::handleSendSignalMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    // 处理发送信号请求
    String signalId = message["signal_id"] | "";

    if (signalId.isEmpty()) {
        DynamicJsonDocument errorResponse(256);
        errorResponse["type"] = "error";
        errorResponse["message"] = "Signal ID is required";
        errorResponse["error_code"] = "MISSING_SIGNAL_ID";

        sendJSONToClient(client->id(), errorResponse);
        return;
    }

    // 第四阶段：通过WebSocket发送信号请求
    // 实际的信号发送应该通过系统管理器或直接调用IRController
    Serial.printf("🔌 Signal send request from client %u: %s\n", client->id(), signalId.c_str());

    DynamicJsonDocument response(256);
    response["type"] = "signal_send_response";
    response["signal_id"] = signalId;
    response["status"] = "queued";  // 第四阶段简化处理
    response["client_id"] = client->id();

    sendJSONToClient(client->id(), response);
}

void WSManager::handleStartLearningMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    // 处理开始学习请求
    unsigned long timeout = message["timeout"] | 10000;

    Serial.printf("🔌 Learning start request from client %u (timeout: %lu ms)\n", client->id(), timeout);

    DynamicJsonDocument response(256);
    response["type"] = "learning_start_response";
    response["status"] = "started";  // 第四阶段简化处理
    response["timeout"] = timeout;
    response["client_id"] = client->id();

    sendJSONToClient(client->id(), response);

    // 广播学习状态变化
    DynamicJsonDocument broadcastData(256);
    broadcastData["is_learning"] = true;
    broadcastData["timeout"] = timeout;
    notifyLearningStatusChanged(broadcastData);
}

void WSManager::handleStopLearningMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    // 处理停止学习请求
    Serial.printf("🔌 Learning stop request from client %u\n", client->id());

    DynamicJsonDocument response(256);
    response["type"] = "learning_stop_response";
    response["status"] = "stopped";  // 第四阶段简化处理
    response["client_id"] = client->id();

    sendJSONToClient(client->id(), response);

    // 广播学习状态变化
    DynamicJsonDocument broadcastData(256);
    broadcastData["is_learning"] = false;
    notifyLearningStatusChanged(broadcastData);
}
