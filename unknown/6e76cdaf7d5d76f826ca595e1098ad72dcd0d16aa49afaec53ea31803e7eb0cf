/**
 * WebSocket管理器 - 实现文件
 * 负责WebSocket连接管理、事件推送、实时通信
 */

#include "WebSocketManager.h"

// WSMessage类方法实现

String WSMessage::toJsonString() const {
    JsonDocument doc;
    JsonObject root = doc.to<JsonObject>();
    
    root["event"] = event;
    root["timestamp"] = timestamp;
    
    if (!data.isNull()) {
        root["data"] = data;
    }
    
    String result;
    serializeJson(doc, result);
    return result;
}

WSMessage WSMessage::createSignalLearned(const JsonObject& signalData) {
    WSMessage msg("signal.learned");
    JsonObject data = msg.data.createNestedObject("data");
    data["success"] = true;
    data["signal"] = signalData;
    return msg;
}

WSMessage WSMessage::createLearningError(const String& error) {
    WSMessage msg("learning.error");
    JsonObject data = msg.data.createNestedObject("data");
    data["error"] = error;
    data["code"] = "LEARNING_ERROR";
    return msg;
}

WSMessage WSMessage::createSignalSent(const String& signalId) {
    WSMessage msg("signal.sent");
    JsonObject data = msg.data.createNestedObject("data");
    data["signalId"] = signalId;
    data["success"] = true;
    return msg;
}

WSMessage WSMessage::createSignalFailed(const String& signalId, const String& error) {
    WSMessage msg("signal.failed");
    JsonObject data = msg.data.createNestedObject("data");
    data["signalId"] = signalId;
    data["success"] = false;
    data["error"] = error;
    return msg;
}

WSMessage WSMessage::createSystemStatus(const JsonObject& status) {
    WSMessage msg("system.status");
    msg.data["data"] = status;
    return msg;
}

// WebSocketManager类方法实现

WebSocketManager::WebSocketManager(AsyncWebSocket* ws, SignalManager* signalManager)
    : ws(ws), signalManager(signalManager), lastHeartbeat(0), heartbeatInterval(30000),
      messagesSent(0), messagesReceived(0), connectionCount(0),
      debugEnabled(true), initialized(false) {
    
    connectedClients.reserve(10); // 预分配空间
    messageQueue.reserve(MAX_QUEUE_SIZE);
}

WebSocketManager::~WebSocketManager() {
    if (ws) {
        ws->cleanupClients();
    }
}

bool WebSocketManager::init() {
    if (!ws) {
        Serial.println("❌ WebSocketManager: WebSocket对象未初始化");
        return false;
    }
    
    Serial.println("🔌 初始化WebSocket管理器...");
    
    // 设置WebSocket事件处理器
    ws->onEvent([this](AsyncWebSocket* server, AsyncWebSocketClient* client, 
                      AwsEventType type, void* arg, uint8_t* data, size_t len) {
        this->handleWebSocketEvent(server, client, type, arg, data, len);
    });
    
    initialized = true;
    Serial.println("✅ WebSocket管理器初始化完成");
    return true;
}

void WebSocketManager::handleWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                                           AwsEventType type, void* arg, uint8_t* data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT:
            handleClientConnect(client);
            break;
            
        case WS_EVT_DISCONNECT:
            handleClientDisconnect(client);
            break;
            
        case WS_EVT_DATA: {
            AwsFrameInfo* info = (AwsFrameInfo*)arg;
            if (info->final && info->index == 0 && info->len == len && info->opcode == WS_TEXT) {
                String message = String((char*)data, len);
                handleClientMessage(client, message);
            }
            break;
        }
        
        case WS_EVT_PONG:
            if (debugEnabled) {
                Serial.printf("🏓 收到客户端 %u 的PONG\n", client->id());
            }
            break;
            
        case WS_EVT_ERROR:
            Serial.printf("❌ WebSocket客户端 %u 错误\n", client->id());
            break;
    }
}

void WebSocketManager::handleClientConnect(AsyncWebSocketClient* client) {
    uint32_t clientId = client->id();
    
    // 添加到连接列表
    connectedClients.push_back(clientId);
    connectionCount++;
    
    Serial.printf("🔌 WebSocket客户端连接: %u (总计: %d)\n", clientId, connectedClients.size());
    
    // 发送欢迎消息
    JsonDocument welcomeData;
    JsonObject data = welcomeData.createNestedObject();
    data["message"] = "ESP32-S3 红外控制系统已连接";
    data["clientId"] = clientId;
    data["serverTime"] = millis();
    data["features"] = JsonArray();
    data["features"].add("signal_learning");
    data["features"].add("signal_transmit");
    data["features"].add("real_time_status");
    
    sendToClient(clientId, "esp32.connected", welcomeData);
    
    // 发送当前系统状态
    broadcastSystemStatus();
}

void WebSocketManager::handleClientDisconnect(AsyncWebSocketClient* client) {
    uint32_t clientId = client->id();
    
    // 从连接列表中移除
    auto it = std::find(connectedClients.begin(), connectedClients.end(), clientId);
    if (it != connectedClients.end()) {
        connectedClients.erase(it);
    }
    
    Serial.printf("🔌 WebSocket客户端断开: %u (剩余: %d)\n", clientId, connectedClients.size());
}

void WebSocketManager::handleClientMessage(AsyncWebSocketClient* client, const String& message) {
    messagesReceived++;
    
    if (debugEnabled) {
        Serial.printf("📨 收到客户端 %u 消息: %s\n", client->id(), message.c_str());
    }
    
    // 解析消息
    JsonDocument doc;
    if (!parseClientMessage(message, doc)) {
        sendResponse(client, "", false);
        return;
    }
    
    JsonObject request = doc.as<JsonObject>();
    handleClientRequest(client, request);
}

bool WebSocketManager::parseClientMessage(const String& message, JsonDocument& doc) {
    DeserializationError error = deserializeJson(doc, message);
    if (error) {
        Serial.printf("❌ JSON解析失败: %s\n", error.c_str());
        return false;
    }
    return true;
}

void WebSocketManager::handleClientRequest(AsyncWebSocketClient* client, const JsonObject& request) {
    String requestType = request["type"] | "";
    String requestId = request["id"] | "";
    
    if (requestType == "ping") {
        // 心跳响应
        JsonDocument responseData;
        responseData["pong"] = true;
        responseData["serverTime"] = millis();
        sendResponse(client, requestId, true, responseData);
        
    } else if (requestType == "get_status") {
        // 获取系统状态
        JsonDocument statusData;
        JsonObject status = statusData.createNestedObject();
        status["connected_clients"] = connectedClients.size();
        status["messages_sent"] = messagesSent;
        status["messages_received"] = messagesReceived;
        status["uptime"] = millis();
        status["free_heap"] = ESP.getFreeHeap();
        
        sendResponse(client, requestId, true, statusData);
        
    } else {
        // 未知请求类型
        Serial.printf("⚠️ 未知请求类型: %s\n", requestType.c_str());
        sendResponse(client, requestId, false);
    }
}

void WebSocketManager::sendResponse(AsyncWebSocketClient* client, const String& requestId, 
                                   bool success, const JsonDocument& data) {
    JsonDocument response;
    JsonObject root = response.to<JsonObject>();
    
    root["type"] = "response";
    root["id"] = requestId;
    root["success"] = success;
    root["timestamp"] = millis();
    
    if (!data.isNull()) {
        root["data"] = data;
    }
    
    String responseStr;
    serializeJson(response, responseStr);
    
    client->text(responseStr);
    messagesSent++;
}

void WebSocketManager::broadcast(const WSMessage& message) {
    if (connectedClients.empty()) {
        // 如果没有连接的客户端，将消息加入队列
        queueMessage(message);
        return;
    }
    
    String messageStr = message.toJsonString();
    
    for (uint32_t clientId : connectedClients) {
        AsyncWebSocketClient* client = ws->client(clientId);
        if (client && client->status() == WS_CONNECTED) {
            client->text(messageStr);
            messagesSent++;
        }
    }
    
    if (debugEnabled) {
        Serial.printf("📡 广播消息: %s (客户端: %d)\n", message.event.c_str(), connectedClients.size());
    }
}

void WebSocketManager::broadcast(const String& event, const JsonDocument& data) {
    WSMessage message(event);
    if (!data.isNull()) {
        message.data = data;
    }
    broadcast(message);
}

void WebSocketManager::sendToClient(uint32_t clientId, const WSMessage& message) {
    AsyncWebSocketClient* client = ws->client(clientId);
    if (client && client->status() == WS_CONNECTED) {
        String messageStr = message.toJsonString();
        client->text(messageStr);
        messagesSent++;
        
        if (debugEnabled) {
            Serial.printf("📤 发送消息到客户端 %u: %s\n", clientId, message.event.c_str());
        }
    }
}

void WebSocketManager::sendToClient(uint32_t clientId, const String& event, const JsonDocument& data) {
    WSMessage message(event);
    if (!data.isNull()) {
        message.data = data;
    }
    sendToClient(clientId, message);
}

void WebSocketManager::broadcastSignalLearned(const JsonObject& signalData) {
    WSMessage message = WSMessage::createSignalLearned(signalData);
    broadcast(message);
}

void WebSocketManager::broadcastLearningError(const String& error) {
    WSMessage message = WSMessage::createLearningError(error);
    broadcast(message);
}

void WebSocketManager::broadcastSignalSent(const String& signalId) {
    WSMessage message = WSMessage::createSignalSent(signalId);
    broadcast(message);
}

void WebSocketManager::broadcastSignalFailed(const String& signalId, const String& error) {
    WSMessage message = WSMessage::createSignalFailed(signalId, error);
    broadcast(message);
}

void WebSocketManager::broadcastSystemStatus() {
    JsonDocument statusData;
    JsonObject status = statusData.createNestedObject();
    
    status["timestamp"] = millis();
    status["connected_clients"] = connectedClients.size();
    status["free_heap"] = ESP.getFreeHeap();
    status["uptime"] = millis();
    
    if (signalManager) {
        status["signal_count"] = signalManager->getSignalCount();
        status["total_sent"] = signalManager->getTotalSentCount();
    }
    
    WSMessage message = WSMessage::createSystemStatus(status);
    broadcast(message);
}

void WebSocketManager::queueMessage(const WSMessage& message) {
    if (messageQueue.size() >= MAX_QUEUE_SIZE) {
        // 队列满了，移除最老的消息
        messageQueue.erase(messageQueue.begin());
    }
    
    messageQueue.push_back(message);
    
    if (debugEnabled) {
        Serial.printf("📥 消息已加入队列: %s (队列大小: %d)\n", 
                     message.event.c_str(), messageQueue.size());
    }
}

void WebSocketManager::processQueue() {
    if (messageQueue.empty() || connectedClients.empty()) {
        return;
    }
    
    // 发送队列中的所有消息
    for (const WSMessage& message : messageQueue) {
        broadcast(message);
    }
    
    // 清空队列
    messageQueue.clear();
    
    if (debugEnabled) {
        Serial.println("📤 消息队列已处理完成");
    }
}

void WebSocketManager::handleHeartbeat() {
    unsigned long now = millis();
    if (now - lastHeartbeat >= heartbeatInterval) {
        lastHeartbeat = now;
        
        // 发送心跳到所有客户端
        sendHeartbeat();
        
        // 清理断开的客户端
        cleanupClients();
        
        // 处理消息队列
        processQueue();
    }
}

void WebSocketManager::sendHeartbeat() {
    JsonDocument heartbeatData;
    heartbeatData["ping"] = true;
    heartbeatData["timestamp"] = millis();
    
    broadcast("heartbeat", heartbeatData);
}

void WebSocketManager::cleanupClients() {
    auto it = connectedClients.begin();
    while (it != connectedClients.end()) {
        AsyncWebSocketClient* client = ws->client(*it);
        if (!client || client->status() != WS_CONNECTED) {
            it = connectedClients.erase(it);
        } else {
            ++it;
        }
    }
}
