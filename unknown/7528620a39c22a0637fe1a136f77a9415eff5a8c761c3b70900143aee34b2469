#include "DataManager.h"
#include "../core/MemoryAllocator.h"

// ==================== 构造函数和析构函数 ====================
DataManager::DataManager(const SystemCapacity& capacity)
    : m_capacity(capacity)
    , m_initialized(false)
    , m_signalStorage(nullptr)
    , m_taskStorage(nullptr)
    , m_timerStorage(nullptr)
    , m_signalCount(0)
    , m_taskCount(0)
    , m_timerCount(0)
    , m_signalsChanged(false)
    , m_tasksChanged(false)
    , m_timersChanged(false)
    , m_lastSaveTime(0)
{
    Serial.printf("📊 DataManager created with capacity: %d signals, %d tasks, %d timers\n",
                 m_capacity.maxSignals, m_capacity.maxTasks, m_capacity.maxTimers);
}

DataManager::~DataManager() {
    shutdown();
    cleanupDataMaps();
    cleanupStorage();
    Serial.println("📊 DataManager destroyed");
}

// ==================== 系统生命周期 ====================
bool DataManager::initialize() {
    if (m_initialized) {
        Serial.println("⚠️  DataManager already initialized");
        return true;
    }
    
    Serial.println("📊 Initializing DataManager...");
    
    // 1. 初始化存储
    if (!initializeStorage()) {
        logError("Storage initialization failed");
        return false;
    }
    
    // 2. 初始化数据映射表
    if (!initializeDataMaps()) {
        logError("Data maps initialization failed");
        return false;
    }

    // 3. 加载数据文件
    if (!loadDataFromFiles()) {
        Serial.println("⚠️  No existing data files found, starting with empty data");
    }
    
    m_initialized = true;
    Serial.println("✅ DataManager initialization completed");
    return true;
}

void DataManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("📊 Shutting down DataManager...");
    
    // 保存数据到文件
    saveDataToFiles();
    
    m_initialized = false;
    Serial.println("✅ DataManager shutdown completed");
}

void DataManager::factoryReset() {
    Serial.println("🏭 DataManager: Performing factory reset...");
    
    // 清除所有数据
    m_signalCount = 0;
    m_taskCount = 0;
    m_timerCount = 0;
    
    // 删除数据文件
    if (SPIFFS.exists(SIGNALS_FILE_PATH)) {
        SPIFFS.remove(SIGNALS_FILE_PATH);
    }
    if (SPIFFS.exists(TASKS_FILE_PATH)) {
        SPIFFS.remove(TASKS_FILE_PATH);
    }
    if (SPIFFS.exists(TIMERS_FILE_PATH)) {
        SPIFFS.remove(TIMERS_FILE_PATH);
    }
    
    Serial.println("✅ DataManager factory reset completed");
}

bool DataManager::isHealthy() const {
    return m_initialized && m_signalStorage && m_taskStorage && m_timerStorage;
}

// ==================== 基本数据获取 ====================
int DataManager::getSignalCount() const {
    return m_signalCount;
}

int DataManager::getTaskCount() const {
    return m_taskCount;
}

int DataManager::getTimerCount() const {
    return m_timerCount;
}

// ==================== 信号数据管理 ====================
DynamicJsonDocument DataManager::getAllSignals() const {
    DynamicJsonDocument signals(m_capacity.bufferSize);
    
    // 第一阶段：返回空数组，第二阶段将实现完整功能
    signals.to<JsonArray>();
    
    return signals;
}

DynamicJsonDocument DataManager::getSignal(const String& signalId) const {
    DynamicJsonDocument signal(512);
    
    // 第一阶段：返回空对象，第二阶段将实现完整功能
    signal["id"] = signalId;
    signal["found"] = false;
    
    return signal;
}

String DataManager::createSignal(const DynamicJsonDocument& signalData) {
    if (!validateSignalData(signalData)) {
        logError("Invalid signal data");
        return "";
    }
    
    // 第一阶段：生成ID但不实际存储，第二阶段将实现完整功能
    String signalId = generateUniqueId("signal_");
    m_signalCount++;
    
    Serial.printf("📊 Signal created with ID: %s (placeholder)\n", signalId.c_str());
    return signalId;
}

bool DataManager::updateSignal(const String& signalId, const DynamicJsonDocument& signalData) {
    if (!validateSignalData(signalData)) {
        logError("Invalid signal data");
        return false;
    }
    
    // 第一阶段：返回成功，第二阶段将实现完整功能
    Serial.printf("📊 Signal updated: %s (placeholder)\n", signalId.c_str());
    return true;
}

bool DataManager::deleteSignal(const String& signalId) {
    // 第一阶段：返回成功，第二阶段将实现完整功能
    if (m_signalCount > 0) {
        m_signalCount--;
    }
    
    Serial.printf("📊 Signal deleted: %s (placeholder)\n", signalId.c_str());
    return true;
}

int DataManager::batchDeleteSignals(const std::vector<String>& signalIds) {
    int deletedCount = 0;
    
    for (const String& signalId : signalIds) {
        if (deleteSignal(signalId)) {
            deletedCount++;
        }
    }
    
    Serial.printf("📊 Batch deleted %d signals (placeholder)\n", deletedCount);
    return deletedCount;
}

// ==================== 任务数据管理 ====================
DynamicJsonDocument DataManager::getAllTasks() const {
    DynamicJsonDocument tasks(m_capacity.bufferSize);
    tasks.to<JsonArray>();
    return tasks;
}

DynamicJsonDocument DataManager::getTask(const String& taskId) const {
    DynamicJsonDocument task(512);
    task["id"] = taskId;
    task["found"] = false;
    return task;
}

String DataManager::createTask(const DynamicJsonDocument& taskData) {
    if (!validateTaskData(taskData)) {
        logError("Invalid task data");
        return "";
    }
    
    String taskId = generateUniqueId("task_");
    m_taskCount++;
    
    Serial.printf("📊 Task created with ID: %s (placeholder)\n", taskId.c_str());
    return taskId;
}

bool DataManager::updateTask(const String& taskId, const DynamicJsonDocument& taskData) {
    if (!validateTaskData(taskData)) {
        logError("Invalid task data");
        return false;
    }
    
    Serial.printf("📊 Task updated: %s (placeholder)\n", taskId.c_str());
    return true;
}

bool DataManager::deleteTask(const String& taskId) {
    if (m_taskCount > 0) {
        m_taskCount--;
    }
    
    Serial.printf("📊 Task deleted: %s (placeholder)\n", taskId.c_str());
    return true;
}

// ==================== 定时器数据管理 ====================
DynamicJsonDocument DataManager::getAllTimers() const {
    DynamicJsonDocument timers(m_capacity.bufferSize);
    timers.to<JsonArray>();
    return timers;
}

DynamicJsonDocument DataManager::getTimer(const String& timerId) const {
    DynamicJsonDocument timer(512);
    timer["id"] = timerId;
    timer["found"] = false;
    return timer;
}

String DataManager::createTimer(const DynamicJsonDocument& timerData) {
    if (!validateTimerData(timerData)) {
        logError("Invalid timer data");
        return "";
    }
    
    String timerId = generateUniqueId("timer_");
    m_timerCount++;
    
    Serial.printf("📊 Timer created with ID: %s (placeholder)\n", timerId.c_str());
    return timerId;
}

bool DataManager::updateTimer(const String& timerId, const DynamicJsonDocument& timerData) {
    if (!validateTimerData(timerData)) {
        logError("Invalid timer data");
        return false;
    }
    
    Serial.printf("📊 Timer updated: %s (placeholder)\n", timerId.c_str());
    return true;
}

bool DataManager::deleteTimer(const String& timerId) {
    if (m_timerCount > 0) {
        m_timerCount--;
    }
    
    Serial.printf("📊 Timer deleted: %s (placeholder)\n", timerId.c_str());
    return true;
}

// ==================== 数据验证 ====================
bool DataManager::validateSignalData(const DynamicJsonDocument& signalData) const {
    // 基本验证：检查必需字段
    if (!signalData.containsKey("name") || !signalData.containsKey("signalCode")) {
        return false;
    }
    
    // 第一阶段：基本验证，第二阶段将实现完整验证
    return true;
}

bool DataManager::validateTaskData(const DynamicJsonDocument& taskData) const {
    // 基本验证：检查必需字段
    if (!taskData.containsKey("name") || !taskData.containsKey("type")) {
        return false;
    }
    
    return true;
}

bool DataManager::validateTimerData(const DynamicJsonDocument& timerData) const {
    // 基本验证：检查必需字段
    if (!timerData.containsKey("name") || !timerData.containsKey("schedule")) {
        return false;
    }
    
    return true;
}

// ==================== 统计信息 ====================
DynamicJsonDocument DataManager::getStatistics() const {
    DynamicJsonDocument stats(512);
    
    stats["signal_count"] = m_signalCount;
    stats["task_count"] = m_taskCount;
    stats["timer_count"] = m_timerCount;
    stats["max_signals"] = m_capacity.maxSignals;
    stats["max_tasks"] = m_capacity.maxTasks;
    stats["max_timers"] = m_capacity.maxTimers;
    stats["storage_healthy"] = isHealthy();
    
    return stats;
}

void DataManager::updateStatistics() {
    // 第一阶段：占位符，第二阶段将实现完整统计更新
    Serial.println("📊 Statistics updated (placeholder)");
}

// ==================== 数据导入导出 ====================
DynamicJsonDocument DataManager::exportAllData() const {
    DynamicJsonDocument exportData(m_capacity.bufferSize * 2);
    
    exportData["signals"] = getAllSignals();
    exportData["tasks"] = getAllTasks();
    exportData["timers"] = getAllTimers();
    exportData["statistics"] = getStatistics();
    exportData["export_time"] = millis();
    
    return exportData;
}

bool DataManager::importData(const DynamicJsonDocument& data) {
    // 第一阶段：基本验证，第二阶段将实现完整导入
    if (!data.containsKey("signals") || !data.containsKey("tasks") || !data.containsKey("timers")) {
        logError("Invalid import data format");
        return false;
    }
    
    Serial.println("📊 Data import completed (placeholder)");
    return true;
}

bool DataManager::backupData() {
    // 第一阶段：占位符，第二阶段将实现完整备份
    Serial.println("📊 Data backup completed (placeholder)");
    return true;
}

bool DataManager::restoreData() {
    // 第一阶段：占位符，第二阶段将实现完整恢复
    Serial.println("📊 Data restore completed (placeholder)");
    return true;
}

// ==================== 私有方法 ====================
bool DataManager::initializeStorage() {
    // 使用智能内存分配器分配存储空间
    size_t signalStorageSize = m_capacity.maxSignals * 512;  // 每个信号512字节
    size_t taskStorageSize = m_capacity.maxTasks * 256;      // 每个任务256字节
    size_t timerStorageSize = m_capacity.maxTimers * 256;    // 每个定时器256字节
    
    m_signalStorage = MemoryAllocator::smartAlloc(signalStorageSize);
    m_taskStorage = MemoryAllocator::smartAlloc(taskStorageSize);
    m_timerStorage = MemoryAllocator::smartAlloc(timerStorageSize);
    
    if (!m_signalStorage || !m_taskStorage || !m_timerStorage) {
        logError("Failed to allocate storage memory");
        cleanupStorage();
        return false;
    }
    
    // 初始化存储区域
    memset(m_signalStorage, 0, signalStorageSize);
    memset(m_taskStorage, 0, taskStorageSize);
    memset(m_timerStorage, 0, timerStorageSize);
    
    Serial.printf("📊 Storage initialized: %d KB allocated\n", 
                 (signalStorageSize + taskStorageSize + timerStorageSize) / 1024);
    return true;
}

void DataManager::cleanupStorage() {
    if (m_signalStorage) {
        MemoryAllocator::smartFree(m_signalStorage);
        m_signalStorage = nullptr;
    }
    
    if (m_taskStorage) {
        MemoryAllocator::smartFree(m_taskStorage);
        m_taskStorage = nullptr;
    }
    
    if (m_timerStorage) {
        MemoryAllocator::smartFree(m_timerStorage);
        m_timerStorage = nullptr;
    }
}

bool DataManager::loadDataFromFiles() {
    Serial.println("📊 Loading data from files...");

    bool success = true;

    // 1. 加载信号数据
    if (SPIFFS.exists(SIGNALS_FILE_PATH)) {
        File signalFile = SPIFFS.open(SIGNALS_FILE_PATH, "r");
        if (signalFile) {
            DynamicJsonDocument signalDoc(m_capacity.bufferSize);
            DeserializationError error = deserializeJson(signalDoc, signalFile);
            signalFile.close();

            if (error) {
                logError("Failed to parse signals file: " + String(error.c_str()));
                success = false;
            } else {
                if (signalDoc.is<JsonArray>()) {
                    m_signalCount = signalDoc.size();
                    Serial.printf("📊 Loaded %d signals\n", m_signalCount);
                }
            }
        } else {
            logError("Failed to open signals file");
            success = false;
        }
    }

    // 2. 加载任务数据
    if (SPIFFS.exists(TASKS_FILE_PATH)) {
        File taskFile = SPIFFS.open(TASKS_FILE_PATH, "r");
        if (taskFile) {
            DynamicJsonDocument taskDoc(m_capacity.bufferSize);
            DeserializationError error = deserializeJson(taskDoc, taskFile);
            taskFile.close();

            if (error) {
                logError("Failed to parse tasks file: " + String(error.c_str()));
                success = false;
            } else {
                if (taskDoc.is<JsonArray>()) {
                    m_taskCount = taskDoc.size();
                    Serial.printf("📊 Loaded %d tasks\n", m_taskCount);
                }
            }
        }
    }

    // 3. 加载定时器数据
    if (SPIFFS.exists(TIMERS_FILE_PATH)) {
        File timerFile = SPIFFS.open(TIMERS_FILE_PATH, "r");
        if (timerFile) {
            DynamicJsonDocument timerDoc(m_capacity.bufferSize);
            DeserializationError error = deserializeJson(timerDoc, timerFile);
            timerFile.close();

            if (error) {
                logError("Failed to parse timers file: " + String(error.c_str()));
                success = false;
            } else {
                if (timerDoc.is<JsonArray>()) {
                    m_timerCount = timerDoc.size();
                    Serial.printf("📊 Loaded %d timers\n", m_timerCount);
                }
            }
        }
    }

    Serial.printf("📊 Data loading completed: %s\n", success ? "SUCCESS" : "PARTIAL");
    return success;
}

bool DataManager::saveDataToFiles() {
    Serial.println("📊 Saving data to files...");

    bool success = true;

    // 1. 保存信号数据
    DynamicJsonDocument signals = getAllSignals();
    File signalFile = SPIFFS.open(SIGNALS_FILE_PATH, "w");
    if (signalFile) {
        size_t bytesWritten = serializeJson(signals, signalFile);
        signalFile.close();

        if (bytesWritten > 0) {
            Serial.printf("📊 Saved %d signals (%d bytes)\n", m_signalCount, bytesWritten);
        } else {
            logError("Failed to write signals file");
            success = false;
        }
    } else {
        logError("Failed to create signals file");
        success = false;
    }

    // 2. 保存任务数据
    DynamicJsonDocument tasks = getAllTasks();
    File taskFile = SPIFFS.open(TASKS_FILE_PATH, "w");
    if (taskFile) {
        size_t bytesWritten = serializeJson(tasks, taskFile);
        taskFile.close();

        if (bytesWritten > 0) {
            Serial.printf("📊 Saved %d tasks (%d bytes)\n", m_taskCount, bytesWritten);
        } else {
            logError("Failed to write tasks file");
            success = false;
        }
    } else {
        logError("Failed to create tasks file");
        success = false;
    }

    // 3. 保存定时器数据
    DynamicJsonDocument timers = getAllTimers();
    File timerFile = SPIFFS.open(TIMERS_FILE_PATH, "w");
    if (timerFile) {
        size_t bytesWritten = serializeJson(timers, timerFile);
        timerFile.close();

        if (bytesWritten > 0) {
            Serial.printf("📊 Saved %d timers (%d bytes)\n", m_timerCount, bytesWritten);
        } else {
            logError("Failed to write timers file");
            success = false;
        }
    } else {
        logError("Failed to create timers file");
        success = false;
    }

    Serial.printf("📊 Data saving completed: %s\n", success ? "SUCCESS" : "PARTIAL");
    return success;
}

String DataManager::generateUniqueId(const String& prefix) {
    static unsigned long counter = 0;
    return prefix + String(millis()) + "_" + String(counter++);
}

void DataManager::logError(const String& error) {
    Serial.printf("❌ DataManager Error: %s\n", error.c_str());
}

// ==================== 数据映射管理实现 ====================
bool DataManager::initializeDataMaps() {
    // 清理现有映射表
    cleanupDataMaps();

    Serial.println("📊 Initializing data maps...");

    // 映射表已经在构造函数中初始化为空
    // 这里只需要确保它们是空的
    m_signalMap.clear();
    m_taskMap.clear();
    m_timerMap.clear();

    Serial.println("✅ Data maps initialized");
    return true;
}

void DataManager::cleanupDataMaps() {
    // 清理信号映射表
    for (auto& pair : m_signalMap) {
        if (pair.second) {
            delete pair.second;
        }
    }
    m_signalMap.clear();

    // 清理任务映射表
    for (auto& pair : m_taskMap) {
        if (pair.second) {
            delete pair.second;
        }
    }
    m_taskMap.clear();

    // 清理定时器映射表
    for (auto& pair : m_timerMap) {
        if (pair.second) {
            delete pair.second;
        }
    }
    m_timerMap.clear();

    Serial.println("📊 Data maps cleaned up");
}

bool DataManager::addSignalToMap(SignalData* signal) {
    if (!signal || signal->id.isEmpty()) {
        return false;
    }

    // 检查是否已存在
    if (m_signalMap.find(signal->id) != m_signalMap.end()) {
        logError("Signal ID already exists: " + signal->id);
        return false;
    }

    m_signalMap[signal->id] = signal;
    return true;
}

bool DataManager::removeSignalFromMap(const String& signalId) {
    auto it = m_signalMap.find(signalId);
    if (it != m_signalMap.end()) {
        if (it->second) {
            delete it->second;
        }
        m_signalMap.erase(it);
        return true;
    }
    return false;
}

bool DataManager::addTaskToMap(TaskData* task) {
    if (!task || task->id.isEmpty()) {
        return false;
    }

    if (m_taskMap.find(task->id) != m_taskMap.end()) {
        logError("Task ID already exists: " + task->id);
        return false;
    }

    m_taskMap[task->id] = task;
    return true;
}

bool DataManager::removeTaskFromMap(const String& taskId) {
    auto it = m_taskMap.find(taskId);
    if (it != m_taskMap.end()) {
        if (it->second) {
            delete it->second;
        }
        m_taskMap.erase(it);
        return true;
    }
    return false;
}

bool DataManager::addTimerToMap(TimerData* timer) {
    if (!timer || timer->id.isEmpty()) {
        return false;
    }

    if (m_timerMap.find(timer->id) != m_timerMap.end()) {
        logError("Timer ID already exists: " + timer->id);
        return false;
    }

    m_timerMap[timer->id] = timer;
    return true;
}

bool DataManager::removeTimerFromMap(const String& timerId) {
    auto it = m_timerMap.find(timerId);
    if (it != m_timerMap.end()) {
        if (it->second) {
            delete it->second;
        }
        m_timerMap.erase(it);
        return true;
    }
    return false;
}

// ==================== 高级数据管理实现 ====================
SignalData* DataManager::getSignalPtr(const String& signalId) {
    auto it = m_signalMap.find(signalId);
    return (it != m_signalMap.end()) ? it->second : nullptr;
}

TaskData* DataManager::getTaskPtr(const String& taskId) {
    auto it = m_taskMap.find(taskId);
    return (it != m_taskMap.end()) ? it->second : nullptr;
}

TimerData* DataManager::getTimerPtr(const String& timerId) {
    auto it = m_timerMap.find(timerId);
    return (it != m_timerMap.end()) ? it->second : nullptr;
}

bool DataManager::autoSaveIfChanged() {
    if (!m_signalsChanged && !m_tasksChanged && !m_timersChanged) {
        return true; // 没有变更，不需要保存
    }

    // 检查是否需要延迟保存（避免频繁保存）
    unsigned long currentTime = millis();
    if (currentTime - m_lastSaveTime < 5000) { // 5秒内不重复保存
        return true;
    }

    bool success = saveDataToFiles();
    if (success) {
        m_signalsChanged = false;
        m_tasksChanged = false;
        m_timersChanged = false;
        m_lastSaveTime = currentTime;
        Serial.println("📊 Auto-save completed");
    }

    return success;
}

void DataManager::markDataChanged(const String& dataType) {
    if (dataType == "signals") {
        m_signalsChanged = true;
    } else if (dataType == "tasks") {
        m_tasksChanged = true;
    } else if (dataType == "timers") {
        m_timersChanged = true;
    }

    Serial.printf("📊 Data marked as changed: %s\n", dataType.c_str());
}

int DataManager::cleanupInvalidData() {
    int cleanedCount = 0;

    Serial.println("📊 Cleaning up invalid data...");

    // 清理无效信号
    std::vector<String> invalidSignals;
    for (auto& pair : m_signalMap) {
        if (!pair.second || !pair.second->validate()) {
            invalidSignals.push_back(pair.first);
        }
    }

    for (const String& signalId : invalidSignals) {
        if (removeSignalFromMap(signalId)) {
            cleanedCount++;
            m_signalCount--;
        }
    }

    // 清理无效任务
    std::vector<String> invalidTasks;
    for (auto& pair : m_taskMap) {
        if (!pair.second || !pair.second->validate()) {
            invalidTasks.push_back(pair.first);
        }
    }

    for (const String& taskId : invalidTasks) {
        if (removeTaskFromMap(taskId)) {
            cleanedCount++;
            m_taskCount--;
        }
    }

    // 清理无效定时器
    std::vector<String> invalidTimers;
    for (auto& pair : m_timerMap) {
        if (!pair.second || !pair.second->validate()) {
            invalidTimers.push_back(pair.first);
        }
    }

    for (const String& timerId : invalidTimers) {
        if (removeTimerFromMap(timerId)) {
            cleanedCount++;
            m_timerCount--;
        }
    }

    if (cleanedCount > 0) {
        markDataChanged("signals");
        markDataChanged("tasks");
        markDataChanged("timers");
        Serial.printf("📊 Cleaned up %d invalid data entries\n", cleanedCount);
    }

    return cleanedCount;
}

bool DataManager::optimizeStorage() {
    Serial.println("📊 Optimizing storage...");

    // 1. 清理无效数据
    int cleanedCount = cleanupInvalidData();

    // 2. 压缩存储空间（如果需要）
    // 第二阶段简化处理，主要是确保数据一致性

    // 3. 验证数据完整性
    bool integrityOk = validateDataIntegrity();

    // 4. 同步映射表和存储
    bool syncOk = syncMapsWithStorage();

    bool success = integrityOk && syncOk;

    Serial.printf("📊 Storage optimization completed: %s (cleaned %d entries)\n",
                 success ? "SUCCESS" : "FAILED", cleanedCount);

    return success;
}

DynamicJsonDocument DataManager::getDataIntegrityReport() {
    DynamicJsonDocument report(1024);

    report["timestamp"] = millis();
    report["signal_count"] = m_signalCount;
    report["task_count"] = m_taskCount;
    report["timer_count"] = m_timerCount;
    report["signal_map_size"] = m_signalMap.size();
    report["task_map_size"] = m_taskMap.size();
    report["timer_map_size"] = m_timerMap.size();

    // 检查数据一致性
    bool signalConsistency = (m_signalCount == (int)m_signalMap.size());
    bool taskConsistency = (m_taskCount == (int)m_taskMap.size());
    bool timerConsistency = (m_timerCount == (int)m_timerMap.size());

    report["signal_consistency"] = signalConsistency;
    report["task_consistency"] = taskConsistency;
    report["timer_consistency"] = timerConsistency;
    report["overall_integrity"] = signalConsistency && taskConsistency && timerConsistency;

    // 检查数据有效性
    int invalidSignals = 0, invalidTasks = 0, invalidTimers = 0;

    for (auto& pair : m_signalMap) {
        if (!pair.second || !pair.second->validate()) {
            invalidSignals++;
        }
    }

    for (auto& pair : m_taskMap) {
        if (!pair.second || !pair.second->validate()) {
            invalidTasks++;
        }
    }

    for (auto& pair : m_timerMap) {
        if (!pair.second || !pair.second->validate()) {
            invalidTimers++;
        }
    }

    report["invalid_signals"] = invalidSignals;
    report["invalid_tasks"] = invalidTasks;
    report["invalid_timers"] = invalidTimers;
    report["total_invalid"] = invalidSignals + invalidTasks + invalidTimers;

    // 变更状态
    report["signals_changed"] = m_signalsChanged;
    report["tasks_changed"] = m_tasksChanged;
    report["timers_changed"] = m_timersChanged;
    report["last_save_time"] = m_lastSaveTime;

    return report;
}

// ==================== 数据验证和修复实现 ====================
bool DataManager::validateDataIntegrity() {
    bool isValid = true;

    // 检查计数一致性
    if (m_signalCount != (int)m_signalMap.size()) {
        logError("Signal count mismatch: " + String(m_signalCount) + " vs " + String(m_signalMap.size()));
        isValid = false;
    }

    if (m_taskCount != (int)m_taskMap.size()) {
        logError("Task count mismatch: " + String(m_taskCount) + " vs " + String(m_taskMap.size()));
        isValid = false;
    }

    if (m_timerCount != (int)m_timerMap.size()) {
        logError("Timer count mismatch: " + String(m_timerCount) + " vs " + String(m_timerMap.size()));
        isValid = false;
    }

    // 检查数据有效性
    for (auto& pair : m_signalMap) {
        if (!pair.second || !pair.second->validate()) {
            logError("Invalid signal data: " + pair.first);
            isValid = false;
        }
    }

    for (auto& pair : m_taskMap) {
        if (!pair.second || !pair.second->validate()) {
            logError("Invalid task data: " + pair.first);
            isValid = false;
        }
    }

    for (auto& pair : m_timerMap) {
        if (!pair.second || !pair.second->validate()) {
            logError("Invalid timer data: " + pair.first);
            isValid = false;
        }
    }

    return isValid;
}

int DataManager::repairCorruptedData() {
    int repairedCount = 0;

    Serial.println("📊 Repairing corrupted data...");

    // 修复计数不一致
    if (m_signalCount != (int)m_signalMap.size()) {
        m_signalCount = m_signalMap.size();
        repairedCount++;
        Serial.printf("📊 Repaired signal count: %d\n", m_signalCount);
    }

    if (m_taskCount != (int)m_taskMap.size()) {
        m_taskCount = m_taskMap.size();
        repairedCount++;
        Serial.printf("📊 Repaired task count: %d\n", m_taskCount);
    }

    if (m_timerCount != (int)m_timerMap.size()) {
        m_timerCount = m_timerMap.size();
        repairedCount++;
        Serial.printf("📊 Repaired timer count: %d\n", m_timerCount);
    }

    // 清理无效数据
    repairedCount += cleanupInvalidData();

    Serial.printf("📊 Data repair completed: %d items repaired\n", repairedCount);
    return repairedCount;
}

bool DataManager::checkDataConsistency() {
    return (m_signalCount == (int)m_signalMap.size()) &&
           (m_taskCount == (int)m_taskMap.size()) &&
           (m_timerCount == (int)m_timerMap.size());
}

bool DataManager::syncMapsWithStorage() {
    // 第二阶段简化实现：确保映射表和计数器同步
    m_signalCount = m_signalMap.size();
    m_taskCount = m_taskMap.size();
    m_timerCount = m_timerMap.size();

    Serial.println("📊 Maps synchronized with storage");
    return true;
}
