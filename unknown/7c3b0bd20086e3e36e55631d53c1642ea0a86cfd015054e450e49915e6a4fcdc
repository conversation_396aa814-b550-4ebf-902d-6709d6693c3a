#ifndef DATA_MANAGER_H
#define DATA_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include <vector>
#include <map>
#include "system-config.h"
#include "DataStructures.h"

/**
 * 数据管理器类
 *
 * 负责所有数据的管理、持久化和验证
 * 是系统的唯一数据权威
 *
 * 核心功能：
 * - 信号数据管理
 * - 任务数据管理
 * - 定时器数据管理
 * - 数据持久化
 * - 数据验证
 * - 统计信息
 */
class DataManager {
public:
    // ==================== 构造函数和析构函数 ====================

    /**
     * 构造函数
     * @param capacity 系统容量配置
     */
    explicit DataManager(const SystemCapacity& capacity);

    /**
     * 析构函数
     */
    ~DataManager();

    // ==================== 系统生命周期 ====================

    /**
     * 初始化数据管理器
     * @return bool 初始化是否成功
     */
    bool initialize();

    /**
     * 关闭数据管理器
     */
    void shutdown();

    /**
     * 工厂重置
     */
    void factoryReset();

    /**
     * 检查健康状态
     * @return bool 是否健康
     */
    bool isHealthy() const;

    // ==================== 基本数据获取 ====================

    /**
     * 获取信号数量
     * @return int 信号数量
     */
    int getSignalCount() const;

    /**
     * 获取任务数量
     * @return int 任务数量
     */
    int getTaskCount() const;

    /**
     * 获取定时器数量
     * @return int 定时器数量
     */
    int getTimerCount() const;

    // ==================== 信号数据管理 ====================

    /**
     * 获取所有信号
     * @return DynamicJsonDocument 信号列表
     */
    DynamicJsonDocument getAllSignals() const;

    /**
     * 根据ID获取信号
     * @param signalId 信号ID
     * @return DynamicJsonDocument 信号数据
     */
    DynamicJsonDocument getSignal(const String& signalId) const;

    /**
     * 创建信号
     * @param signalData 信号数据
     * @return String 新创建的信号ID
     */
    String createSignal(const DynamicJsonDocument& signalData);

    /**
     * 更新信号
     * @param signalId 信号ID
     * @param signalData 信号数据
     * @return bool 更新是否成功
     */
    bool updateSignal(const String& signalId, const DynamicJsonDocument& signalData);

    /**
     * 删除信号
     * @param signalId 信号ID
     * @return bool 删除是否成功
     */
    bool deleteSignal(const String& signalId);

    /**
     * 批量删除信号
     * @param signalIds 信号ID列表
     * @return int 成功删除的数量
     */
    int batchDeleteSignals(const std::vector<String>& signalIds);

    // ==================== 任务数据管理 ====================

    /**
     * 获取所有任务
     * @return DynamicJsonDocument 任务列表
     */
    DynamicJsonDocument getAllTasks() const;

    /**
     * 根据ID获取任务
     * @param taskId 任务ID
     * @return DynamicJsonDocument 任务数据
     */
    DynamicJsonDocument getTask(const String& taskId) const;

    /**
     * 创建任务
     * @param taskData 任务数据
     * @return String 新创建的任务ID
     */
    String createTask(const DynamicJsonDocument& taskData);

    /**
     * 更新任务
     * @param taskId 任务ID
     * @param taskData 任务数据
     * @return bool 更新是否成功
     */
    bool updateTask(const String& taskId, const DynamicJsonDocument& taskData);

    /**
     * 删除任务
     * @param taskId 任务ID
     * @return bool 删除是否成功
     */
    bool deleteTask(const String& taskId);

    // ==================== 定时器数据管理 ====================

    /**
     * 获取所有定时器
     * @return DynamicJsonDocument 定时器列表
     */
    DynamicJsonDocument getAllTimers() const;

    /**
     * 根据ID获取定时器
     * @param timerId 定时器ID
     * @return DynamicJsonDocument 定时器数据
     */
    DynamicJsonDocument getTimer(const String& timerId) const;

    /**
     * 创建定时器
     * @param timerData 定时器数据
     * @return String 新创建的定时器ID
     */
    String createTimer(const DynamicJsonDocument& timerData);

    /**
     * 更新定时器
     * @param timerId 定时器ID
     * @param timerData 定时器数据
     * @return bool 更新是否成功
     */
    bool updateTimer(const String& timerId, const DynamicJsonDocument& timerData);

    /**
     * 删除定时器
     * @param timerId 定时器ID
     * @return bool 删除是否成功
     */
    bool deleteTimer(const String& timerId);

    // ==================== 数据验证 ====================

    /**
     * 验证信号数据
     * @param signalData 信号数据
     * @return bool 验证是否通过
     */
    bool validateSignalData(const DynamicJsonDocument& signalData) const;

    /**
     * 验证任务数据
     * @param taskData 任务数据
     * @return bool 验证是否通过
     */
    bool validateTaskData(const DynamicJsonDocument& taskData) const;

    /**
     * 验证定时器数据
     * @param timerData 定时器数据
     * @return bool 验证是否通过
     */
    bool validateTimerData(const DynamicJsonDocument& timerData) const;

    // ==================== 统计信息 ====================

    /**
     * 获取统计信息
     * @return DynamicJsonDocument 统计信息
     */
    DynamicJsonDocument getStatistics() const;

    /**
     * 更新统计信息
     */
    void updateStatistics();

    // ==================== 数据导入导出 ====================

    /**
     * 导出所有数据
     * @return DynamicJsonDocument 导出的数据
     */
    DynamicJsonDocument exportAllData() const;

    /**
     * 导入数据
     * @param data 要导入的数据
     * @return bool 导入是否成功
     */
    bool importData(const DynamicJsonDocument& data);

    /**
     * 备份数据
     * @return bool 备份是否成功
     */
    bool backupData();

    /**
     * 恢复数据
     * @return bool 恢复是否成功
     */
    bool restoreData();

    // ==================== 高级数据管理 ====================

    /**
     * 获取信号数据指针（内部使用）
     * @param signalId 信号ID
     * @return SignalData* 信号数据指针
     */
    SignalData* getSignalPtr(const String& signalId);

    /**
     * 获取任务数据指针（内部使用）
     * @param taskId 任务ID
     * @return TaskData* 任务数据指针
     */
    TaskData* getTaskPtr(const String& taskId);

    /**
     * 获取定时器数据指针（内部使用）
     * @param timerId 定时器ID
     * @return TimerData* 定时器数据指针
     */
    TimerData* getTimerPtr(const String& timerId);

    /**
     * 自动保存数据（如果有变更）
     * @return bool 保存是否成功
     */
    bool autoSaveIfChanged();

    /**
     * 标记数据已变更
     * @param dataType 数据类型 ("signals", "tasks", "timers")
     */
    void markDataChanged(const String& dataType);

    /**
     * 清理无效数据
     * @return int 清理的数据数量
     */
    int cleanupInvalidData();

    /**
     * 优化数据存储
     * @return bool 优化是否成功
     */
    bool optimizeStorage();

    /**
     * 获取数据完整性报告
     * @return DynamicJsonDocument 完整性报告
     */
    DynamicJsonDocument getDataIntegrityReport();

private:
    // ==================== 私有成员变量 ====================

    SystemCapacity m_capacity;           // 系统容量配置
    bool m_initialized;                  // 是否已初始化

    // 数据存储（使用智能内存分配）
    void* m_signalStorage;              // 信号数据存储
    void* m_taskStorage;                // 任务数据存储
    void* m_timerStorage;               // 定时器数据存储

    // 数据映射表（用于快速查找）
    std::map<String, SignalData*> m_signalMap;    // 信号映射表
    std::map<String, TaskData*> m_taskMap;        // 任务映射表
    std::map<String, TimerData*> m_timerMap;      // 定时器映射表

    // 统计信息
    int m_signalCount;
    int m_taskCount;
    int m_timerCount;

    // 数据变更标记
    bool m_signalsChanged;              // 信号数据是否已变更
    bool m_tasksChanged;                // 任务数据是否已变更
    bool m_timersChanged;               // 定时器数据是否已变更
    unsigned long m_lastSaveTime;       // 最后保存时间

    // ==================== 私有方法 ====================

    /**
     * 初始化存储
     * @return bool 初始化是否成功
     */
    bool initializeStorage();

    /**
     * 清理存储
     */
    void cleanupStorage();

    /**
     * 加载数据从文件
     * @return bool 加载是否成功
     */
    bool loadDataFromFiles();

    /**
     * 保存数据到文件
     * @return bool 保存是否成功
     */
    bool saveDataToFiles();

    /**
     * 生成唯一ID
     * @param prefix ID前缀
     * @return String 唯一ID
     */
    String generateUniqueId(const String& prefix);

    /**
     * 记录错误
     * @param error 错误信息
     */
    void logError(const String& error);

    // ==================== 数据映射管理 ====================

    /**
     * 初始化数据映射表
     * @return bool 初始化是否成功
     */
    bool initializeDataMaps();

    /**
     * 清理数据映射表
     */
    void cleanupDataMaps();

    /**
     * 添加信号到映射表
     * @param signal 信号数据
     * @return bool 添加是否成功
     */
    bool addSignalToMap(SignalData* signal);

    /**
     * 从映射表移除信号
     * @param signalId 信号ID
     * @return bool 移除是否成功
     */
    bool removeSignalFromMap(const String& signalId);

    /**
     * 添加任务到映射表
     * @param task 任务数据
     * @return bool 添加是否成功
     */
    bool addTaskToMap(TaskData* task);

    /**
     * 从映射表移除任务
     * @param taskId 任务ID
     * @return bool 移除是否成功
     */
    bool removeTaskFromMap(const String& taskId);

    /**
     * 添加定时器到映射表
     * @param timer 定时器数据
     * @return bool 添加是否成功
     */
    bool addTimerToMap(TimerData* timer);

    /**
     * 从映射表移除定时器
     * @param timerId 定时器ID
     * @return bool 移除是否成功
     */
    bool removeTimerFromMap(const String& timerId);

    // ==================== 数据验证和修复 ====================

    /**
     * 验证数据完整性
     * @return bool 数据是否完整
     */
    bool validateDataIntegrity();

    /**
     * 修复损坏的数据
     * @return int 修复的数据数量
     */
    int repairCorruptedData();

    /**
     * 检查数据一致性
     * @return bool 数据是否一致
     */
    bool checkDataConsistency();

    /**
     * 同步映射表和存储
     * @return bool 同步是否成功
     */
    bool syncMapsWithStorage();
};

#endif // DATA_MANAGER_H
