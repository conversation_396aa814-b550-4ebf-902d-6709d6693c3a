#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

// ==================== 系统版本信息 ====================
#define SYSTEM_VERSION "2.0.0"
#define SYSTEM_NAME "ESP32-S3 IR Control System"
#define API_VERSION "1.0"

// ==================== 调试配置 ====================
#define DEBUG_MODE 1
#define SERIAL_BAUD_RATE 115200

// ==================== 系统模式定义 ====================
enum class SystemMode {
    HIGH_PERFORMANCE,  // PSRAM可用 - 高性能模式
    STANDARD          // 仅RAM - 标准模式
};

// ==================== 系统容量配置 ====================
struct SystemCapacity {
    int maxSignals;      // 最大信号数量
    int maxTasks;        // 最大任务数量
    int maxTimers;       // 最大定时器数量
    size_t bufferSize;   // 缓冲区大小
    int maxConnections;  // 最大WebSocket连接数
    
    // 根据系统模式获取容量配置
    static SystemCapacity getCapacity(SystemMode mode) {
        if (mode == SystemMode::HIGH_PERFORMANCE) {
            // 高性能模式 - PSRAM可用
            return {
                .maxSignals = 1000,
                .maxTasks = 100,
                .maxTimers = 50,
                .bufferSize = 8192,
                .maxConnections = 10
            };
        } else {
            // 标准模式 - 仅使用普通RAM
            return {
                .maxSignals = 100,
                .maxTasks = 20,
                .maxTimers = 10,
                .bufferSize = 2048,
                .maxConnections = 3
            };
        }
    }
};

// ==================== PSRAM配置 ====================
// 高性能模式配置（PSRAM可用）
#define MAX_SIGNALS_PSRAM 1000
#define MAX_TASKS_PSRAM 100
#define MAX_TIMERS_PSRAM 50
#define BUFFER_SIZE_PSRAM 8192
#define MAX_CONNECTIONS_PSRAM 10

// 标准模式配置（仅RAM）
#define MAX_SIGNALS_RAM 100
#define MAX_TASKS_RAM 20
#define MAX_TIMERS_RAM 10
#define BUFFER_SIZE_RAM 2048
#define MAX_CONNECTIONS_RAM 3

// ==================== 内存管理配置 ====================
#define MIN_FREE_HEAP 20480          // 最小空闲堆内存 (20KB)
#define MIN_FREE_PSRAM 102400        // 最小空闲PSRAM (100KB)
#define MEMORY_CHECK_INTERVAL 60000  // 内存检查间隔 (1分钟)
#define AUTO_SAVE_INTERVAL 300000    // 自动保存间隔 (5分钟)

// ==================== 网络配置 ====================
#define DEFAULT_WEB_PORT 80
#define DEFAULT_WEBSOCKET_PATH "/ws"
#define MAX_REQUEST_SIZE 4096
#define CONNECTION_TIMEOUT 30000     // 连接超时 (30秒)
#define WEBSOCKET_PING_INTERVAL 30000 // WebSocket ping间隔

// ==================== 文件系统配置 ====================
#define SIGNALS_FILE_PATH "/signals.json"
#define TASKS_FILE_PATH "/tasks.json"
#define TIMERS_FILE_PATH "/timers.json"
#define CONFIG_FILE_PATH "/config.json"
#define BACKUP_FILE_PATH "/backup.json"

// ==================== 红外配置 ====================
#define DEFAULT_IR_FREQUENCY 38000
#define IR_LEARN_TIMEOUT 10000       // 学习超时 (10秒)
#define IR_SEND_RETRY_COUNT 3        // 发送重试次数
#define IR_SIGNAL_INTERVAL 100       // 信号间隔 (毫秒)

// ==================== 任务配置 ====================
#define MAX_TASK_EXECUTION_TIME 300000  // 最大任务执行时间 (5分钟)
#define TASK_QUEUE_SIZE 50              // 任务队列大小
#define SCHEDULER_CHECK_INTERVAL 60000  // 调度器检查间隔 (1分钟)

// ==================== 安全配置 ====================
#define ENABLE_API_KEY_AUTH false       // 启用API密钥认证
#define ENABLE_RATE_LIMITING true       // 启用速率限制
#define MAX_REQUESTS_PER_MINUTE 100     // 每分钟最大请求数
#define SESSION_TIMEOUT 3600000         // 会话超时 (1小时)

// ==================== 日志配置 ====================
#define LOG_LEVEL_DEBUG 0
#define LOG_LEVEL_INFO 1
#define LOG_LEVEL_WARNING 2
#define LOG_LEVEL_ERROR 3
#define CURRENT_LOG_LEVEL LOG_LEVEL_INFO

// ==================== 系统限制 ====================
#define MAX_SIGNAL_NAME_LENGTH 64
#define MAX_SIGNAL_CODE_LENGTH 256
#define MAX_TASK_NAME_LENGTH 64
#define MAX_TIMER_NAME_LENGTH 64
#define MAX_DESCRIPTION_LENGTH 256

// ==================== 性能配置 ====================
#define ENABLE_PERFORMANCE_MONITORING true
#define PERFORMANCE_SAMPLE_INTERVAL 10000  // 性能采样间隔 (10秒)
#define MAX_PERFORMANCE_SAMPLES 100        // 最大性能样本数

// ==================== 错误处理配置 ====================
#define MAX_ERROR_HISTORY 50            // 最大错误历史记录数
#define ERROR_RECOVERY_TIMEOUT 5000     // 错误恢复超时 (5秒)
#define CRITICAL_ERROR_RESTART true     // 严重错误时重启

// ==================== 功能开关 ====================
#define ENABLE_WEBSOCKET true           // 启用WebSocket
#define ENABLE_REST_API true            // 启用REST API
#define ENABLE_SIGNAL_LEARNING true     // 启用信号学习
#define ENABLE_TASK_SCHEDULING true     // 启用任务调度
#define ENABLE_DATA_BACKUP true         // 启用数据备份
#define ENABLE_SYSTEM_DIAGNOSTICS true  // 启用系统诊断

// ==================== 硬件特性检测 ====================
#define AUTO_DETECT_PSRAM true          // 自动检测PSRAM
#define AUTO_DETECT_FLASH_SIZE true     // 自动检测Flash大小
#define ENABLE_HARDWARE_WATCHDOG true   // 启用硬件看门狗

// ==================== 开发调试配置 ====================
#ifdef DEBUG_MODE
    #define DEBUG_MEMORY_USAGE true     // 调试内存使用
    #define DEBUG_NETWORK_TRAFFIC true  // 调试网络流量
    #define DEBUG_IR_SIGNALS true       // 调试红外信号
    #define DEBUG_TASK_EXECUTION true   // 调试任务执行
    #define ENABLE_SERIAL_COMMANDS true // 启用串口命令
#else
    #define DEBUG_MEMORY_USAGE false
    #define DEBUG_NETWORK_TRAFFIC false
    #define DEBUG_IR_SIGNALS false
    #define DEBUG_TASK_EXECUTION false
    #define ENABLE_SERIAL_COMMANDS false
#endif

// ==================== 编译时配置验证 ====================
#if MAX_SIGNALS_RAM > 200
    #error "MAX_SIGNALS_RAM too large for standard mode"
#endif

#if BUFFER_SIZE_RAM > 4096
    #error "BUFFER_SIZE_RAM too large for standard mode"
#endif

#if MAX_CONNECTIONS_RAM > 5
    #error "MAX_CONNECTIONS_RAM too large for standard mode"
#endif

// ==================== 系统状态定义 ====================
enum class SystemStatus {
    INITIALIZING,    // 初始化中
    READY,          // 就绪
    RUNNING,        // 运行中
    ERROR,          // 错误状态
    MAINTENANCE,    // 维护模式
    SHUTDOWN        // 关闭中
};

// ==================== 辅助宏定义 ====================
#define STRINGIFY(x) #x
#define TOSTRING(x) STRINGIFY(x)

#define SYSTEM_INFO_STRING \
    SYSTEM_NAME " v" SYSTEM_VERSION " (API v" API_VERSION ")"

#define BUILD_INFO_STRING \
    "Built on " __DATE__ " at " __TIME__

// ==================== 内存分配策略 ====================
enum class MemoryStrategy {
    PREFER_PSRAM,    // 优先使用PSRAM
    PREFER_HEAP,     // 优先使用堆内存
    BALANCED         // 平衡使用
};

// ==================== 全局配置获取函数 ====================
inline SystemCapacity getSystemCapacity(SystemMode mode) {
    return SystemCapacity::getCapacity(mode);
}

inline const char* getSystemModeString(SystemMode mode) {
    switch (mode) {
        case SystemMode::HIGH_PERFORMANCE:
            return "High Performance (PSRAM)";
        case SystemMode::STANDARD:
            return "Standard (RAM Only)";
        default:
            return "Unknown";
    }
}

inline const char* getSystemStatusString(SystemStatus status) {
    switch (status) {
        case SystemStatus::INITIALIZING:
            return "Initializing";
        case SystemStatus::READY:
            return "Ready";
        case SystemStatus::RUNNING:
            return "Running";
        case SystemStatus::ERROR:
            return "Error";
        case SystemStatus::MAINTENANCE:
            return "Maintenance";
        case SystemStatus::SHUTDOWN:
            return "Shutdown";
        default:
            return "Unknown";
    }
}

#endif // SYSTEM_CONFIG_H
