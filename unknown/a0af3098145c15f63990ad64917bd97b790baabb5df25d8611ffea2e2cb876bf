#include "WSManager.h"
#include "DataManager.h"
#include "IRController.h"
#include "TaskManager.h"

// ==================== 构造函数和析构函数 ====================
WSManager::WSManager()
    : m_webSocket(nullptr)
    , m_initialized(false)
    , m_debugMode(false)
    , m_dataManager(nullptr)
    , m_irController(nullptr)
    , m_taskManager(nullptr)
    , m_totalConnections(0)
    , m_totalMessages(0)
    , m_totalBroadcasts(0)
    , m_startTime(0)
{
    Serial.println("🔌 WSManager created");
}

WSManager::~WSManager() {
    shutdown();
    Serial.println("🔌 WSManager destroyed");
}

// ==================== 系统生命周期 ====================
bool WSManager::initialize(AsyncWebServer* server) {
    if (m_initialized) {
        Serial.println("⚠️ WSManager already initialized");
        return true;
    }
    
    if (!server) {
        Serial.println("❌ Server instance required");
        return false;
    }
    
    Serial.println("🔌 Initializing WSManager...");
    
    // 创建WebSocket实例
    m_webSocket = new AsyncWebSocket("/ws");
    if (!m_webSocket) {
        Serial.println("❌ Failed to create WebSocket instance");
        return false;
    }
    
    // 设置事件处理器
    m_webSocket->onEvent([this](AsyncWebSocket* server, AsyncWebSocketClient* client, 
                               AwsEventType type, void* arg, uint8_t* data, size_t len) {
        handleWebSocketEvent(server, client, type, arg, data, len);
    });
    
    // 添加到服务器
    server->addHandler(m_webSocket);
    
    m_initialized = true;
    m_startTime = millis();
    
    Serial.println("✅ WSManager initialization completed");
    return true;
}

void WSManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("🔌 Shutting down WSManager...");
    
    // 断开所有客户端
    disconnectAllClients();
    
    // 清理WebSocket
    if (m_webSocket) {
        delete m_webSocket;
        m_webSocket = nullptr;
    }
    
    m_initialized = false;
    Serial.println("✅ WSManager shutdown completed");
}

bool WSManager::isHealthy() const {
    return m_initialized && m_webSocket;
}

void WSManager::setDependencies(DataManager* dataManager, IRController* irController, TaskManager* taskManager) {
    m_dataManager = dataManager;
    m_irController = irController;
    m_taskManager = taskManager;
    
    Serial.println("🔌 WSManager dependencies set");
}

// ==================== 连接管理 ====================
int WSManager::getConnectionCount() const {
    return m_webSocket ? m_webSocket->count() : 0;
}

DynamicJsonDocument WSManager::getConnectionInfo() const {
    DynamicJsonDocument info(512);
    
    info["connection_count"] = getConnectionCount();
    info["total_connections"] = m_totalConnections;
    info["total_messages"] = m_totalMessages;
    info["total_broadcasts"] = m_totalBroadcasts;
    info["uptime"] = millis() - m_startTime;
    
    JsonArray clients = info.createNestedArray("clients");
    
    for (const ClientInfo& clientInfo : m_clients) {
        JsonObject client = clients.createNestedObject();
        client["id"] = clientInfo.id;
        client["ip"] = clientInfo.ipAddress;
        client["connect_time"] = clientInfo.connectTime;
        client["last_ping"] = clientInfo.lastPingTime;
        client["subscribed"] = clientInfo.isSubscribed;
    }
    
    return info;
}

bool WSManager::disconnectClient(uint32_t clientId) {
    if (!m_webSocket) {
        return false;
    }
    
    AsyncWebSocketClient* client = m_webSocket->client(clientId);
    if (client) {
        client->close();
        removeClientInfo(clientId);
        debugLog("Client disconnected: " + String(clientId));
        return true;
    }
    
    return false;
}

void WSManager::disconnectAllClients() {
    if (!m_webSocket) {
        return;
    }
    
    m_webSocket->closeAll();
    m_clients.clear();
    
    debugLog("All clients disconnected");
}

// ==================== 消息发送 ====================
void WSManager::broadcastMessage(const DynamicJsonDocument& message) {
    if (!m_webSocket) {
        return;
    }
    
    String messageStr;
    serializeJson(message, messageStr);
    
    m_webSocket->textAll(messageStr);
    m_totalBroadcasts++;
    
    debugLog("Broadcast message: " + messageStr);
}

bool WSManager::sendMessage(uint32_t clientId, const DynamicJsonDocument& message) {
    if (!m_webSocket) {
        return false;
    }
    
    AsyncWebSocketClient* client = m_webSocket->client(clientId);
    if (!client) {
        return false;
    }
    
    String messageStr;
    serializeJson(message, messageStr);
    
    client->text(messageStr);
    m_totalMessages++;
    
    debugLog("Message sent to " + String(clientId) + ": " + messageStr);
    return true;
}

bool WSManager::sendTextMessage(uint32_t clientId, const String& text) {
    if (!m_webSocket) {
        return false;
    }
    
    AsyncWebSocketClient* client = m_webSocket->client(clientId);
    if (!client) {
        return false;
    }
    
    client->text(text);
    m_totalMessages++;
    
    debugLog("Text message sent to " + String(clientId) + ": " + text);
    return true;
}

void WSManager::broadcastTextMessage(const String& text) {
    if (!m_webSocket) {
        return;
    }
    
    m_webSocket->textAll(text);
    m_totalBroadcasts++;
    
    debugLog("Broadcast text: " + text);
}

// ==================== 状态同步 ====================
void WSManager::syncSystemStatus() {
    DynamicJsonDocument status(512);
    status["type"] = "system_status";
    status["timestamp"] = millis();
    status["free_heap"] = ESP.getFreeHeap();
    
    if (m_dataManager) {
        status["signal_count"] = m_dataManager->getSignalCount();
        status["task_count"] = m_dataManager->getTaskCount();
    }
    
    if (m_taskManager) {
        status["is_executing"] = m_taskManager->isExecuting();
    }
    
    broadcastMessage(status);
}

void WSManager::syncSignalList() {
    if (!m_dataManager) {
        return;
    }
    
    DynamicJsonDocument message(1024);
    message["type"] = "signal_list";
    message["timestamp"] = millis();
    message["signals"] = m_dataManager->getAllSignals();
    
    broadcastMessage(message);
}

void WSManager::syncTaskStatus() {
    if (!m_taskManager) {
        return;
    }
    
    DynamicJsonDocument message(512);
    message["type"] = "task_status";
    message["timestamp"] = millis();
    message["status"] = m_taskManager->getAllTasksStatus();
    
    broadcastMessage(message);
}

void WSManager::syncExecutionProgress(const DynamicJsonDocument& progress) {
    DynamicJsonDocument message(512);
    message["type"] = "execution_progress";
    message["timestamp"] = millis();
    message["progress"] = progress;
    
    broadcastMessage(message);
}

// ==================== 事件处理 ====================
void WSManager::handleWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                                    AwsEventType type, void* arg, uint8_t* data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT:
            handleClientConnect(client);
            break;
            
        case WS_EVT_DISCONNECT:
            handleClientDisconnect(client);
            break;
            
        case WS_EVT_DATA:
            handleClientMessage(client, data, len);
            break;
            
        case WS_EVT_PONG:
        case WS_EVT_ERROR:
            // 处理pong和错误事件
            break;
    }
}

void WSManager::handleClientConnect(AsyncWebSocketClient* client) {
    m_totalConnections++;
    addClientInfo(client);
    
    debugLog("Client connected: " + String(client->id()) + " from " + client->remoteIP().toString());
    
    // 发送欢迎消息
    DynamicJsonDocument welcome = createResponse("welcome", true, "Connected to IR System");
    sendMessage(client->id(), welcome);
    
    // 同步当前状态
    syncSystemStatus();
}

void WSManager::handleClientDisconnect(AsyncWebSocketClient* client) {
    removeClientInfo(client->id());
    
    debugLog("Client disconnected: " + String(client->id()));
}

void WSManager::handleClientMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len) {
    String messageStr = String((char*)data, len);
    
    debugLog("Message from " + String(client->id()) + ": " + messageStr);
    
    DynamicJsonDocument message(512);
    DeserializationError error = deserializeJson(message, messageStr);
    
    if (error) {
        DynamicJsonDocument errorResponse = createResponse("error", false, "Invalid JSON format");
        sendMessage(client->id(), errorResponse);
        return;
    }
    
    if (!validateMessageFormat(message)) {
        DynamicJsonDocument errorResponse = createResponse("error", false, "Invalid message format");
        sendMessage(client->id(), errorResponse);
        return;
    }
    
    String messageType = message["type"];
    
    // 路由消息到相应的处理器
    if (messageType == "ping") {
        handlePingMessage(client, message);
    } else if (messageType == "get_status") {
        handleGetStatusMessage(client, message);
    } else if (messageType == "execute_signal") {
        handleExecuteSignalMessage(client, message);
    } else if (messageType == "stop_execution") {
        handleStopExecutionMessage(client, message);
    } else if (messageType == "learn_signal") {
        handleLearnSignalMessage(client, message);
    } else if (messageType == "subscribe") {
        handleSubscribeMessage(client, message);
    } else {
        handleUnknownMessage(client, message);
    }
    
    m_totalMessages++;
}

// ==================== 消息处理 ====================
void WSManager::handlePingMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    DynamicJsonDocument pong = createResponse("pong", true, "Pong");
    pong["timestamp"] = millis();
    sendMessage(client->id(), pong);

    // 更新客户端ping时间
    ClientInfo* clientInfo = findClientInfo(client->id());
    if (clientInfo) {
        clientInfo->lastPingTime = millis();
    }
}

void WSManager::handleGetStatusMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    syncSystemStatus();
}

void WSManager::handleExecuteSignalMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    if (!message.containsKey("signal_id")) {
        DynamicJsonDocument error = createResponse("error", false, "Missing signal_id");
        sendMessage(client->id(), error);
        return;
    }

    String signalId = message["signal_id"];

    if (m_taskManager) {
        bool success = m_taskManager->executeSingleSignal(signalId);
        DynamicJsonDocument response = createResponse("execute_result", success,
                                                     success ? "Signal executed" : "Execution failed");
        sendMessage(client->id(), response);
    } else {
        DynamicJsonDocument error = createResponse("error", false, "TaskManager not available");
        sendMessage(client->id(), error);
    }
}

void WSManager::handleStopExecutionMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    if (m_taskManager) {
        m_taskManager->stopExecution();
        DynamicJsonDocument response = createResponse("stop_result", true, "Execution stopped");
        sendMessage(client->id(), response);
    } else {
        DynamicJsonDocument error = createResponse("error", false, "TaskManager not available");
        sendMessage(client->id(), error);
    }
}

void WSManager::handleLearnSignalMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    if (!m_irController) {
        DynamicJsonDocument error = createResponse("error", false, "IRController not available");
        sendMessage(client->id(), error);
        return;
    }

    unsigned long timeout = message["timeout"] | 10000;
    bool success = m_irController->startLearning(timeout);

    DynamicJsonDocument response = createResponse("learn_result", success,
                                                 success ? "Learning started" : "Failed to start learning");
    sendMessage(client->id(), response);
}

void WSManager::handleSubscribeMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    String subscriptionType = message["subscription_type"] | "all";

    ClientInfo* clientInfo = findClientInfo(client->id());
    if (clientInfo) {
        clientInfo->isSubscribed = true;
        clientInfo->subscriptionType = subscriptionType;

        DynamicJsonDocument response = createResponse("subscribe_result", true, "Subscribed to " + subscriptionType);
        sendMessage(client->id(), response);
    }
}

// ==================== 统计和监控 ====================
DynamicJsonDocument WSManager::getStatistics() const {
    DynamicJsonDocument stats(512);

    stats["connection_count"] = getConnectionCount();
    stats["total_connections"] = m_totalConnections;
    stats["total_messages"] = m_totalMessages;
    stats["total_broadcasts"] = m_totalBroadcasts;
    stats["uptime"] = millis() - m_startTime;
    stats["client_count"] = m_clients.size();

    return stats;
}

void WSManager::resetStatistics() {
    m_totalConnections = 0;
    m_totalMessages = 0;
    m_totalBroadcasts = 0;
    m_startTime = millis();

    debugLog("Statistics reset");
}

void WSManager::cleanupExpiredConnections() {
    unsigned long currentTime = millis();

    auto it = m_clients.begin();
    while (it != m_clients.end()) {
        // 清理超过5分钟没有ping的连接
        if (currentTime - it->lastPingTime > 300000) {
            debugLog("Removing expired client: " + String(it->id));
            it = m_clients.erase(it);
        } else {
            ++it;
        }
    }
}

// ==================== 调试功能 ====================
void WSManager::enableDebugMode(bool enable) {
    m_debugMode = enable;
    Serial.printf("🔌 WebSocket debug mode %s\n", enable ? "enabled" : "disabled");
}

String WSManager::getDebugInfo() const {
    String info = "WSManager Debug Info:\n";
    info += "  Initialized: " + String(m_initialized ? "Yes" : "No") + "\n";
    info += "  Connection Count: " + String(getConnectionCount()) + "\n";
    info += "  Total Connections: " + String(m_totalConnections) + "\n";
    info += "  Total Messages: " + String(m_totalMessages) + "\n";
    info += "  Total Broadcasts: " + String(m_totalBroadcasts) + "\n";

    return info;
}

// ==================== 私有方法实现 ====================
DynamicJsonDocument WSManager::createResponse(const String& type, bool success,
                                             const String& message, const DynamicJsonDocument& data) {
    DynamicJsonDocument response(512);
    response["type"] = type;
    response["success"] = success;
    response["message"] = message;
    response["timestamp"] = millis();

    if (!data.isNull()) {
        response["data"] = data;
    }

    return response;
}

bool WSManager::validateMessageFormat(const DynamicJsonDocument& message) {
    return message.containsKey("type");
}

WSManager::ClientInfo* WSManager::findClientInfo(uint32_t clientId) {
    for (ClientInfo& client : m_clients) {
        if (client.id == clientId) {
            return &client;
        }
    }
    return nullptr;
}

void WSManager::addClientInfo(AsyncWebSocketClient* client) {
    ClientInfo clientInfo;
    clientInfo.id = client->id();
    clientInfo.ipAddress = client->remoteIP().toString();
    clientInfo.connectTime = millis();
    clientInfo.lastPingTime = millis();
    clientInfo.isSubscribed = false;
    clientInfo.subscriptionType = "";

    m_clients.push_back(clientInfo);
}

void WSManager::removeClientInfo(uint32_t clientId) {
    auto it = std::find_if(m_clients.begin(), m_clients.end(),
                          [clientId](const ClientInfo& client) {
                              return client.id == clientId;
                          });

    if (it != m_clients.end()) {
        m_clients.erase(it);
    }
}

void WSManager::debugLog(const String& message) {
    if (m_debugMode) {
        Serial.printf("🔌 WS DEBUG: %s\n", message.c_str());
    }
}

void WSManager::handleUnknownMessage(AsyncWebSocketClient* client, const DynamicJsonDocument& message) {
    DynamicJsonDocument error = createResponse("error", false, "Unknown message type");
    sendMessage(client->id(), error);
}
