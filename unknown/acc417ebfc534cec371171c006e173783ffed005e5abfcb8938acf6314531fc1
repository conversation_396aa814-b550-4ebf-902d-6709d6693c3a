/**
 * Web服务器管理器 - 实现文件
 * 负责HTTP API路由、请求处理、响应格式化
 */

#include "WebServerManager.h"
#include <SPIFFS.h>

// CORS配置常量
const char* WebServerManager::CORS_HEADERS = "Content-Type, Authorization, X-Requested-With";
const char* WebServerManager::CORS_METHODS = "GET, POST, PUT, DELETE, OPTIONS";
const char* WebServerManager::CORS_ORIGIN = "*";

WebServerManager::WebServerManager(AsyncWebServer* server, SignalManager* signalManager, IRController* irController)
    : server(server), signalManager(signalManager), irController(irController),
      requestCount(0), errorCount(0), initialized(false), debugEnabled(true) {
}

WebServerManager::~WebServerManager() {
    if (server) {
        server->end();
    }
}

bool WebServerManager::init() {
    if (!server || !signalManager || !irController) {
        Serial.println("❌ WebServerManager: 依赖对象未初始化");
        return false;
    }
    
    Serial.println("🌐 初始化Web服务器管理器...");
    
    // 设置CORS
    setupCORS();
    
    // 设置静态文件服务
    setupStaticFiles();
    
    // 设置API路由
    setupRoutes();
    
    // 设置默认处理器
    server->onNotFound([this](AsyncWebServerRequest* request) {
        handleNotFound(request);
    });
    
    initialized = true;
    Serial.println("✅ Web服务器管理器初始化完成");
    return true;
}

void WebServerManager::setupCORS() {
    // 处理OPTIONS预检请求
    server->on("/*", HTTP_OPTIONS, [this](AsyncWebServerRequest* request) {
        handleCORS(request);
    });
}

void WebServerManager::setupStaticFiles() {
    // 服务静态文件
    server->serveStatic("/", SPIFFS, "/").setDefaultFile("index.html");
    
    // 设置缓存头
    server->serveStatic("/js/", SPIFFS, "/js/").setCacheControl("max-age=86400");
    server->serveStatic("/css/", SPIFFS, "/css/").setCacheControl("max-age=86400");
    server->serveStatic("/images/", SPIFFS, "/images/").setCacheControl("max-age=86400");
}

void WebServerManager::setupRoutes() {
    Serial.println("🛣️ 设置API路由...");

    // 信号管理API
    server->on("/api/signals", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSignals(request);
    });

    server->on("/api/signals/delete", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleDeleteSignal(request);
    });

    server->on("/api/signals/batch-delete", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleBatchDeleteSignals(request);
    });

    server->on("/api/signals/update", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleUpdateSignal(request);
    });

    server->on("/api/signals/send", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSendSignal(request);
    });

    // 学习功能API
    server->on("/api/signals/learn/start", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleStartLearning(request);
    });

    server->on("/api/signals/learn/stop", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleStopLearning(request);
    });

    server->on("/api/signals/learn/save", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSaveLearning(request);
    });
    
    // 导入功能API
    server->on("/api/signals/import", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleImportSignals(request);
    });
    
    server->on("/api/signals/import/text", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleImportText(request);
    });
    
    server->on("/api/signals/import/execute", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleExecuteImport(request);
    });
    
    server->on("/api/signals/import/text/execute", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleExecuteTextImport(request);
    });
    
    // 系统管理API
    server->on("/api/system/logs", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemLogs(request);
    });
    
    server->on("/api/system/logs", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSaveSystemLogs(request);
    });
    
    server->on("/api/system/error-log", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleErrorLog(request);
    });
    
    server->on("/api/system/stats", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemStats(request);
    });
    
    // 控制管理API
    server->on("/api/control/history", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetControlHistory(request);
    });
    
    server->on("/api/control/history", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSaveControlHistory(request);
    });
    
    Serial.println("✅ API路由设置完成");
}

void WebServerManager::handleCORS(AsyncWebServerRequest* request) {
    AsyncWebServerResponse* response = request->beginResponse(200, "text/plain", "OK");
    addCORSHeaders(response);
    request->send(response);
}

void WebServerManager::handleNotFound(AsyncWebServerRequest* request) {
    logRequest(request);

    // 如果是API请求，返回JSON错误
    if (request->url().startsWith("/api/")) {
        sendErrorResponse(request, "API端点不存在", 404);
        return;
    }

    // 对于其他请求，尝试返回index.html（SPA路由）
    if (SPIFFS.exists("/index.html")) {
        request->send(SPIFFS, "/index.html", "text/html");
    } else {
        request->send(404, "text/plain", "页面不存在");
    }
}

void WebServerManager::handleFileRequest(AsyncWebServerRequest* request) {
    logRequest(request);

    String path = request->url();

    // 默认首页
    if (path == "/") {
        path = "/index.html";
    }

    // 安全检查：防止目录遍历攻击
    if (path.indexOf("..") != -1) {
        sendErrorResponse(request, "非法路径", 403);
        return;
    }

    // 检查文件是否存在
    if (!SPIFFS.exists(path)) {
        handleNotFound(request);
        return;
    }

    // 获取文件MIME类型
    String contentType = "text/plain";
    if (path.endsWith(".html")) contentType = "text/html";
    else if (path.endsWith(".css")) contentType = "text/css";
    else if (path.endsWith(".js")) contentType = "application/javascript";
    else if (path.endsWith(".json")) contentType = "application/json";
    else if (path.endsWith(".png")) contentType = "image/png";
    else if (path.endsWith(".jpg") || path.endsWith(".jpeg")) contentType = "image/jpeg";
    else if (path.endsWith(".gif")) contentType = "image/gif";
    else if (path.endsWith(".ico")) contentType = "image/x-icon";

    // 发送文件
    AsyncWebServerResponse* response = request->beginResponse(SPIFFS, path, contentType);
    addCORSHeaders(response);
    request->send(response);

    if (debugEnabled) {
        Serial.printf("📄 文件请求: %s (%s)\n", path.c_str(), contentType.c_str());
    }
}

void WebServerManager::handleGetSignals(AsyncWebServerRequest* request) {
    logRequest(request);
    
    APIResponse response = signalManager->getAllSignals();
    sendJsonResponse(request, response);
}

void WebServerManager::handleDeleteSignal(AsyncWebServerRequest* request) {
    logRequest(request);

    // 修复：从表单参数获取signalId
    if (!request->hasParam("signalId", true)) {
        sendErrorResponse(request, "缺少必需参数: signalId", 400);
        return;
    }

    String signalId = request->getParam("signalId", true)->value();
    if (!validateSignalId(signalId)) {
        sendErrorResponse(request, "无效的信号ID格式", 400);
        return;
    }
    
    APIResponse response = signalManager->deleteSignal(signalId);
    sendJsonResponse(request, response);
}

void WebServerManager::handleSendSignal(AsyncWebServerRequest* request) {
    logRequest(request);

    // 修复：从表单参数获取signalId
    if (!request->hasParam("signalId", true)) {
        sendErrorResponse(request, "缺少必需参数: signalId", 400);
        return;
    }

    String signalId = request->getParam("signalId", true)->value();
    
    // 获取信号数据
    APIResponse getResponse = signalManager->getSignal(signalId);
    if (!getResponse.success) {
        sendJsonResponse(request, getResponse);
        return;
    }
    
    // 提取信号信息
    JsonObject signalData = getResponse.data.as<JsonObject>();
    String protocol = signalData["protocol"];
    String signalCode = signalData["signalCode"];
    String frequency = signalData["frequency"];
    String rawData = signalData["rawData"];
    
    // 发射信号
    bool success = false;
    if (protocol == "RAW") {
        success = irController->sendRawData(rawData, frequency.toInt());
    } else {
        success = irController->sendSignal(protocol, signalCode, frequency);
    }
    
    if (success) {
        // 更新发射统计
        signalManager->updateSentStatistics(signalId);
        
        JsonDocument responseData;
        JsonObject result = responseData.createNestedObject();
        result["signalId"] = signalId;
        result["message"] = "信号发射成功";
        result["timestamp"] = millis();
        
        sendSuccessResponse(request, responseData);
    } else {
        sendErrorResponse(request, "信号发射失败", 500);
    }
}

void WebServerManager::handleStartLearning(AsyncWebServerRequest* request) {
    logRequest(request);
    
    JsonDocument doc;
    if (!parseRequestBody(request, doc)) {
        sendErrorResponse(request, "无效的JSON请求", 400);
        return;
    }
    
    JsonObject obj = doc.as<JsonObject>();
    unsigned long timeout = obj["timeout"] | 10000; // 默认10秒超时
    
    // 设置学习超时
    irController->setLearningTimeout(timeout);
    
    // 开始学习
    String learningId = "learning_" + String(millis());
    bool success = irController->startLearning(learningId);
    
    if (success) {
        JsonDocument responseData;
        JsonObject result = responseData.createNestedObject();
        result["learningId"] = learningId;
        result["timeout"] = timeout;
        result["message"] = "学习模式已启动";
        
        sendSuccessResponse(request, responseData);
    } else {
        sendErrorResponse(request, "学习模式启动失败", 500);
    }
}

void WebServerManager::sendJsonResponse(AsyncWebServerRequest* request, const APIResponse& response) {
    AsyncWebServerResponse* webResponse = request->beginResponse(
        response.success ? 200 : 400,
        "application/json",
        response.toJsonString()
    );
    
    addCORSHeaders(webResponse);
    request->send(webResponse);
    
    requestCount++;
    if (!response.success) {
        errorCount++;
    }
}

void WebServerManager::sendErrorResponse(AsyncWebServerRequest* request, const String& error, int code) {
    APIResponse response = APIResponse::createError(error, code);
    sendJsonResponse(request, response);
}

void WebServerManager::sendSuccessResponse(AsyncWebServerRequest* request, const JsonDocument& data) {
    APIResponse response = APIResponse::createSuccess(data);
    sendJsonResponse(request, response);
}

void WebServerManager::addCORSHeaders(AsyncWebServerResponse* response) {
    response->addHeader("Access-Control-Allow-Origin", CORS_ORIGIN);
    response->addHeader("Access-Control-Allow-Methods", CORS_METHODS);
    response->addHeader("Access-Control-Allow-Headers", CORS_HEADERS);
    response->addHeader("Access-Control-Max-Age", "86400");
}

bool WebServerManager::validateSignalId(const String& id) {
    return id.startsWith("signal_") && id.length() == 15;
}

bool WebServerManager::validateJsonRequest(AsyncWebServerRequest* request, JsonDocument& doc) {
    return parseRequestBody(request, doc) && !doc.isNull();
}

bool WebServerManager::validateRequiredFields(const JsonObject& obj, const std::vector<String>& fields) {
    for (const String& field : fields) {
        if (!obj.containsKey(field) || obj[field].isNull()) {
            return false;
        }
    }
    return true;
}

void WebServerManager::logRequest(AsyncWebServerRequest* request) {
    if (debugEnabled) {
        Serial.printf("🌐 %s %s from %s\n",
                     request->methodToString(),
                     request->url().c_str(),
                     request->client()->remoteIP().toString().c_str());
    }
}

void WebServerManager::logError(const String& error, const String& context) {
    String logMessage = "❌ WebServerManager";
    if (!context.isEmpty()) {
        logMessage += " [" + context + "]";
    }
    logMessage += ": " + error;

    Serial.println(logMessage);

    // 可以在这里添加错误日志到文件系统的逻辑
    errorCount++;
}

bool WebServerManager::parseRequestBody(AsyncWebServerRequest* request, JsonDocument& doc) {
    // 尝试从URL参数中获取JSON数据（兼容前端发送方式）
    if (request->hasParam("data", true)) {
        String jsonData = request->getParam("data", true)->value();
        DeserializationError error = deserializeJson(doc, jsonData);
        if (error) {
            Serial.printf("❌ JSON解析失败: %s\n", error.c_str());
            return false;
        }
        return true;
    }

    // 如果没有data参数，尝试从其他参数构建JSON
    JsonObject obj = doc.to<JsonObject>();
    bool hasData = false;

    // 遍历所有POST参数
    for (int i = 0; i < request->params(); i++) {
        AsyncWebParameter* param = request->getParam(i);
        if (param->isPost()) {
            obj[param->name()] = param->value();
            hasData = true;
        }
    }

    return hasData;
}

void WebServerManager::handleBatchDeleteSignals(AsyncWebServerRequest* request) {
    logRequest(request);

    // 由于AsyncWebServer的body处理限制，我们需要通过参数获取数据
    if (!request->hasParam("signalIds", true)) {
        sendErrorResponse(request, "缺少必需参数: signalIds", 400);
        return;
    }

    String signalIdsStr = request->getParam("signalIds", true)->value();

    // 解析信号ID列表
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, signalIdsStr);
    if (error) {
        sendErrorResponse(request, "无效的JSON格式", 400);
        return;
    }

    std::vector<String> signalIds;
    JsonArray idsArray = doc.as<JsonArray>();
    for (JsonVariant id : idsArray) {
        String signalId = id.as<String>();
        if (validateSignalId(signalId)) {
            signalIds.push_back(signalId);
        }
    }

    if (signalIds.empty()) {
        sendErrorResponse(request, "没有有效的信号ID", 400);
        return;
    }

    APIResponse response = signalManager->batchDeleteSignals(signalIds);
    sendJsonResponse(request, response);
}

void WebServerManager::handleUpdateSignal(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!request->hasParam("signalId", true) || !request->hasParam("signalData", true)) {
        sendErrorResponse(request, "缺少必需参数", 400);
        return;
    }

    String signalId = request->getParam("signalId", true)->value();
    String signalDataStr = request->getParam("signalData", true)->value();

    if (!validateSignalId(signalId)) {
        sendErrorResponse(request, "无效的信号ID格式", 400);
        return;
    }

    // 解析信号数据
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, signalDataStr);
    if (error) {
        sendErrorResponse(request, "无效的信号数据格式", 400);
        return;
    }

    JsonObject signalObj = doc.as<JsonObject>();
    Signal updatedSignal = Signal::fromJson(signalObj);

    APIResponse response = signalManager->updateSignal(signalId, updatedSignal);
    sendJsonResponse(request, response);
}

void WebServerManager::handleStopLearning(AsyncWebServerRequest* request) {
    logRequest(request);

    bool success = irController->stopLearning();

    if (success) {
        JsonDocument responseData;
        JsonObject result = responseData.createNestedObject();
        result["message"] = "学习模式已停止";
        result["timestamp"] = millis();

        sendSuccessResponse(request, responseData);
    } else {
        sendErrorResponse(request, "学习模式停止失败", 500);
    }
}

void WebServerManager::handleSaveLearning(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!request->hasParam("signalData", true) ||
        !request->hasParam("name", true) ||
        !request->hasParam("type", true)) {
        sendErrorResponse(request, "缺少必需参数", 400);
        return;
    }

    String signalDataStr = request->getParam("signalData", true)->value();
    String name = request->getParam("name", true)->value();
    String type = request->getParam("type", true)->value();
    String description = request->getParam("description", true) ?
                        request->getParam("description", true)->value() : "";

    // 解析信号数据
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, signalDataStr);
    if (error) {
        sendErrorResponse(request, "无效的信号数据格式", 400);
        return;
    }

    JsonObject signalData = doc.as<JsonObject>();
    APIResponse response = signalManager->saveLearnedSignal(name, type, description, signalData);
    sendJsonResponse(request, response);
}

void WebServerManager::handleImportSignals(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!request->hasParam("content", true) || !request->hasParam("fileType", true)) {
        sendErrorResponse(request, "缺少必需参数: content, fileType", 400);
        return;
    }

    String content = request->getParam("content", true)->value();
    String fileType = request->getParam("fileType", true)->value();

    APIResponse response = signalManager->parseImportFile(content, fileType);
    sendJsonResponse(request, response);
}

void WebServerManager::handleImportText(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!request->hasParam("content", true)) {
        sendErrorResponse(request, "缺少必需参数: content", 400);
        return;
    }

    String content = request->getParam("content", true)->value();
    String defaultType = request->getParam("defaultType", true) ?
                        request->getParam("defaultType", true)->value() : "other";

    APIResponse response = signalManager->parseTextImport(content, defaultType);
    sendJsonResponse(request, response);
}

void WebServerManager::handleExecuteImport(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!request->hasParam("signals", true)) {
        sendErrorResponse(request, "缺少必需参数: signals", 400);
        return;
    }

    String signalsStr = request->getParam("signals", true)->value();

    // 解析信号数组
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, signalsStr);
    if (error) {
        sendErrorResponse(request, "无效的信号数据格式", 400);
        return;
    }

    JsonArray signalsArray = doc.as<JsonArray>();
    std::vector<Signal> signals;

    for (JsonObject signalObj : signalsArray) {
        Signal signal = Signal::fromJson(signalObj);
        if (signal.isValid()) {
            signals.push_back(signal);
        }
    }

    if (signals.empty()) {
        sendErrorResponse(request, "没有有效的信号数据", 400);
        return;
    }

    APIResponse response = signalManager->importSignals(signals);
    sendJsonResponse(request, response);
}

void WebServerManager::handleExecuteTextImport(AsyncWebServerRequest* request) {
    logRequest(request);

    // 与handleExecuteImport相同的处理逻辑
    handleExecuteImport(request);
}

void WebServerManager::handleGetSystemLogs(AsyncWebServerRequest* request) {
    logRequest(request);

    // 创建系统日志响应
    JsonDocument responseData;
    JsonArray logsArray = responseData.createNestedArray("logs");

    // 添加一些示例日志（实际应该从文件系统读取）
    JsonObject log1 = logsArray.createNestedObject();
    log1["timestamp"] = millis() - 60000;
    log1["level"] = "INFO";
    log1["category"] = "SYSTEM";
    log1["message"] = "系统启动完成";
    log1["source"] = "main";

    JsonObject log2 = logsArray.createNestedObject();
    log2["timestamp"] = millis() - 30000;
    log2["level"] = "INFO";
    log2["category"] = "NETWORK";
    log2["message"] = "WiFi连接成功";
    log2["source"] = "wifi";

    sendSuccessResponse(request, responseData);
}

void WebServerManager::handleSaveSystemLogs(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!request->hasParam("logs", true)) {
        sendErrorResponse(request, "缺少必需参数: logs", 400);
        return;
    }

    String logsStr = request->getParam("logs", true)->value();

    // 解析日志数据
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, logsStr);
    if (error) {
        sendErrorResponse(request, "无效的日志数据格式", 400);
        return;
    }

    // 这里应该保存到文件系统
    // 暂时只返回成功响应
    JsonDocument responseData;
    JsonObject result = responseData.createNestedObject();
    result["message"] = "日志保存成功";
    result["timestamp"] = millis();

    sendSuccessResponse(request, responseData);
}

void WebServerManager::handleErrorLog(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!request->hasParam("error", true)) {
        sendErrorResponse(request, "缺少必需参数: error", 400);
        return;
    }

    String errorStr = request->getParam("error", true)->value();

    // 记录错误日志
    Serial.printf("📝 前端错误日志: %s\n", errorStr.c_str());

    JsonDocument responseData;
    JsonObject result = responseData.createNestedObject();
    result["message"] = "错误日志已记录";
    result["timestamp"] = millis();

    sendSuccessResponse(request, responseData);
}

void WebServerManager::handleGetSystemStats(AsyncWebServerRequest* request) {
    logRequest(request);

    JsonDocument responseData;
    JsonObject stats = responseData.createNestedObject();

    // 系统统计信息
    stats["uptime"] = millis();
    stats["free_heap"] = ESP.getFreeHeap();
    stats["total_heap"] = ESP.getHeapSize();
    stats["free_psram"] = ESP.getFreePsram();
    stats["total_psram"] = ESP.getPsramSize();
    stats["cpu_freq"] = ESP.getCpuFreqMHz();
    stats["flash_size"] = ESP.getFlashChipSize();

    // 信号统计
    if (signalManager) {
        stats["signal_count"] = signalManager->getSignalCount();
        stats["total_sent"] = signalManager->getTotalSentCount();
    }

    // 网络统计
    stats["wifi_rssi"] = WiFi.RSSI();
    stats["wifi_ssid"] = WiFi.SSID();
    stats["local_ip"] = WiFi.localIP().toString();

    // 服务器统计
    stats["request_count"] = requestCount;
    stats["error_count"] = errorCount;

    sendSuccessResponse(request, responseData);
}

void WebServerManager::handleGetControlHistory(AsyncWebServerRequest* request) {
    logRequest(request);

    // 创建控制历史响应
    JsonDocument responseData;
    JsonArray historyArray = responseData.createNestedArray("history");

    // 添加一些示例历史记录
    JsonObject task1 = historyArray.createNestedObject();
    task1["id"] = "task_12345678";
    task1["type"] = "signal_send";
    task1["signal_id"] = "signal_87654321";
    task1["signal_name"] = "客厅电视开关";
    task1["timestamp"] = millis() - 120000;
    task1["status"] = "completed";
    task1["duration"] = 150;

    sendSuccessResponse(request, responseData);
}

void WebServerManager::handleSaveControlHistory(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!request->hasParam("history", true)) {
        sendErrorResponse(request, "缺少必需参数: history", 400);
        return;
    }

    String historyStr = request->getParam("history", true)->value();

    // 解析历史数据
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, historyStr);
    if (error) {
        sendErrorResponse(request, "无效的历史数据格式", 400);
        return;
    }

    // 这里应该保存到文件系统
    JsonDocument responseData;
    JsonObject result = responseData.createNestedObject();
    result["message"] = "控制历史保存成功";
    result["timestamp"] = millis();

    sendSuccessResponse(request, responseData);
}
