#include "TaskManager.h"
#include "DataManager.h"
#include "IRController.h"
#include "MemoryAllocator.h"

// ==================== 构造函数和析构函数 ====================
TaskManager::TaskManager()
    : m_initialized(false)
    , m_isExecuting(false)
    , m_isPaused(false)
    , m_dataManager(nullptr)
    , m_irController(nullptr)
    , m_currentTaskId("")
    , m_currentStatus(TaskStatus::PENDING)
    , m_currentSignalIndex(0)
    , m_currentLoop(0)
    , m_lastExecutionTime(0)
    , m_executionStartTime(0)
    , m_lastScheduleCheck(0)
    , m_totalExecutions(0)
    , m_successfulExecutions(0)
    , m_failedExecutions(0)
    , m_totalSignalsSent(0)
{
    Serial.println("⚙️ TaskManager created");
}

TaskManager::~TaskManager() {
    shutdown();
    Serial.println("⚙️ TaskManager destroyed");
}

// ==================== 系统生命周期 ====================
bool TaskManager::initialize() {
    if (m_initialized) {
        Serial.println("⚠️ TaskManager already initialized");
        return true;
    }

    Serial.println("⚙️ Initializing TaskManager...");

    // 安全地初始化容器 - 避免在PSRAM未完全准备时调用clear()
    try {
        // 检查容器是否可用，如果不可用则重新构造
        if (m_taskQueue.capacity() == 0) {
            m_taskQueue.reserve(10);  // 预分配少量空间
        }
        if (m_scheduledTasks.capacity() == 0) {
            m_scheduledTasks.reserve(10);  // 预分配少量空间
        }

        // 现在安全地清空
        m_taskQueue.clear();
        m_scheduledTasks.clear();

        Serial.println("✅ Task containers initialized safely");
    } catch (const std::exception& e) {
        Serial.printf("❌ TaskManager container initialization failed: %s\n", e.what());
        return false;
    } catch (...) {
        Serial.println("❌ TaskManager container initialization failed: unknown error");
        return false;
    }

    m_initialized = true;
    Serial.println("✅ TaskManager initialization completed");
    return true;
}

void TaskManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    Serial.println("⚙️ Shutting down TaskManager...");
    
    // 停止当前执行
    stopExecution();
    
    // 清空队列
    clearTaskQueue();
    
    m_initialized = false;
    Serial.println("✅ TaskManager shutdown completed");
}

bool TaskManager::isHealthy() const {
    return m_initialized && m_dataManager && m_irController;
}

void TaskManager::setDependencies(DataManager* dataManager, IRController* irController) {
    m_dataManager = dataManager;
    m_irController = irController;
    Serial.println("⚙️ TaskManager dependencies set");
}

// ==================== 任务执行 ====================
bool TaskManager::executeSingleSignal(const String& signalId) {
    if (!m_initialized || !m_dataManager || !m_irController) {
        logExecution("Dependencies not set");
        return false;
    }
    
    if (m_isExecuting) {
        logExecution("Already executing a task");
        return false;
    }
    
    Serial.printf("⚙️ Executing single signal: %s\n", signalId.c_str());
    
    m_isExecuting = true;
    m_executionStartTime = millis();
    
    bool success = sendSignal(signalId);
    
    if (success) {
        m_successfulExecutions++;
        m_totalSignalsSent++;
        logExecution("Single signal executed successfully");
    } else {
        m_failedExecutions++;
        logExecution("Single signal execution failed");
    }
    
    m_totalExecutions++;
    m_lastExecutionTime = millis();
    m_isExecuting = false;
    
    return success;
}

bool TaskManager::executeAllSignals(int interval) {
    if (!m_initialized || !m_dataManager || !m_irController) {
        return false;
    }
    
    if (m_isExecuting) {
        return false;
    }
    
    Serial.printf("⚙️ Executing all signals with %d ms interval\n", interval);
    
    // 获取所有信号
    DynamicJsonDocument signals = m_dataManager->getAllSignals();
    if (!signals.is<JsonArray>()) {
        logExecution("No signals found");
        return false;
    }
    
    JsonArray signalArray = signals.as<JsonArray>();
    if (signalArray.size() == 0) {
        logExecution("Signal list is empty");
        return false;
    }
    
    m_isExecuting = true;
    m_executionStartTime = millis();
    
    int successCount = 0;
    int totalCount = signalArray.size();
    
    for (JsonVariant signal : signalArray) {
        if (!m_isExecuting || m_isPaused) {
            break;
        }
        
        if (signal.containsKey("id")) {
            String signalId = signal["id"];
            if (sendSignal(signalId)) {
                successCount++;
                m_totalSignalsSent++;
            }
            
            if (interval > 0) {
                delay(interval);
            }
        }
    }
    
    bool success = (successCount == totalCount);
    
    if (success) {
        m_successfulExecutions++;
        logExecution("All signals executed successfully");
    } else {
        m_failedExecutions++;
        logExecution("Some signals failed to execute");
    }
    
    m_totalExecutions++;
    m_lastExecutionTime = millis();
    m_isExecuting = false;
    
    return success;
}

bool TaskManager::executeSelectedSignals(const std::vector<String>& signalIds, int interval) {
    if (!m_initialized || !m_dataManager || !m_irController) {
        return false;
    }
    
    if (m_isExecuting) {
        return false;
    }
    
    if (signalIds.empty()) {
        logExecution("No signals selected");
        return false;
    }
    
    Serial.printf("⚙️ Executing %d selected signals with %d ms interval\n", signalIds.size(), interval);
    
    m_isExecuting = true;
    m_executionStartTime = millis();
    
    int successCount = 0;
    
    for (const String& signalId : signalIds) {
        if (!m_isExecuting || m_isPaused) {
            break;
        }
        
        if (sendSignal(signalId)) {
            successCount++;
            m_totalSignalsSent++;
        }
        
        if (interval > 0) {
            delay(interval);
        }
    }
    
    bool success = (successCount == (int)signalIds.size());
    
    if (success) {
        m_successfulExecutions++;
        logExecution("Selected signals executed successfully");
    } else {
        m_failedExecutions++;
        logExecution("Some selected signals failed to execute");
    }
    
    m_totalExecutions++;
    m_lastExecutionTime = millis();
    m_isExecuting = false;
    
    return success;
}

bool TaskManager::executeTask(const String& taskId) {
    if (!m_initialized || !m_dataManager) {
        return false;
    }
    
    if (m_isExecuting) {
        return false;
    }
    
    // 获取任务数据
    TaskData* task = m_dataManager->getTaskPtr(taskId);
    if (!task) {
        logExecution("Task not found: " + taskId);
        return false;
    }
    
    Serial.printf("⚙️ Executing task: %s\n", taskId.c_str());
    
    m_currentTaskId = taskId;
    m_isExecuting = true;
    m_executionStartTime = millis();
    
    bool success = executeTaskInternal(task);
    
    if (success) {
        m_successfulExecutions++;
        task->status = TaskStatus::COMPLETED;
        logExecution("Task executed successfully");
    } else {
        m_failedExecutions++;
        task->status = TaskStatus::FAILED;
        logExecution("Task execution failed");
    }
    
    task->endTime = millis();
    m_totalExecutions++;
    m_lastExecutionTime = millis();
    m_isExecuting = false;
    m_currentTaskId = "";
    
    return success;
}

void TaskManager::stopExecution() {
    if (m_isExecuting) {
        m_isExecuting = false;
        m_isPaused = false;
        
        // 停止红外发送
        if (m_irController) {
            m_irController->stopSending();
        }
        
        logExecution("Execution stopped");
    }
}

void TaskManager::pauseExecution() {
    if (m_isExecuting && !m_isPaused) {
        m_isPaused = true;
        logExecution("Execution paused");
    }
}

void TaskManager::resumeExecution() {
    if (m_isExecuting && m_isPaused) {
        m_isPaused = false;
        logExecution("Execution resumed");
    }
}

// ==================== 状态监控 ====================
DynamicJsonDocument TaskManager::getExecutionStatus() {
    DynamicJsonDocument status(512);

    status["is_executing"] = m_isExecuting;
    status["is_paused"] = m_isPaused;
    status["current_task"] = m_currentTaskId;
    status["current_signal_index"] = m_currentSignalIndex;
    status["current_loop"] = m_currentLoop;
    status["execution_start_time"] = m_executionStartTime;
    status["last_execution_time"] = m_lastExecutionTime;

    return status;
}

DynamicJsonDocument TaskManager::getStatistics() {
    DynamicJsonDocument stats(512);

    stats["total_executions"] = m_totalExecutions;
    stats["successful_executions"] = m_successfulExecutions;
    stats["failed_executions"] = m_failedExecutions;
    stats["total_signals_sent"] = m_totalSignalsSent;
    stats["queue_size"] = m_taskQueue.size();
    stats["scheduled_tasks"] = m_scheduledTasks.size();

    if (m_totalExecutions > 0) {
        stats["success_rate"] = (float)m_successfulExecutions / m_totalExecutions * 100;
    } else {
        stats["success_rate"] = 0.0f;
    }

    return stats;
}

void TaskManager::resetStatistics() {
    m_totalExecutions = 0;
    m_successfulExecutions = 0;
    m_failedExecutions = 0;
    m_totalSignalsSent = 0;

    logExecution("Statistics reset");
}

bool TaskManager::isExecuting() const {
    return m_isExecuting;
}

bool TaskManager::isPaused() const {
    return m_isPaused;
}

// ==================== 循环处理 ====================
void TaskManager::loop() {
    if (!m_initialized) {
        return;
    }

    // 处理任务队列
    processTaskQueue();

    // 检查定时任务
    checkScheduledTasks();

    // 更新任务状态
    updateTaskStates();

    // 清理完成的任务
    cleanupCompletedTasks();
}

// ==================== 缺失函数实现 ====================

void TaskManager::clearTaskQueue() {
    if (!m_initialized) {
        return;
    }

    // 清空任务队列
    m_taskQueue.clear();

    Serial.println("✅ Task queue cleared");
}

void TaskManager::processTaskQueue() {
    if (!m_initialized || !m_dataManager || m_taskQueue.empty()) {
        return;
    }

    // 如果当前有任务在执行，不处理新任务
    if (!m_currentTaskId.isEmpty()) {
        return;
    }

    // 排序任务队列（按优先级）
    sortTaskQueue();

    // 取出优先级最高的任务
    QueuedTask queuedTask = m_taskQueue[0];
    m_taskQueue.erase(m_taskQueue.begin());

    // 执行任务
    executeTask(queuedTask.taskId);
}

void TaskManager::checkScheduledTasks() {
    if (!m_initialized || !m_dataManager) {
        return;
    }

    unsigned long currentTime = millis();

    // 检查是否需要检查定时任务
    if (currentTime - m_lastScheduleCheck < 1000) { // 每秒检查一次
        return;
    }

    m_lastScheduleCheck = currentTime;

    // 检查所有定时任务
    for (const String& taskId : m_scheduledTasks) {
        // 获取任务数据
        TaskData* taskData = m_dataManager->getTaskPtr(taskId);
        if (taskData && taskData->scheduledTime > 0 &&
            currentTime >= taskData->scheduledTime) {

            // 添加到执行队列
            QueuedTask queuedTask;
            queuedTask.taskId = taskId;
            queuedTask.priority = 1; // 定时任务优先级较高
            queuedTask.queueTime = currentTime;

            m_taskQueue.push_back(queuedTask);

            // 重置定时时间
            taskData->scheduledTime = 0;

            Serial.println("⏰ Scheduled task triggered: " + taskId);
        }
    }
}

DynamicJsonDocument TaskManager::getAllTasksStatus() {
    DynamicJsonDocument status(2048);

    if (!m_initialized || !m_dataManager) {
        status["error"] = "TaskManager not initialized or DataManager not available";
        return status;
    }

    // 获取所有任务数据
    DynamicJsonDocument allTasks = m_dataManager->getAllTasks();

    // 创建状态响应
    status["initialized"] = m_initialized;
    status["executing"] = m_isExecuting;
    status["paused"] = m_isPaused;
    status["current_task_id"] = m_currentTaskId;
    status["queue_size"] = m_taskQueue.size();
    status["scheduled_tasks_count"] = m_scheduledTasks.size();

    // 添加任务列表
    status["tasks"] = allTasks;

    // 添加队列信息
    JsonArray queueArray = status.createNestedArray("queue");
    for (const auto& queuedTask : m_taskQueue) {
        JsonObject queueItem = queueArray.createNestedObject();
        queueItem["task_id"] = queuedTask.taskId;
        queueItem["priority"] = queuedTask.priority;
        queueItem["queue_time"] = queuedTask.queueTime;
    }

    // 添加统计信息
    JsonObject stats = status.createNestedObject("statistics");
    stats["total_executions"] = m_totalExecutions;
    stats["successful_executions"] = m_successfulExecutions;

    return status;
}

void TaskManager::updateTaskStates() {
    // 检查当前任务是否超时
    if (m_isExecuting && !m_currentTaskId.isEmpty()) {
        if (checkTaskTimeout(m_currentTaskId)) {
            handleTaskError(m_currentTaskId, "Task timeout");
            stopExecution();
        }
    }
}

void TaskManager::cleanupCompletedTasks() {
    // 定期清理完成的任务（这里是占位符实现）
    static unsigned long lastCleanup = 0;
    unsigned long currentTime = millis();

    if (currentTime - lastCleanup > 300000) { // 每5分钟清理一次
        // 实际实现中可以清理旧的任务历史记录
        lastCleanup = currentTime;
    }
}

// ==================== 私有方法实现 ====================
bool TaskManager::executeTaskInternal(TaskData* task) {
    if (!task || !m_irController) {
        return false;
    }

    task->status = TaskStatus::RUNNING;
    task->startTime = millis();
    task->currentIndex = 0;
    task->currentLoop = 0;
    task->completedSignals = 0;

    bool success = true;

    // 根据任务类型执行
    switch (task->type) {
        case TaskType::SINGLE_SIGNAL:
            if (!task->signalIds.empty()) {
                success = sendSignal(task->signalIds[0]);
                task->completedSignals = success ? 1 : 0;
                task->totalSignals = 1;
            }
            break;

        case TaskType::ALL_SIGNALS:
            success = executeAllSignals(task->interval);
            break;

        case TaskType::SELECTED_SIGNALS:
            success = executeSelectedSignals(task->signalIds, task->interval);
            break;

        default:
            success = false;
            break;
    }

    return success;
}

bool TaskManager::sendSignal(const String& signalId) {
    if (!m_dataManager || !m_irController) {
        return false;
    }

    // 获取信号数据
    DynamicJsonDocument signalData = m_dataManager->getSignal(signalId);
    if (signalData.isNull() || !signalData.containsKey("signalCode")) {
        logExecution("Signal not found or invalid: " + signalId);
        return false;
    }

    String signalCode = signalData["signalCode"];
    String protocol = signalData["protocol"] | "NEC";

    // 发送信号
    bool success = m_irController->sendSignal(signalCode, protocol);

    if (success) {
        logExecution("Signal sent: " + signalId);
    } else {
        logExecution("Failed to send signal: " + signalId);
    }

    return success;
}

void TaskManager::updateTaskProgress(const String& taskId, const DynamicJsonDocument& progress) {
    // 更新任务进度（占位符实现）
    logExecution("Task progress updated: " + taskId);
}

bool TaskManager::checkTaskTimeout(const String& taskId) {
    // 检查任务是否超时（简化实现）
    unsigned long elapsed = millis() - m_executionStartTime;
    return elapsed > 300000; // 5分钟超时
}

void TaskManager::handleTaskError(const String& taskId, const String& error) {
    logExecution("Task error [" + taskId + "]: " + error);

    if (m_dataManager) {
        TaskData* task = m_dataManager->getTaskPtr(taskId);
        if (task) {
            task->status = TaskStatus::FAILED;
            task->errorMessage = error;
            task->endTime = millis();
        }
    }
}

void TaskManager::logExecution(const String& message) {
    Serial.printf("⚙️ TaskManager: %s\n", message.c_str());
}

bool TaskManager::validateTaskData(const DynamicJsonDocument& taskData) {
    // 基本验证
    if (!taskData.containsKey("name") || !taskData.containsKey("type")) {
        return false;
    }

    // 验证任务类型
    if (!taskData.containsKey("signalIds") || !taskData["signalIds"].is<JsonArray>()) {
        return false;
    }

    return true;
}

void TaskManager::sortTaskQueue() {
    // 按优先级排序（优先级高的在前）
    std::sort(m_taskQueue.begin(), m_taskQueue.end(),
              [](const QueuedTask& a, const QueuedTask& b) {
                  return a.priority > b.priority;
              });
}
