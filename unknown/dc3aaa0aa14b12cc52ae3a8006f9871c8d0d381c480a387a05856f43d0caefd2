/**
 * 红外控制器 - 实现文件
 * 负责红外信号的学习、发射、硬件控制
 */

#include "IRController.h"

// IRSignalData类方法实现

JsonObject IRSignalData::toJson(JsonDocument& doc) const {
    JsonObject obj = doc.createNestedObject();
    
    obj["protocol"] = protocol;
    obj["signalCode"] = signalCode;
    obj["frequency"] = frequency;
    obj["rawData"] = rawData;
    obj["parseSuccess"] = parseSuccess;
    obj["timestamp"] = timestamp;
    
    return obj;
}

IRSignalData IRSignalData::fromDecodeResults(const decode_results& results) {
    IRSignalData data;
    
    data.timestamp = millis();
    data.protocol = typeToString(results.decode_type);
    data.frequency = String(results.freq);
    
    // 处理不同协议的信号码
    if (results.decode_type != UNKNOWN) {
        data.signalCode = "0x" + String(results.value, HEX);
        data.parseSuccess = true;
    } else {
        data.signalCode = "RAW_DATA";
        data.parseSuccess = false;
    }
    
    // 生成原始数据字符串
    String rawStr = "";
    for (uint16_t i = 1; i < results.rawlen; i++) {
        if (i > 1) rawStr += ",";
        rawStr += String(results.rawbuf[i] * kRawTick);
    }
    data.rawData = rawStr;
    
    return data;
}

bool IRSignalData::isValid() const {
    return !protocol.isEmpty() && !signalCode.isEmpty() && timestamp > 0;
}

// IRController类方法实现

IRController::IRController(int sendPin, int recvPin, int statusLedPin) 
    : sendPin(sendPin), recvPin(recvPin), statusLedPin(statusLedPin),
      currentState(LEARNING_IDLE), learningStartTime(0), 
      learningTimeout(DEFAULT_LEARNING_TIMEOUT), debugEnabled(false) {
    
    irSend = nullptr;
    irRecv = nullptr;
}

IRController::~IRController() {
    if (irSend) {
        delete irSend;
    }
    if (irRecv) {
        delete irRecv;
    }
}

bool IRController::init() {
    Serial.println("📡 初始化红外控制器...");
    
    // 初始化状态LED
    if (statusLedPin >= 0) {
        pinMode(statusLedPin, OUTPUT);
        setStatusLED(false);
    }
    
    // 初始化发射器
    if (!initSender()) {
        Serial.println("❌ 红外发射器初始化失败");
        return false;
    }
    
    // 初始化接收器
    if (!initReceiver()) {
        Serial.println("❌ 红外接收器初始化失败");
        return false;
    }
    
    // 重置学习状态
    resetLearningState();
    
    Serial.printf("✅ 红外控制器初始化成功 (发射引脚: %d, 接收引脚: %d)\n", sendPin, recvPin);
    return true;
}

bool IRController::initSender() {
    try {
        irSend = new IRsend(sendPin);
        irSend->begin();
        
        Serial.printf("✅ 红外发射器初始化成功 (引脚: %d)\n", sendPin);
        return true;
    } catch (const std::exception& e) {
        Serial.printf("❌ 红外发射器初始化异常: %s\n", e.what());
        return false;
    }
}

bool IRController::initReceiver() {
    try {
        irRecv = new IRrecv(recvPin, RECV_BUFFER_SIZE, 50, true);
        irRecv->enableIRIn();
        
        Serial.printf("✅ 红外接收器初始化成功 (引脚: %d)\n", recvPin);
        return true;
    } catch (const std::exception& e) {
        Serial.printf("❌ 红外接收器初始化异常: %s\n", e.what());
        return false;
    }
}

bool IRController::startLearning(const String& learningId) {
    if (currentState == LEARNING_ACTIVE) {
        Serial.println("⚠️ 学习模式已在运行中");
        return false;
    }
    
    if (!irRecv) {
        Serial.println("❌ 红外接收器未初始化");
        return false;
    }
    
    // 重置学习状态
    resetLearningState();
    
    // 设置学习参数
    this->learningId = learningId.isEmpty() ? "learning_" + String(millis()) : learningId;
    currentState = LEARNING_ACTIVE;
    learningStartTime = millis();
    
    // 启用接收器
    irRecv->enableIRIn();
    
    // 状态LED指示
    if (statusLedPin >= 0) {
        blinkStatusLED(3, 200); // 快速闪烁3次表示开始学习
    }
    
    Serial.printf("🔍 开始红外学习 (ID: %s, 超时: %d ms)\n", 
                 this->learningId.c_str(), learningTimeout);
    return true;
}

bool IRController::stopLearning() {
    if (currentState != LEARNING_ACTIVE) {
        return false;
    }
    
    currentState = LEARNING_IDLE;
    
    // 禁用接收器
    if (irRecv) {
        irRecv->disableIRIn();
    }
    
    // 关闭状态LED
    setStatusLED(false);
    
    Serial.printf("⏹️ 停止红外学习 (ID: %s)\n", learningId.c_str());
    return true;
}

void IRController::handleLearning() {
    if (currentState != LEARNING_ACTIVE || !irRecv) {
        return;
    }
    
    // 检查学习超时
    unsigned long elapsed = millis() - learningStartTime;
    if (elapsed >= learningTimeout) {
        currentState = LEARNING_TIMEOUT;
        setStatusLED(false);
        
        if (onLearningError) {
            onLearningError("学习超时");
        }
        
        Serial.printf("⏰ 学习超时 (ID: %s, 耗时: %d ms)\n", learningId.c_str(), elapsed);
        return;
    }
    
    // 检查是否接收到信号
    decode_results results;
    if (irRecv->decode(&results)) {
        // 处理接收到的信号
        if (processReceivedSignal(results)) {
            currentState = LEARNING_SUCCESS;
            setStatusLED(true);
            
            if (onLearningSuccess) {
                onLearningSuccess(learnedSignal);
            }
            
            Serial.printf("✅ 学习成功 (ID: %s, 协议: %s, 信号码: %s)\n", 
                         learningId.c_str(), learnedSignal.protocol.c_str(), 
                         learnedSignal.signalCode.c_str());
        }
        
        // 准备接收下一个信号
        irRecv->resume();
    }
}

bool IRController::processReceivedSignal(const decode_results& results) {
    // 过滤无效信号
    if (results.bits == 0 && results.decode_type == UNKNOWN) {
        if (debugEnabled) {
            Serial.println("🔍 跳过无效信号");
        }
        return false;
    }
    
    // 创建信号数据
    learnedSignal = IRSignalData::fromDecodeResults(results);
    
    if (debugEnabled) {
        Serial.printf("🔍 接收到信号: 协议=%s, 位数=%d, 值=0x%08X\n", 
                     learnedSignal.protocol.c_str(), results.bits, results.value);
    }
    
    return learnedSignal.isValid();
}

bool IRController::sendSignal(const String& protocol, const String& signalCode, 
                             const String& frequency) {
    if (!irSend) {
        Serial.println("❌ 红外发射器未初始化");
        return false;
    }
    
    int freq = frequency.toInt();
    if (!validateFrequency(freq)) {
        Serial.printf("❌ 无效的频率: %d\n", freq);
        return false;
    }
    
    if (!validateSignalCode(signalCode, protocol)) {
        Serial.printf("❌ 无效的信号码: %s (协议: %s)\n", signalCode.c_str(), protocol.c_str());
        return false;
    }
    
    // 状态LED指示发射开始
    setStatusLED(true);
    
    bool success = false;
    
    // 根据协议发射信号
    if (protocol == "NEC") {
        uint32_t data = strtoul(signalCode.c_str(), nullptr, 16);
        success = sendNECSignal(data, freq);
    } else if (protocol == "RC5") {
        uint32_t data = strtoul(signalCode.c_str(), nullptr, 16);
        success = sendRC5Signal(data, freq);
    } else if (protocol == "SONY") {
        uint32_t data = strtoul(signalCode.c_str(), nullptr, 16);
        success = sendSONYSignal(data, freq);
    } else if (protocol == "RAW") {
        // RAW数据需要从rawData字段获取
        Serial.println("⚠️ RAW协议需要使用sendRawData方法");
        success = false;
    } else {
        Serial.printf("❌ 不支持的协议: %s\n", protocol.c_str());
        success = false;
    }
    
    // 状态LED指示发射结束
    setStatusLED(false);
    
    if (success) {
        Serial.printf("📡 信号发射成功: %s (%s)\n", signalCode.c_str(), protocol.c_str());
    } else {
        Serial.printf("❌ 信号发射失败: %s (%s)\n", signalCode.c_str(), protocol.c_str());
    }
    
    return success;
}

bool IRController::sendNECSignal(uint32_t data, int frequency) {
    try {
        irSend->sendNEC(data, 32, SEND_REPEAT_COUNT);
        return true;
    } catch (const std::exception& e) {
        Serial.printf("❌ NEC信号发射异常: %s\n", e.what());
        return false;
    }
}

bool IRController::sendRC5Signal(uint32_t data, int frequency) {
    try {
        irSend->sendRC5(data, 13, SEND_REPEAT_COUNT);
        return true;
    } catch (const std::exception& e) {
        Serial.printf("❌ RC5信号发射异常: %s\n", e.what());
        return false;
    }
}

bool IRController::sendSONYSignal(uint32_t data, int frequency) {
    try {
        irSend->sendSony(data, 12, SEND_REPEAT_COUNT);
        return true;
    } catch (const std::exception& e) {
        Serial.printf("❌ SONY信号发射异常: %s\n", e.what());
        return false;
    }
}

void IRController::resetLearningState() {
    currentState = LEARNING_IDLE;
    learningStartTime = 0;
    learnedSignal = IRSignalData();
    learningId = "";
}

bool IRController::validateSignalCode(const String& code, const String& protocol) {
    if (code.isEmpty()) {
        return false;
    }
    
    if (protocol == "RAW") {
        return code == "RAW_DATA";
    }
    
    // 十六进制格式验证
    if (code.startsWith("0x") || code.startsWith("0X")) {
        String hexPart = code.substring(2);
        for (char c : hexPart) {
            if (!isHexadecimalDigit(c)) {
                return false;
            }
        }
        return hexPart.length() > 0 && hexPart.length() <= 8;
    }
    
    return false;
}

bool IRController::validateFrequency(int frequency) {
    return frequency >= 30000 && frequency <= 60000;
}

void IRController::setStatusLED(bool state) {
    if (statusLedPin >= 0) {
        digitalWrite(statusLedPin, state ? HIGH : LOW);
    }
}

void IRController::blinkStatusLED(int times, int delayMs) {
    if (statusLedPin < 0) return;

    for (int i = 0; i < times; i++) {
        digitalWrite(statusLedPin, HIGH);
        delay(delayMs);
        digitalWrite(statusLedPin, LOW);
        if (i < times - 1) {
            delay(delayMs);
        }
    }
}

bool IRController::sendSignal(const IRSignalData& signalData) {
    return sendSignal(signalData.protocol, signalData.signalCode, signalData.frequency);
}

bool IRController::sendRawData(const String& rawData, int frequency) {
    if (!irSend) {
        Serial.println("❌ 红外发射器未初始化");
        return false;
    }

    if (!validateRawData(rawData)) {
        Serial.printf("❌ 无效的原始数据: %s\n", rawData.c_str());
        return false;
    }

    if (!validateFrequency(frequency)) {
        Serial.printf("❌ 无效的频率: %d\n", frequency);
        return false;
    }

    // 解析原始数据字符串
    std::vector<uint16_t> rawArray;
    int start = 0;
    int end = rawData.indexOf(',');

    while (end != -1 || start < rawData.length()) {
        if (end == -1) end = rawData.length();

        String valueStr = rawData.substring(start, end);
        valueStr.trim();

        if (!valueStr.isEmpty()) {
            uint16_t value = valueStr.toInt();
            if (value > 0 && value < 65535) {
                rawArray.push_back(value);
            }
        }

        start = end + 1;
        end = rawData.indexOf(',', start);
    }

    if (rawArray.empty()) {
        Serial.println("❌ 原始数据解析失败");
        return false;
    }

    // 状态LED指示发射开始
    setStatusLED(true);

    bool success = false;
    try {
        // 发射原始数据
        irSend->sendRaw(rawArray.data(), rawArray.size(), frequency);
        success = true;
        Serial.printf("📡 原始数据发射成功: %d 个时序值\n", rawArray.size());
    } catch (const std::exception& e) {
        Serial.printf("❌ 原始数据发射异常: %s\n", e.what());
        success = false;
    }

    // 状态LED指示发射结束
    setStatusLED(false);

    return success;
}

bool IRController::validateRawData(const String& rawData) {
    if (rawData.isEmpty()) {
        return false;
    }

    // 检查是否包含数字和逗号
    bool hasDigit = false;
    bool hasComma = false;

    for (char c : rawData) {
        if (isDigit(c)) {
            hasDigit = true;
        } else if (c == ',') {
            hasComma = true;
        } else if (c != ' ' && c != '\t') {
            // 包含无效字符
            return false;
        }
    }

    return hasDigit; // 至少要有数字
}

bool IRController::isTransmitterReady() const {
    return irSend != nullptr;
}

bool IRController::isReceiverReady() const {
    return irRecv != nullptr;
}

bool IRController::isSending() const {
    // 简单实现：检查发射器是否存在
    return irSend != nullptr;
}

String IRController::getHardwareInfo() const {
    String info = "ESP32-S3 红外控制器\n";
    info += "发射引脚: " + String(sendPin) + "\n";
    info += "接收引脚: " + String(recvPin) + "\n";
    info += "状态LED: " + String(statusLedPin) + "\n";
    info += "发射器状态: " + String(isTransmitterReady() ? "就绪" : "未就绪") + "\n";
    info += "接收器状态: " + String(isReceiverReady() ? "就绪" : "未就绪") + "\n";
    info += "学习状态: " + String(isLearning() ? "学习中" : "空闲") + "\n";
    return info;
}

JsonObject IRController::getHardwareStatus() const {
    JsonDocument doc;
    JsonObject status = doc.to<JsonObject>();

    status["send_pin"] = sendPin;
    status["recv_pin"] = recvPin;
    status["status_led_pin"] = statusLedPin;
    status["transmitter_ready"] = isTransmitterReady();
    status["receiver_ready"] = isReceiverReady();
    status["is_learning"] = isLearning();
    status["learning_state"] = static_cast<int>(currentState);

    if (isLearning()) {
        status["learning_elapsed"] = getLearningElapsedTime();
        status["learning_timeout"] = learningTimeout;
        status["learning_id"] = learningId;
    }

    return status;
}

unsigned long IRController::getLearningElapsedTime() const {
    if (currentState == LEARNING_ACTIVE) {
        return millis() - learningStartTime;
    }
    return 0;
}

void IRController::enableDebug(bool enable) {
    debugEnabled = enable;
    Serial.printf("🔧 红外控制器调试模式: %s\n", enable ? "开启" : "关闭");
}

void IRController::printLearningStatus() const {
    Serial.println("=== 红外学习状态 ===");
    Serial.printf("状态: %d\n", static_cast<int>(currentState));
    Serial.printf("学习ID: %s\n", learningId.c_str());
    Serial.printf("开始时间: %lu\n", learningStartTime);
    Serial.printf("超时时间: %lu\n", learningTimeout);
    Serial.printf("已用时间: %lu\n", getLearningElapsedTime());
    Serial.println("==================");
}

void IRController::printHardwareStatus() const {
    Serial.println("=== 红外硬件状态 ===");
    Serial.printf("发射引脚: %d\n", sendPin);
    Serial.printf("接收引脚: %d\n", recvPin);
    Serial.printf("状态LED: %d\n", statusLedPin);
    Serial.printf("发射器: %s\n", isTransmitterReady() ? "就绪" : "未就绪");
    Serial.printf("接收器: %s\n", isReceiverReady() ? "就绪" : "未就绪");
    Serial.printf("调试模式: %s\n", debugEnabled ? "开启" : "关闭");
    Serial.println("==================");
}

bool IRController::sendRawSignal(const String& rawData, int frequency) {
    if (!irSend) {
        Serial.println("❌ 红外发射器未初始化");
        return false;
    }

    if (!validateRawData(rawData)) {
        Serial.println("❌ 原始数据格式无效");
        return false;
    }

    // 解析原始数据
    std::vector<uint16_t> rawArray;
    String data = rawData;
    data.trim();

    int startIndex = 0;
    int commaIndex = data.indexOf(',');

    while (commaIndex != -1) {
        String valueStr = data.substring(startIndex, commaIndex);
        valueStr.trim();
        uint16_t value = valueStr.toInt();
        if (value > 0) {
            rawArray.push_back(value);
        }

        startIndex = commaIndex + 1;
        commaIndex = data.indexOf(',', startIndex);
    }

    // 处理最后一个值
    if (startIndex < data.length()) {
        String valueStr = data.substring(startIndex);
        valueStr.trim();
        uint16_t value = valueStr.toInt();
        if (value > 0) {
            rawArray.push_back(value);
        }
    }

    if (rawArray.empty()) {
        Serial.println("❌ 原始数据解析失败");
        return false;
    }

    try {
        setStatusLED(true);
        irSend->sendRaw(rawArray.data(), rawArray.size(), frequency);
        setStatusLED(false);

        Serial.printf("✅ 原始信号发射成功 (%d 个时序值, %d Hz)\n",
                     rawArray.size(), frequency);
        return true;
    } catch (const std::exception& e) {
        setStatusLED(false);
        Serial.printf("❌ 原始信号发射异常: %s\n", e.what());
        return false;
    }
}

String IRController::protocolToString(decode_type_t protocol) {
    switch (protocol) {
        case NEC: return "NEC";
        case RC5: return "RC5";
        case RC6: return "RC6";
        case SONY: return "SONY";
        case PANASONIC: return "PANASONIC";
        case JVC: return "JVC";
        case SAMSUNG: return "SAMSUNG";
        case LG: return "LG";
        case DISH: return "DISH";
        case SHARP: return "SHARP";
        case COOLIX: return "COOLIX";
        case DAIKIN: return "DAIKIN";
        case DENON: return "DENON";
        case KELVINATOR: return "KELVINATOR";
        case MITSUBISHI: return "MITSUBISHI";
        case RCMM: return "RCMM";
        case SANYO: return "SANYO";
        case VOLTAS: return "VOLTAS";
        case WHIRLPOOL: return "WHIRLPOOL";
        case AIWA_RC_T501: return "AIWA_RC_T501";
        case CARRIER_AC: return "CARRIER_AC";
        case HITACHI_AC: return "HITACHI_AC";
        case MIDEA: return "MIDEA";
        case TOSHIBA_AC: return "TOSHIBA_AC";
        case MAGIQUEST: return "MAGIQUEST";
        case LASERTAG: return "LASERTAG";
        case UNKNOWN: return "UNKNOWN";
        default: return "OTHER";
    }
}

decode_type_t IRController::stringToProtocol(const String& protocolStr) {
    String protocol = protocolStr;
    protocol.toUpperCase();

    if (protocol == "NEC") return NEC;
    else if (protocol == "RC5") return RC5;
    else if (protocol == "RC6") return RC6;
    else if (protocol == "SONY") return SONY;
    else if (protocol == "PANASONIC") return PANASONIC;
    else if (protocol == "JVC") return JVC;
    else if (protocol == "SAMSUNG") return SAMSUNG;
    else if (protocol == "LG") return LG;
    else if (protocol == "DISH") return DISH;
    else if (protocol == "SHARP") return SHARP;
    else if (protocol == "COOLIX") return COOLIX;
    else if (protocol == "DAIKIN") return DAIKIN;
    else if (protocol == "DENON") return DENON;
    else if (protocol == "KELVINATOR") return KELVINATOR;
    else if (protocol == "MITSUBISHI") return MITSUBISHI;
    else if (protocol == "RCMM") return RCMM;
    else if (protocol == "SANYO") return SANYO;
    else if (protocol == "VOLTAS") return VOLTAS;
    else if (protocol == "WHIRLPOOL") return WHIRLPOOL;
    else if (protocol == "AIWA_RC_T501") return AIWA_RC_T501;
    else if (protocol == "CARRIER_AC") return CARRIER_AC;
    else if (protocol == "HITACHI_AC") return HITACHI_AC;
    else if (protocol == "MIDEA") return MIDEA;
    else if (protocol == "TOSHIBA_AC") return TOSHIBA_AC;
    else if (protocol == "MAGIQUEST") return MAGIQUEST;
    else if (protocol == "LASERTAG") return LASERTAG;
    else return UNKNOWN;
}
