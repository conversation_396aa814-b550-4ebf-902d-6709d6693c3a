#!/usr/bin/env python3
"""
ESP32-S3 红外控制系统 - 预构建脚本
负责构建前的准备工作：文件复制、版本生成、依赖检查
"""

import os
import shutil
import json
import hashlib
from datetime import datetime

Import("env")

def copy_web_files():
    """复制Web文件到data目录"""
    print("📁 复制Web文件到data目录...")
    
    # 源目录和目标目录
    src_data_dir = os.path.join(env.get("PROJECT_DIR"), "data")
    build_data_dir = os.path.join(env.get("BUILD_DIR"), "spiffs")
    
    # 创建目标目录
    if not os.path.exists(build_data_dir):
        os.makedirs(build_data_dir)
    
    # 复制文件
    if os.path.exists(src_data_dir):
        for root, dirs, files in os.walk(src_data_dir):
            for file in files:
                src_file = os.path.join(root, file)
                rel_path = os.path.relpath(src_file, src_data_dir)
                dest_file = os.path.join(build_data_dir, rel_path)
                
                # 创建目标目录
                dest_dir = os.path.dirname(dest_file)
                if not os.path.exists(dest_dir):
                    os.makedirs(dest_dir)
                
                # 复制文件
                shutil.copy2(src_file, dest_file)
                print(f"  ✅ {rel_path}")
    
    print("✅ Web文件复制完成")

def generate_version_info():
    """生成版本信息文件"""
    print("📝 生成版本信息...")
    
    # 版本信息
    version_info = {
        "version": "1.0.0",
        "build_date": datetime.now().isoformat(),
        "build_number": env.get("BUILD_NUMBER", "dev"),
        "git_commit": get_git_commit(),
        "platform": "ESP32-S3",
        "framework": "Arduino",
        "features": [
            "红外学习",
            "红外发射", 
            "Web界面",
            "WebSocket通信",
            "文件系统存储",
            "PSRAM优化"
        ]
    }
    
    # 写入版本文件
    version_file = os.path.join(env.get("BUILD_DIR"), "spiffs", "version.json")
    os.makedirs(os.path.dirname(version_file), exist_ok=True)
    
    with open(version_file, 'w', encoding='utf-8') as f:
        json.dump(version_info, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 版本信息已生成: {version_info['version']}")

def get_git_commit():
    """获取Git提交哈希"""
    try:
        import subprocess
        result = subprocess.run(['git', 'rev-parse', '--short', 'HEAD'], 
                              capture_output=True, text=True, cwd=env.get("PROJECT_DIR"))
        if result.returncode == 0:
            return result.stdout.strip()
    except:
        pass
    return "unknown"

def generate_file_manifest():
    """生成文件清单"""
    print("📋 生成文件清单...")
    
    build_data_dir = os.path.join(env.get("BUILD_DIR"), "spiffs")
    manifest = {
        "generated": datetime.now().isoformat(),
        "files": []
    }
    
    if os.path.exists(build_data_dir):
        for root, dirs, files in os.walk(build_data_dir):
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, build_data_dir)
                
                # 计算文件哈希
                with open(file_path, 'rb') as f:
                    file_hash = hashlib.md5(f.read()).hexdigest()
                
                manifest["files"].append({
                    "path": "/" + rel_path.replace("\\", "/"),
                    "size": os.path.getsize(file_path),
                    "hash": file_hash
                })
    
    # 写入清单文件
    manifest_file = os.path.join(build_data_dir, "manifest.json")
    with open(manifest_file, 'w', encoding='utf-8') as f:
        json.dump(manifest, f, indent=2)
    
    print(f"✅ 文件清单已生成，共 {len(manifest['files'])} 个文件")

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_libs = [
        "ESPAsyncWebServer",
        "ArduinoJson", 
        "IRremoteESP8266"
    ]
    
    # 检查库是否存在
    lib_dir = env.get("PROJECT_LIBDEPS_DIR")
    if lib_dir:
        for lib in required_libs:
            lib_found = False
            for root, dirs, files in os.walk(lib_dir):
                if lib.lower() in root.lower():
                    lib_found = True
                    break
            
            if lib_found:
                print(f"  ✅ {lib}")
            else:
                print(f"  ❌ {lib} - 未找到")
    
    print("✅ 依赖项检查完成")

def optimize_web_files():
    """优化Web文件"""
    print("⚡ 优化Web文件...")
    
    build_data_dir = os.path.join(env.get("BUILD_DIR"), "spiffs")
    
    # 压缩CSS和JS文件（简单的空白符移除）
    for root, dirs, files in os.walk(build_data_dir):
        for file in files:
            if file.endswith(('.css', '.js')):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 简单的压缩：移除多余空白符和注释
                    lines = content.split('\n')
                    compressed_lines = []
                    
                    for line in lines:
                        line = line.strip()
                        # 跳过空行和注释行
                        if line and not line.startswith('//') and not line.startswith('/*'):
                            compressed_lines.append(line)
                    
                    compressed_content = '\n'.join(compressed_lines)
                    
                    # 只有在压缩后文件更小时才保存
                    if len(compressed_content) < len(content):
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(compressed_content)
                        
                        reduction = len(content) - len(compressed_content)
                        print(f"  ✅ {file} 压缩了 {reduction} 字节")
                
                except Exception as e:
                    print(f"  ⚠️ {file} 压缩失败: {e}")
    
    print("✅ Web文件优化完成")

def main():
    """主函数"""
    print("\n🚀 ESP32-S3 红外控制系统 - 预构建脚本")
    print("=" * 50)
    
    try:
        # 执行预构建任务
        copy_web_files()
        generate_version_info()
        generate_file_manifest()
        check_dependencies()
        optimize_web_files()
        
        print("\n✅ 预构建完成！")
        
    except Exception as e:
        print(f"\n❌ 预构建失败: {e}")
        exit(1)

if __name__ == "__main__":
    main()
