#ifndef TASK_QUEUE_H
#define TASK_QUEUE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <queue>
#include "../data/DataStructures.h"

/**
 * 任务优先级枚举
 */
enum class TaskPriority {
    LOW = 0,        // 低优先级
    NORMAL = 1,     // 普通优先级
    HIGH = 2,       // 高优先级
    URGENT = 3      // 紧急优先级
};

/**
 * 队列中的任务项
 */
struct QueuedTask {
    String taskId;              // 任务ID
    TaskPriority priority;      // 任务优先级
    unsigned long queueTime;    // 入队时间
    unsigned long scheduleTime; // 计划执行时间
    int retryCount;            // 重试次数
    bool isScheduled;          // 是否为定时任务
    
    QueuedTask() 
        : taskId(""), priority(TaskPriority::NORMAL), queueTime(0), 
          scheduleTime(0), retryCount(0), isScheduled(false) {}
    
    QueuedTask(const String& id, TaskPriority prio = TaskPriority::NORMAL) 
        : taskId(id), priority(prio), queueTime(millis()), 
          scheduleTime(0), retryCount(0), isScheduled(false) {}
};

/**
 * 任务队列比较器（用于优先级队列）
 */
struct TaskComparator {
    bool operator()(const QueuedTask& a, const QueuedTask& b) const {
        // 首先按优先级排序
        if (a.priority != b.priority) {
            return static_cast<int>(a.priority) < static_cast<int>(b.priority);
        }
        
        // 相同优先级按入队时间排序（FIFO）
        return a.queueTime > b.queueTime;
    }
};

/**
 * 任务队列管理器类
 * 
 * 负责任务队列的管理和调度
 * 支持优先级队列和定时任务
 */
class TaskQueue {
public:
    // ==================== 构造函数和析构函数 ====================
    
    /**
     * 构造函数
     * @param maxSize 队列最大大小
     */
    explicit TaskQueue(size_t maxSize = 100);
    
    /**
     * 析构函数
     */
    ~TaskQueue();
    
    // ==================== 队列操作 ====================
    
    /**
     * 添加任务到队列
     * @param taskId 任务ID
     * @param priority 任务优先级
     * @return bool 添加是否成功
     */
    bool enqueue(const String& taskId, TaskPriority priority = TaskPriority::NORMAL);
    
    /**
     * 添加定时任务到队列
     * @param taskId 任务ID
     * @param scheduleTime 计划执行时间
     * @param priority 任务优先级
     * @return bool 添加是否成功
     */
    bool enqueueScheduled(const String& taskId, unsigned long scheduleTime, 
                         TaskPriority priority = TaskPriority::NORMAL);
    
    /**
     * 从队列中取出下一个任务
     * @return QueuedTask 下一个任务（如果队列为空，返回空任务）
     */
    QueuedTask dequeue();
    
    /**
     * 查看队列中的下一个任务（不移除）
     * @return QueuedTask 下一个任务
     */
    QueuedTask peek() const;
    
    /**
     * 检查队列是否为空
     * @return bool 是否为空
     */
    bool isEmpty() const;
    
    /**
     * 检查队列是否已满
     * @return bool 是否已满
     */
    bool isFull() const;
    
    /**
     * 获取队列大小
     * @return size_t 队列中的任务数量
     */
    size_t size() const;
    
    /**
     * 清空队列
     */
    void clear();
    
    // ==================== 任务查找和管理 ====================
    
    /**
     * 检查任务是否在队列中
     * @param taskId 任务ID
     * @return bool 是否在队列中
     */
    bool contains(const String& taskId) const;
    
    /**
     * 从队列中移除指定任务
     * @param taskId 任务ID
     * @return bool 移除是否成功
     */
    bool remove(const String& taskId);
    
    /**
     * 更新任务优先级
     * @param taskId 任务ID
     * @param newPriority 新优先级
     * @return bool 更新是否成功
     */
    bool updatePriority(const String& taskId, TaskPriority newPriority);
    
    /**
     * 获取任务在队列中的位置
     * @param taskId 任务ID
     * @return int 位置（-1表示不在队列中）
     */
    int getPosition(const String& taskId) const;
    
    // ==================== 定时任务管理 ====================
    
    /**
     * 检查是否有到期的定时任务
     * @return bool 是否有到期任务
     */
    bool hasReadyScheduledTasks() const;
    
    /**
     * 获取所有到期的定时任务
     * @return std::vector<QueuedTask> 到期任务列表
     */
    std::vector<QueuedTask> getReadyScheduledTasks();
    
    /**
     * 移动到期的定时任务到主队列
     * @return int 移动的任务数量
     */
    int promoteScheduledTasks();
    
    // ==================== 队列状态和统计 ====================
    
    /**
     * 获取队列状态
     * @return DynamicJsonDocument 队列状态信息
     */
    DynamicJsonDocument getQueueStatus() const;
    
    /**
     * 获取队列统计信息
     * @return DynamicJsonDocument 统计信息
     */
    DynamicJsonDocument getStatistics() const;
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    /**
     * 获取按优先级分组的任务数量
     * @return DynamicJsonDocument 按优先级分组的统计
     */
    DynamicJsonDocument getPriorityDistribution() const;

private:
    // ==================== 私有成员变量 ====================
    
    size_t m_maxSize;                                           // 队列最大大小
    std::priority_queue<QueuedTask, std::vector<QueuedTask>, TaskComparator> m_queue;  // 优先级队列
    std::vector<QueuedTask> m_scheduledTasks;                   // 定时任务列表
    
    // 统计信息
    unsigned long m_totalEnqueued;      // 总入队任务数
    unsigned long m_totalDequeued;      // 总出队任务数
    unsigned long m_totalScheduled;     // 总定时任务数
    unsigned long m_totalPromoted;      // 总提升任务数
    unsigned long m_totalRemoved;       // 总移除任务数
    
    // ==================== 私有方法 ====================
    
    /**
     * 验证任务ID
     * @param taskId 任务ID
     * @return bool 是否有效
     */
    bool isValidTaskId(const String& taskId) const;
    
    /**
     * 更新统计信息
     * @param operation 操作类型
     */
    void updateStatistics(const String& operation);
    
    /**
     * 清理过期的定时任务
     */
    void cleanupExpiredScheduledTasks();
    
    /**
     * 获取优先级字符串
     * @param priority 优先级
     * @return String 优先级字符串
     */
    String getPriorityString(TaskPriority priority) const;
};

#endif // TASK_QUEUE_H
