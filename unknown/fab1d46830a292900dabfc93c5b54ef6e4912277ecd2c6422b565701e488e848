#ifndef IR_CONTROLLER_H
#define IR_CONTROLLER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <IRsend.h>
#include "system-config.h"
#include "hardware-config.h"

// Forward declaration
class DataManager;

/**
 * 红外控制器类
 *
 * 负责红外信号的发送和接收
 * 硬件抽象层的核心组件
 *
 * 核心功能：
 * - 红外信号发送
 * - 红外信号学习
 * - 硬件状态监控
 * - 信号格式转换
 */
class IRController {
public:
    // ==================== 构造函数和析构函数 ====================

    /**
     * 构造函数
     */
    IRController();

    /**
     * 析构函数
     */
    ~IRController();

    // ==================== 系统生命周期 ====================

    /**
     * 初始化红外控制器
     * @return bool 初始化是否成功
     */
    bool initialize();

    /**
     * 关闭红外控制器
     */
    void shutdown();

    /**
     * 检查健康状态
     * @return bool 是否健康
     */
    bool isHealthy() const;

    /**
     * 设置数据管理器依赖
     * @param dataManager 数据管理器指针
     */
    void setDataManager(DataManager* dataManager);

    // ==================== 信号发送 ====================

    /**
     * 发送红外信号
     * @param signalCode 信号代码
     * @param protocol 信号协议
     * @return bool 发送是否成功
     */
    bool sendSignal(const String& signalCode, const String& protocol = "NEC");

    /**
     * 发送原始信号
     * @param rawData 原始数据数组
     * @param dataLength 数据长度
     * @param frequency 载波频率
     * @return bool 发送是否成功
     */
    bool sendRawSignal(const uint16_t* rawData, size_t dataLength, uint16_t frequency = 38000);

    /**
     * 发送NEC协议信号
     * @param signalCode 信号代码
     * @return bool 发送是否成功
     */
    bool sendNECSignal(const String& signalCode);

    /**
     * 发送RC5协议信号
     * @param signalCode 信号代码
     * @return bool 发送是否成功
     */
    bool sendRC5Signal(const String& signalCode);

    /**
     * 发送RC6协议信号
     * @param signalCode 信号代码
     * @return bool 发送是否成功
     */
    bool sendRC6Signal(const String& signalCode);

    /**
     * 批量发送信号
     * @param signals 信号列表
     * @param interval 信号间隔（毫秒）
     * @return int 成功发送的信号数量
     */
    int batchSendSignals(const DynamicJsonDocument& signals, int interval = 100);

    /**
     * 停止当前发送
     */
    void stopSending();

    // ==================== 信号学习 ====================

    /**
     * 开始学习模式
     * @param timeout 学习超时时间（毫秒）
     * @return bool 是否成功开始学习
     */
    bool startLearning(unsigned long timeout = 10000);

    /**
     * 停止学习模式
     * @return bool 停止是否成功
     */
    bool stopLearning();

    /**
     * 检查是否正在学习
     * @return bool 是否正在学习
     */
    bool isLearning() const;

    /**
     * 获取学习到的信号
     * @return DynamicJsonDocument 学习到的信号数据
     */
    DynamicJsonDocument getLearnedSignal();

    /**
     * 清除学习缓冲区
     */
    void clearLearnBuffer();

    // ==================== 硬件状态 ====================

    /**
     * 获取硬件状态
     * @return DynamicJsonDocument 硬件状态信息
     */
    DynamicJsonDocument getHardwareStatus() const;

    /**
     * 检查发射器状态
     * @return bool 发射器是否正常
     */
    bool isTransmitterReady() const;

    /**
     * 检查接收器状态
     * @return bool 接收器是否正常
     */
    bool isReceiverReady() const;

    /**
     * 获取信号强度
     * @return int 信号强度 (0-100)
     */
    int getSignalStrength() const;

    /**
     * 设置发射功率
     * @param power 功率百分比 (0-100)
     * @return bool 设置是否成功
     */
    bool setTransmitPower(int power);

    /**
     * 获取发射功率
     * @return int 当前功率百分比
     */
    int getTransmitPower() const;

    // ==================== 信号格式转换 ====================

    /**
     * 解析信号代码
     * @param signalCode 信号代码字符串
     * @param protocol 信号协议
     * @return DynamicJsonDocument 解析后的信号数据
     */
    DynamicJsonDocument parseSignalCode(const String& signalCode, const String& protocol);

    /**
     * 格式化信号代码
     * @param signalData 信号数据
     * @return String 格式化后的信号代码
     */
    String formatSignalCode(const DynamicJsonDocument& signalData);

    /**
     * 验证信号代码
     * @param signalCode 信号代码
     * @param protocol 信号协议
     * @return bool 信号代码是否有效
     */
    bool validateSignalCode(const String& signalCode, const String& protocol);

    /**
     * 转换信号格式
     * @param signalCode 原始信号代码
     * @param fromProtocol 源协议
     * @param toProtocol 目标协议
     * @return String 转换后的信号代码
     */
    String convertSignalFormat(const String& signalCode, const String& fromProtocol, const String& toProtocol);

    // ==================== 统计信息 ====================

    /**
     * 获取发送统计
     * @return DynamicJsonDocument 发送统计信息
     */
    DynamicJsonDocument getSendStatistics() const;

    /**
     * 获取学习统计
     * @return DynamicJsonDocument 学习统计信息
     */
    DynamicJsonDocument getLearnStatistics() const;

    /**
     * 重置统计信息
     */
    void resetStatistics();

    // ==================== 调试功能 ====================

    /**
     * 启用调试模式
     * @param enable 是否启用
     */
    void enableDebugMode(bool enable);

    /**
     * 获取调试信息
     * @return String 调试信息
     */
    String getDebugInfo() const;

    /**
     * 执行硬件自检
     * @return DynamicJsonDocument 自检结果
     */
    DynamicJsonDocument performSelfTest();

private:
    // ==================== 私有成员变量 ====================

    bool m_initialized;                 // 是否已初始化
    bool m_transmitterReady;           // 发射器是否就绪
    bool m_receiverReady;              // 接收器是否就绪
    bool m_isLearning;                 // 是否正在学习
    bool m_isSending;                  // 是否正在发送
    bool m_debugMode;                  // 调试模式

    int m_transmitPower;               // 发射功率 (0-100)
    unsigned long m_learningStartTime; // 学习开始时间
    unsigned long m_learningTimeout;   // 学习超时时间

    DataManager* m_dataManager;        // 数据管理器指针
    IRsend m_irSender;                 // IR发送器对象

    // 学习缓冲区
    uint16_t* m_learnBuffer;           // 学习数据缓冲区
    size_t m_learnBufferSize;          // 缓冲区大小
    size_t m_learnDataLength;          // 已学习数据长度

    // 统计信息
    unsigned long m_totalSentSignals;  // 总发送信号数
    unsigned long m_totalLearnedSignals; // 总学习信号数
    unsigned long m_lastSendTime;      // 最后发送时间
    unsigned long m_lastLearnTime;     // 最后学习时间

    // ==================== 私有方法 ====================

    /**
     * 初始化硬件
     * @return bool 初始化是否成功
     */
    bool initializeHardware();

    /**
     * 清理硬件资源
     */
    void cleanupHardware();

    /**
     * 配置PWM
     * @param frequency 频率
     * @return bool 配置是否成功
     */
    bool configurePWM(uint16_t frequency);

    /**
     * 发送脉冲
     * @param duration 持续时间（微秒）
     * @param level 电平 (HIGH/LOW)
     */
    void sendPulse(uint16_t duration, bool level);

    /**
     * 检测信号
     * @return bool 是否检测到信号
     */
    bool detectSignal();

    /**
     * 读取原始数据
     * @return size_t 读取的数据长度
     */
    size_t readRawData();

    /**
     * 分析学习到的数据
     * @return DynamicJsonDocument 分析结果
     */
    DynamicJsonDocument analyzeLearnedData();

    /**
     * 记录调试信息
     * @param message 调试信息
     */
    void debugLog(const String& message);
};

#endif // IR_CONTROLLER_H
