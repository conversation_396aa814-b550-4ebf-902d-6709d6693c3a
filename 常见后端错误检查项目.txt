ESP32-S3红外控制系统 - 后端错误检查清单

=== 检查流程（必须按顺序执行）===

第一步：标准文档合规性检查
✅ 检查是否100%按照"ESP32-S3红外控制系统-完整后端开发标准文档.md"要求实现
✅ 检查文件结构、命名规范、接口定义是否完全符合标准
✅ 检查数据结构、枚举、常量定义是否统一
✅ 检查函数签名、返回类型是否符合标准
✅ 检查注释格式、文档说明是否符合标准
✅ 检查依赖关系、初始化顺序是否符合标准
✅ 检查错误处理机制是否符合标准
✅ 检查线程安全机制是否符合标准

第二步：错误预防清单检查

最常见的“实现缺陷”错误清单
① 函数、模块缺失 / 不完整
 函数声明了，没写实现

 函数实现了，但逻辑是空的（没做任何事）

 新功能少了关键模块：

比如：

信号发送功能写了 send()，但少了校验环节

没写 error handling

 只有 happy path，没有处理异常 path

 函数名写错

 中断服务函数未注册

 FreeRTOS task 创建了，但没进入 scheduler

 回调函数没绑定

 event group / semaphore / queue 少了 create

影响：程序能编译，但功能没反应或直接 crash。

② 逻辑遗漏
 if-else 分支漏写一条路

 break / continue 写错导致循环异常

 switch-case 漏写 default

 异常条件没覆盖

 特殊输入没处理（空串、null）

 串口通讯漏写超时处理

 不同状态下没有覆盖所有分支

 文件句柄/指针关闭忘记释放

 电源异常情况没考虑

 OTA 升级失败回滚没写

③ 内存溢出 / 泄漏
 malloc 没判断返回值

 malloc 后 free 忘了写

 全局数组超出 .bss 区域

 memcpy/memset 长度写错

 字符串拼接导致越界

 sprintf 不判断长度

 堆栈溢出：

ISR 占用栈过大

FreeRTOS task stack 太小

 内存泄漏（每次分配都没释放）

 多线程同时访问 malloc/free，没加锁

④ 死循环 / 阻塞
 while(1) 少了 exit 条件

 for(;;) 忘了 break

 等待事件永远不触发

 阻塞等待信号量，但无人释放

 FreeRTOS 队列满，send block forever

 串口读写阻塞太久

 SPI / I2C 死等 busy flag

⑤ 冗余 / 重复
 同样代码写了多次

 多次初始化硬件

 反复打开同一外设

 全局变量多次定义

 相同宏定义在不同文件重复

 重复注册 ISR

 重复开启任务

⑥ 数学 / 算术错误
 除 0

 数据类型溢出：

int -> long

uint8_t 溢出

 shift 左移过头

 浮点运算精度误差

 转换错误：

atoi -> 溢出

float -> int 丢失精度

 符号问题：

signed vs unsigned 比较

⑦ 并发问题
 全局变量被多任务同时访问

 没加 mutex 或 critical section

 任务间数据竞争

 中断里访问与任务共用的 buffer

 FreeRTOS queue 被多线程同时操作

 发送和接收线程对同一 socket 操作

 ISR 内调用阻塞 API

⑧ 未做错误处理
 不检查函数返回值

esp_err_t

HAL_StatusTypeDef

 打印日志了事，但没处理

 关键外设错误没有 fallback

 网络失败无重试

 OTA 校验失败不处理

 内存分配失败不处理

 文件系统操作失败不处理

⑨ 硬件依赖错误
 GPIO 未初始化即使用

 I2C 未配置时钟

 PWM 与外设冲突

 电平电压不匹配 (3.3V vs 5V)

 flash 芯片容量误用

 MCU 支持功能与代码不匹配：

写了 PSRAM，硬件没支持

⑩ 时序/定时错误
 定时器没开启

 延时不够

 task sleep 写错时间单位（ms vs tick）

 同步信号先到，事件监听晚到

 多任务事件 race condition

⑪ 数据结构错用
 queue size 不够

 队列或 buffer 未初始化

 数组越界

 链表节点指针指空

 内存池分配大小写错

 栈溢出（ISR 或任务）

⑫ 串口/通讯协议错误
 Baud rate 不一致

 帧头帧尾搞错

 校验漏写

 协议 version 不匹配

 字节序大小端写错

 串口 flush 忘记

 接收 buffer 不够长

 断线重连没写

⑬ Flash 操作错误
 写地址错

 擦写不对齐

 擦写太频繁 → 寿命

 分区表搞错

 OTA 不匹配分区表

⑭ OTA / 升级
 校验漏写

 分区不匹配

 OTA 失败不回滚

 OTA buffer 太小

 OTA 版本号没写进 Flash

⑮ 日志 / Debug
 串口 printf 太多导致卡死

 Debug log 写在中断里

 Release 没关闭调试宏

 日志溢出导致崩溃

⑯ 接口层面错误
 JSON 格式与前端不一致

 null 值没处理

 key 大小写不一致

 返回字段漏写

 特殊字符没转义

 API 路由写错

⑰ 性能问题
 for 循环写成 while(1)

 malloc 太频繁

 task 优先级写太高

 中断处理时间太长

 内存分配过度导致碎片化

⑱ 电源相关
 电流不足导致 brownout

 芯片掉电 reset

 没加稳压电容

 高 IO 电流导致电压跌落

⑲ 配置缺失
 platformio.ini 忘了写 build_flags

 sdkconfig 未开启某功能

 OTA 分区表没配置

 PWM, ADC 通道冲突未配置

 linker script 没改

⑳ 测试缺失
 没做边界值测试

 没测异常输入

 功能只测 happy path

 OTA 没测试

 电源波动没测

 大量并发请求没测


MCU / PlatformIO 编译阶段常见错误全清单
以下错误是：

能被编译器 / 链接器检测到

必须修掉，否则连 .bin/.elf 都出不来

不包含“运行期 bug”，是编译期错误

✅ ① 语法错误（Syntax Errors）
 拼写错误

c
复制
编辑
inti x = 0;     // 错写 int
 分号漏写

 花括号不成对

 if/else、switch 语法结构写错

 函数声明与定义不匹配

✅ ② 类型错误（Type Errors）
 类型不兼容

c
复制
编辑
int x = "hello";
 函数返回值类型不匹配

 指针类型不兼容

 隐式转换导致 warning/error

 const 被强制修改

✅ ③ 未声明/未定义
 变量未声明

 函数未声明

 宏未定义

 extern 声明未找到定义

 未 include 必要头文件

 symbol not found in object file

✅ ④ 重复定义
 同名变量多次定义

 同名函数多次定义

 重复 include 引发重复定义

 全局变量名冲突

 #define 与全局变量冲突

✅ ⑤ 链接错误（Linker Errors）
 undefined reference to xxxx

 multiple definition of xxxx

 cannot find symbol

 unresolved external symbol

 目标文件缺失 (.o, .a)

 链接脚本 (.ld) 写错或丢失

 entry point 未定义

 分区表大小和 elf 超界

 目标地址冲突

✅ ⑥ 库相关错误
 没有 install 库 (missing library)

 platformio.ini 中 lib_deps 写错

 库版本不兼容

 两个库内部 symbol 冲突

 framework 冲突（比如 ESP-IDF vs Arduino 混用）

 lib_ldf_mode 设置不对导致依赖没解析

✅ ⑦ 文件路径 / 名称错误
 文件名拼写错

 路径大小写不一致 (Linux vs Windows)

 include 路径写错

 relative path vs absolute path 错误

 header 文件丢失

 source file 没加进 project

 依赖库路径没配置

✅ ⑧ 内存限制 / 空间超限
 data section 超出 RAM

 flash section 超出 flash

 .bss 段太大

 PSRAM overflow

 linker script memory region overflow

典型错误：

vbnet
复制
编辑
region `DRAM' overflowed by 456 bytes
✅ ⑨ 编译器选项 / Flags 错误
 未识别的编译参数

 -std=c99 / -std=gnu++11 不兼容

 优化等级冲突 (e.g. -O3 与某 flags 不兼容)

 不支持 LTO

 编译器不支持目标架构

 CXXFLAGS vs CFLAGS 混淆

✅ ⑩ 预处理器问题
 #ifdef 写错

 宏定义名写错

 #endif 丢失

 条件编译逻辑写错

 宏递归定义

✅ ⑪ 编译器 / Toolchain 版本问题
 GCC 版本过低

 framework 与 toolchain 不兼容

 platformio.ini 指定 platform 版本过旧

 特定 chip 需要新版 SDK

 内部工具版本 mismatch (ld, ar, objcopy)

✅ ⑫ 编码 / 字符集问题
 文件保存为 UTF-16 被误读

 非 ASCII 字符未转义

 BOM 字节导致编译错误

 Linux 与 Windows 行尾混用

✅ ⑬ PlatformIO 特有
 环境名写错

 platformio.ini 的 [env] 格式错误

 build_flags 写错

 upload_port 未指定导致上传失败

 分区表未指定

 使用了不支持的 board

 lib_archive = false 与静态库冲突

 使用 wrong framework（比如写成 arduinoespressif32 拼错）

✅ ⑭ .ld 链接脚本问题
 segment 定义漏写

 地址 overlap

 未定义 __heap_start, __heap_end

 start address 写错

 OTA 分区地址冲突

 未指定 entry point

✅ ⑮ Mixed C/C++
 .c 调用 C++ symbol

 .cpp include .c 未 extern "C"

 类方法声明为 extern C

典型错误：

vbnet
复制
编辑
undefined reference to `xxx::Method()'
✅ ⑯ 调试宏未关闭
 DEBUG 宏未 #undef

 assert 在 release 编译时导致问题

 日志打印导致大 code size

✅ ⑰ 特定芯片相关
针对 ESP32：

 PSRAM 未开启导致内存超界

 flash_mode 写错 (qio/dio)

 sdkconfig 未同步

 partition.csv 配置不对

针对 STM32：

 HAL 库版本不一致

 vector table 未定义

 clock config 不匹配

✅ ⑱ 烧录工具错误（虽然不是编译 error，但常卡住上传）
 串口占用

 未进入 boot mode

 upload_speed 太快导致失败

 flash size mismatch

 USB driver 未装

 esptool.py 报错