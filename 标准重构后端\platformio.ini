;PlatformIO Project Configuration File
;
; ESP32-S3 红外控制系统 - 项目配置
; 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第二部分：最优化库配置
;
; Build options: build flags, source filter
; Upload options: custom upload port, speed and extra flags
; Library options: dependencies, extra library storages
; Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-s3-devkitm-1]
platform = espressif32@6.11.0          ; 官方最新稳定版
board = esp32-s3-devkitm-1
framework = arduino

; === 性能优化配置 ===
upload_speed = 921600
monitor_speed = 115200
upload_protocol = esptool

; === Flash 优化配置 ===
board_build.flash_mode = qio            ; 最快的Flash模式
board_build.flash_size = 16MB
board_build.filesystem = littlefs
board_build.partitions = partitions.csv

; === 最新库依赖 ===
lib_deps =
    Arduino<PERSON>son@7.4.2                   ; 最新版本，性能提升15%
    ESP32Async/ESPAsyncWebServer@3.7.8  ; 活跃维护版本
    ESP32Async/AsyncTCP@3.4.4           ; 最新稳定版
    IRremoteESP8266@2.8.6               ; 最新稳定版

; === 高性能构建标志 ===
build_flags =
    ; 基础配置
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DCORE_DEBUG_LEVEL=1                ; 生产环境降低日志级别
    
    ; 网络性能优化
    -DCONFIG_LWIP_MAX_SOCKETS=16        ; 增加最大连接数
    -DCONFIG_LWIP_TCP_MSS=1460          ; 最优MSS值
    -DCONFIG_LWIP_TCP_SND_BUF_DEFAULT=8192  ; 增大发送缓冲区
    -DCONFIG_LWIP_TCP_WND_DEFAULT=8192      ; 增大接收窗口
    
    ; AsyncTCP 优化
    -DCONFIG_ASYNC_TCP_STACK_SIZE=16384     ; 增大栈大小
    -DCONFIG_ASYNC_TCP_PRIORITY=10          ; 高优先级
    -DCONFIG_ASYNC_TCP_QUEUE_SIZE=128       ; 增大队列
    -DCONFIG_ASYNC_TCP_USE_WDT=0            ; 禁用看门狗
    
    ; 内存优化
    -DCONFIG_NEWLIB_NANO_FORMAT             ; 使用nano格式化
    -DCONFIG_SPIRAM_SUPPORT=0               ; 明确禁用PSRAM
    
    ; JSON 性能优化
    -DARDUINOJSON_USE_LONG_LONG=1           ; 支持64位整数
    -DARDUINOJSON_ENABLE_PROGMEM=1          ; 启用PROGMEM支持
    
    ; 红外优化
    -DDECODE_HASH=1                         ; 启用Hash解码
    -DSEND_RAW=1                           ; 启用原始发送
    
    ; 系统优化
    -DCONFIG_FREERTOS_HZ=1000               ; 提高FreeRTOS频率
    -DCONFIG_ESP32_DEFAULT_CPU_FREQ_240     ; 240MHz CPU频率
    
    ; 文件系统优化
    -DCONFIG_LITTLEFS_FOR_IDF_3_2           ; LittleFS兼容性
    -DCONFIG_LITTLEFS_MAX_PARTITIONS=3      ; 最大分区数
    
    ; WebServer优化
    -DCONFIG_ASYNC_TCP_MAX_ACK_TIME=5000    ; ACK超时时间
    -DCONFIG_ASYNC_TCP_SYN_RETRIES=5        ; SYN重试次数
    
    ; 调试和监控
    -DCONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=4096  ; 事件任务栈大小
    -DCONFIG_ESP_MAIN_TASK_STACK_SIZE=8192           ; 主任务栈大小

; === 监控配置 ===
monitor_filters = 
    esp32_exception_decoder
    time

; === 调试配置 ===
debug_tool = esp-builtin
debug_init_break = tbreak setup

; === 上传配置 ===
upload_port = COM*                      ; 自动检测端口
monitor_port = COM*                     ; 自动检测端口

; === 测试配置 ===
test_framework = unity
test_build_project_src = true
