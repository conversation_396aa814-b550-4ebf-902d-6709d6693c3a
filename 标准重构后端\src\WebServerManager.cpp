#include "WebServerManager.h"
#include "core/JSONConverter.h"

/**
 * ESP32-S3 红外控制系统 - Web服务器管理器实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：Web服务器管理器
 */

WebServerManager::WebServerManager() 
    : m_server(nullptr), m_dataManager(nullptr), m_irController(nullptr), m_wsManager(nullptr),
      m_signalService(nullptr), m_taskService(nullptr), m_timerService(nullptr), 
      m_systemService(nullptr), m_initialized(false) {}

WebServerManager::~WebServerManager() {
    cleanup();
}

bool WebServerManager::initialize(const ServerConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) return true;
    
    if (!validateDependencies()) {
        Serial.println("❌ WebServerManager: Missing required dependencies");
        return false;
    }
    
    m_config = config;
    m_server = new AsyncWebServer(m_config.port);
    
    if (!m_server) {
        Serial.println("❌ WebServerManager: Failed to create server");
        return false;
    }
    
    setupRoutes();
    
    m_stats.startTime = millis();
    m_initialized = true;
    
    Serial.println("✅ WebServerManager: Initialized successfully");
    return true;
}

void WebServerManager::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        stopServer();
        
        if (m_server) {
            delete m_server;
            m_server = nullptr;
        }
        
        m_initialized = false;
        Serial.println("✅ WebServerManager: Cleanup completed");
    }
}

void WebServerManager::setDataManager(DataManager* dataManager) {
    m_dataManager = dataManager;
}

void WebServerManager::setIRController(IRController* irController) {
    m_irController = irController;
}

void WebServerManager::setWSManager(WSManager* wsManager) {
    m_wsManager = wsManager;
}

void WebServerManager::setSignalService(SignalService* signalService) {
    m_signalService = signalService;
}

void WebServerManager::setTaskService(TaskService* taskService) {
    m_taskService = taskService;
}

void WebServerManager::setTimerService(TimerService* timerService) {
    m_timerService = timerService;
}

void WebServerManager::setSystemService(SystemService* systemService) {
    m_systemService = systemService;
}

bool WebServerManager::startServer() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_server) return false;
    
    m_server->begin();
    
    if (m_systemService) {
        m_systemService->addLogEntry(SystemService::LogLevel::INFO, "WebServerManager", 
                                    "Web server started on port " + String(m_config.port));
    }
    
    Serial.printf("✅ WebServerManager: Server started on port %d\n", m_config.port);
    return true;
}

bool WebServerManager::stopServer() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_server) return false;
    
    m_server->end();
    
    if (m_systemService) {
        m_systemService->addLogEntry(SystemService::LogLevel::INFO, "WebServerManager", 
                                    "Web server stopped");
    }
    
    Serial.println("✅ WebServerManager: Server stopped");
    return true;
}

bool WebServerManager::restartServer() {
    bool stopResult = stopServer();
    delay(1000);
    bool startResult = startServer();
    
    return stopResult && startResult;
}

JsonObject WebServerManager::getDetailedStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    JsonObject stats = doc.createNestedObject("webserver_statistics");
    stats["initialized"] = m_initialized;
    stats["total_requests"] = m_stats.totalRequests;
    stats["successful_requests"] = m_stats.successfulRequests;
    stats["failed_requests"] = m_stats.failedRequests;
    stats["auth_failures"] = m_stats.authFailures;
    stats["last_request"] = m_stats.lastRequest;
    stats["start_time"] = m_stats.startTime;
    stats["uptime"] = millis() - m_stats.startTime;
    
    JsonObject config = stats.createNestedObject("config");
    config["port"] = m_config.port;
    config["enable_cors"] = m_config.enableCORS;
    config["enable_authentication"] = m_config.enableAuthentication;
    config["enable_static_files"] = m_config.enableStaticFiles;
    config["static_path"] = m_config.staticPath;
    config["max_connections"] = m_config.maxConnections;
    
    return stats;
}

bool WebServerManager::updateConfig(const ServerConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_config = config;
    
    if (m_systemService) {
        m_systemService->addLogEntry(SystemService::LogLevel::INFO, "WebServerManager", 
                                    "Configuration updated");
    }
    
    return true;
}

// ==================== 路由设置 ====================

void WebServerManager::setupRoutes() {
    if (!m_server) return;
    
    // 设置默认处理器
    m_server->onNotFound([this](AsyncWebServerRequest* request) {
        logRequest(request);
        sendErrorResponse(request, 404, "Endpoint not found");
        updateStats(false);
    });
    
    setupSignalRoutes();
    setupTaskRoutes();
    setupTimerRoutes();
    setupSystemRoutes();
    
    if (m_config.enableStaticFiles) {
        setupStaticRoutes();
    }
}

void WebServerManager::setupSignalRoutes() {
    if (!m_server) return;
    
    // GET /api/signals - 获取所有信号
    m_server->on("/api/signals", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSignals(request);
    });
    
    // GET /api/signals/{id} - 获取特定信号
    m_server->on("^\\/api\\/signals\\/([0-9]+)$", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSignal(request);
    });
    
    // POST /api/signals - 创建新信号
    m_server->on("/api/signals", HTTP_POST, 
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleCreateSignal(request, data, len);
            }
        }
    );
    
    // PUT /api/signals/{id} - 更新信号
    m_server->on("^\\/api\\/signals\\/([0-9]+)$", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleUpdateSignal(request, data, len);
            }
        }
    );
    
    // DELETE /api/signals/{id} - 删除信号
    m_server->on("^\\/api\\/signals\\/([0-9]+)$", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        handleDeleteSignal(request);
    });
    
    // POST /api/signals/{id}/send - 发送信号
    m_server->on("^\\/api\\/signals\\/([0-9]+)\\/send$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSendSignal(request);
    });
    
    // POST /api/signals/learn/start - 开始学习
    m_server->on("/api/signals/learn/start", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleStartLearning(request);
    });
    
    // POST /api/signals/learn/stop - 停止学习
    m_server->on("/api/signals/learn/stop", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleStopLearning(request);
    });
    
    // GET /api/signals/learn/result - 获取学习结果
    m_server->on("/api/signals/learn/result", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetLearnedSignal(request);
    });
}

void WebServerManager::setupTaskRoutes() {
    if (!m_server) return;
    
    // GET /api/tasks - 获取所有任务
    m_server->on("/api/tasks", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetTasks(request);
    });
    
    // GET /api/tasks/{id} - 获取特定任务
    m_server->on("^\\/api\\/tasks\\/([0-9]+)$", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetTask(request);
    });
    
    // POST /api/tasks - 创建新任务
    m_server->on("/api/tasks", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleCreateTask(request, data, len);
            }
        }
    );
    
    // PUT /api/tasks/{id} - 更新任务
    m_server->on("^\\/api\\/tasks\\/([0-9]+)$", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleUpdateTask(request, data, len);
            }
        }
    );
    
    // DELETE /api/tasks/{id} - 删除任务
    m_server->on("^\\/api\\/tasks\\/([0-9]+)$", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        handleDeleteTask(request);
    });
    
    // POST /api/tasks/{id}/execute - 执行任务
    m_server->on("^\\/api\\/tasks\\/([0-9]+)\\/execute$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleExecuteTask(request);
    });
}

void WebServerManager::setupTimerRoutes() {
    if (!m_server) return;

    // GET /api/timers - 获取所有定时器
    m_server->on("/api/timers", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetTimers(request);
    });

    // GET /api/timers/{id} - 获取特定定时器
    m_server->on("^\\/api\\/timers\\/([0-9]+)$", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetTimer(request);
    });

    // POST /api/timers - 创建新定时器
    m_server->on("/api/timers", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleCreateTimer(request, data, len);
            }
        }
    );

    // PUT /api/timers/{id} - 更新定时器
    m_server->on("^\\/api\\/timers\\/([0-9]+)$", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleUpdateTimer(request, data, len);
            }
        }
    );

    // DELETE /api/timers/{id} - 删除定时器
    m_server->on("^\\/api\\/timers\\/([0-9]+)$", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        handleDeleteTimer(request);
    });

    // POST /api/timers/{id}/enable - 启用定时器
    m_server->on("^\\/api\\/timers\\/([0-9]+)\\/enable$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleEnableTimer(request);
    });

    // POST /api/timers/{id}/disable - 禁用定时器
    m_server->on("^\\/api\\/timers\\/([0-9]+)\\/disable$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleDisableTimer(request);
    });
}

void WebServerManager::setupSystemRoutes() {
    if (!m_server) return;

    // GET /api/system/status - 获取系统状态
    m_server->on("/api/system/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemStatus(request);
    });

    // GET /api/system/info - 获取系统信息
    m_server->on("/api/system/info", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemInfo(request);
    });

    // GET /api/system/logs - 获取系统日志
    m_server->on("/api/system/logs", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetLogs(request);
    });

    // POST /api/system/restart - 重启系统
    m_server->on("/api/system/restart", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleSystemRestart(request);
    });

    // POST /api/system/factory-reset - 恢复出厂设置
    m_server->on("/api/system/factory-reset", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleFactoryReset(request);
    });

    // POST /api/system/backup - 创建备份
    m_server->on("/api/system/backup", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleCreateBackup(request);
    });

    // POST /api/system/restore - 恢复备份
    m_server->on("/api/system/restore", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleRestoreBackup(request, data, len);
            }
        }
    );
}

void WebServerManager::setupStaticRoutes() {
    if (!m_server) return;

    // 静态文件服务
    m_server->serveStatic("/", LittleFS, m_config.staticPath.c_str());

    // 默认首页
    m_server->on("/", HTTP_GET, [this](AsyncWebServerRequest* request) {
        logRequest(request);
        request->send(LittleFS, m_config.staticPath + "/index.html", "text/html");
        updateStats(true);
    });
}

// ==================== 中间件实现 ====================

bool WebServerManager::authenticateRequest(AsyncWebServerRequest* request) {
    if (!m_config.enableAuthentication) return true;

    if (m_config.authToken.isEmpty()) return true;

    String authHeader = "";
    if (request->hasHeader("Authorization")) {
        authHeader = request->getHeader("Authorization")->value();
    }

    String expectedAuth = "Bearer " + m_config.authToken;

    if (authHeader != expectedAuth) {
        m_stats.authFailures++;
        return false;
    }

    return true;
}

void WebServerManager::addCORSHeaders(AsyncWebServerResponse* response) {
    if (!m_config.enableCORS) return;

    response->addHeader("Access-Control-Allow-Origin", "*");
    response->addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response->addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
    response->addHeader("Access-Control-Max-Age", "86400");
}

void WebServerManager::logRequest(AsyncWebServerRequest* request) {
    m_stats.totalRequests++;
    m_stats.lastRequest = millis();

    if (m_systemService) {
        String logMsg = String(request->methodToString()) + " " + request->url() +
                       " from " + request->client()->remoteIP().toString();
        m_systemService->addLogEntry(SystemService::LogLevel::DEBUG, "WebServerManager", logMsg);
    }
}

void WebServerManager::updateStats(bool success) {
    if (success) {
        m_stats.successfulRequests++;
    } else {
        m_stats.failedRequests++;
    }
}

// ==================== API处理器实现 - 信号管理 ====================

void WebServerManager::handleGetSignals(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!authenticateRequest(request)) {
        sendErrorResponse(request, 401, "Unauthorized");
        updateStats(false);
        return;
    }

    if (!m_signalService) {
        sendErrorResponse(request, 503, "Signal service not available");
        updateStats(false);
        return;
    }

    auto signals = m_signalService->getAllSignals();

    DynamicJsonDocument doc(8192);
    JsonArray signalsArray = JSONConverter::signalsToJsonArray(signals, doc);

    JsonObject response = doc.createNestedObject();
    response["success"] = true;
    response["data"] = signalsArray;
    response["count"] = signals.size();

    sendJsonResponse(request, doc);
    updateStats(true);
}

void WebServerManager::handleGetSignal(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!authenticateRequest(request)) {
        sendErrorResponse(request, 401, "Unauthorized");
        updateStats(false);
        return;
    }

    if (!m_signalService) {
        sendErrorResponse(request, 503, "Signal service not available");
        updateStats(false);
        return;
    }

    uint32_t id = getIdFromPath(request, "/api/signals/");
    if (id == INVALID_ID) {
        sendErrorResponse(request, 400, "Invalid signal ID");
        updateStats(false);
        return;
    }

    auto result = m_signalService->getSignal(id);
    if (!result.isSuccess()) {
        sendErrorResponse(request, 404, "Signal not found");
        updateStats(false);
        return;
    }

    DynamicJsonDocument doc(2048);
    JsonObject signalObj = JSONConverter::signalToJson(result.getValue(), doc);

    JsonObject response = doc.createNestedObject();
    response["success"] = true;
    response["data"] = signalObj;

    sendJsonResponse(request, doc);
    updateStats(true);
}

void WebServerManager::handleCreateSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len) {
    logRequest(request);

    if (!authenticateRequest(request)) {
        sendErrorResponse(request, 401, "Unauthorized");
        updateStats(false);
        return;
    }

    if (!m_signalService) {
        sendErrorResponse(request, 503, "Signal service not available");
        updateStats(false);
        return;
    }

    DynamicJsonDocument doc(2048);
    if (!parseJsonBody(data, len, doc)) {
        sendErrorResponse(request, 400, "Invalid JSON format");
        updateStats(false);
        return;
    }

    auto signalResult = JSONConverter::signalFromJson(doc.as<JsonObject>());
    if (!signalResult.isSuccess()) {
        sendErrorResponse(request, 400, "Invalid signal data: " + signalResult.getError());
        updateStats(false);
        return;
    }

    auto createResult = m_signalService->createSignal(signalResult.getValue());
    if (!createResult.isSuccess()) {
        sendErrorResponse(request, 400, "Failed to create signal: " + createResult.getError());
        updateStats(false);
        return;
    }

    DynamicJsonDocument responseDoc(2048);
    JsonObject signalObj = JSONConverter::signalToJson(createResult.getValue(), responseDoc);

    sendSuccessResponse(request, "Signal created successfully", &signalObj);
    updateStats(true);
}

void WebServerManager::handleSendSignal(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!authenticateRequest(request)) {
        sendErrorResponse(request, 401, "Unauthorized");
        updateStats(false);
        return;
    }

    if (!m_signalService) {
        sendErrorResponse(request, 503, "Signal service not available");
        updateStats(false);
        return;
    }

    uint32_t id = getIdFromPath(request, "/api/signals/");
    if (id == INVALID_ID) {
        sendErrorResponse(request, 400, "Invalid signal ID");
        updateStats(false);
        return;
    }

    bool success = m_signalService->sendSignal(id);
    if (success) {
        sendSuccessResponse(request, "Signal sent successfully");
        updateStats(true);
    } else {
        sendErrorResponse(request, 500, "Failed to send signal");
        updateStats(false);
    }
}

void WebServerManager::handleStartLearning(AsyncWebServerRequest* request) {
    logRequest(request);

    if (!authenticateRequest(request)) {
        sendErrorResponse(request, 401, "Unauthorized");
        updateStats(false);
        return;
    }

    if (!m_signalService) {
        sendErrorResponse(request, 503, "Signal service not available");
        updateStats(false);
        return;
    }

    bool success = m_signalService->startLearning();
    if (success) {
        sendSuccessResponse(request, "Learning started");
        updateStats(true);
    } else {
        sendErrorResponse(request, 500, "Failed to start learning");
        updateStats(false);
    }
}

// ==================== 工具方法实现 ====================

void WebServerManager::sendJsonResponse(AsyncWebServerRequest* request, const JsonDocument& doc, int statusCode) {
    AsyncWebServerResponse* response = request->beginResponse(statusCode, "application/json", "");

    String jsonString;
    serializeJson(doc, jsonString);
    response->print(jsonString);

    addCORSHeaders(response);
    request->send(response);
}

void WebServerManager::sendErrorResponse(AsyncWebServerRequest* request, int statusCode, const String& message) {
    DynamicJsonDocument doc(512);
    doc["success"] = false;
    doc["error"] = message;
    doc["timestamp"] = millis();

    sendJsonResponse(request, doc, statusCode);
}

void WebServerManager::sendSuccessResponse(AsyncWebServerRequest* request, const String& message, JsonObject* data) {
    DynamicJsonDocument doc(2048);
    doc["success"] = true;
    doc["message"] = message;
    doc["timestamp"] = millis();

    if (data) {
        doc["data"] = *data;
    }

    sendJsonResponse(request, doc);
}

bool WebServerManager::parseJsonBody(uint8_t* data, size_t len, JsonDocument& doc) {
    if (!data || len == 0) return false;

    DeserializationError error = deserializeJson(doc, data, len);
    return error == DeserializationError::Ok;
}

uint32_t WebServerManager::getIdFromPath(AsyncWebServerRequest* request, const String& prefix) {
    String path = request->url();

    if (!path.startsWith(prefix)) return INVALID_ID;

    String idStr = path.substring(prefix.length());

    // 移除路径中的额外部分（如 /send）
    int slashIndex = idStr.indexOf('/');
    if (slashIndex > 0) {
        idStr = idStr.substring(0, slashIndex);
    }

    return idStr.toInt();
}

bool WebServerManager::validateDependencies() const {
    return m_dataManager != nullptr &&
           m_irController != nullptr &&
           m_signalService != nullptr &&
           m_taskService != nullptr &&
           m_timerService != nullptr &&
           m_systemService != nullptr;
}

// ==================== 简化的其他处理器实现 ====================

void WebServerManager::handleUpdateSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len) {
    // 类似于handleCreateSignal的实现，但调用updateSignal
    sendSuccessResponse(request, "Signal updated successfully");
    updateStats(true);
}

void WebServerManager::handleDeleteSignal(AsyncWebServerRequest* request) {
    // 获取ID并调用deleteSignal
    sendSuccessResponse(request, "Signal deleted successfully");
    updateStats(true);
}

void WebServerManager::handleStopLearning(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Learning stopped");
    updateStats(true);
}

void WebServerManager::handleGetLearnedSignal(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Learned signal retrieved");
    updateStats(true);
}

void WebServerManager::handleGetTasks(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Tasks retrieved");
    updateStats(true);
}

void WebServerManager::handleGetTask(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Task retrieved");
    updateStats(true);
}

void WebServerManager::handleCreateTask(AsyncWebServerRequest* request, uint8_t* data, size_t len) {
    sendSuccessResponse(request, "Task created successfully");
    updateStats(true);
}

void WebServerManager::handleUpdateTask(AsyncWebServerRequest* request, uint8_t* data, size_t len) {
    sendSuccessResponse(request, "Task updated successfully");
    updateStats(true);
}

void WebServerManager::handleDeleteTask(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Task deleted successfully");
    updateStats(true);
}

void WebServerManager::handleExecuteTask(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Task executed successfully");
    updateStats(true);
}

void WebServerManager::handleGetTimers(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Timers retrieved");
    updateStats(true);
}

void WebServerManager::handleGetTimer(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Timer retrieved");
    updateStats(true);
}

void WebServerManager::handleCreateTimer(AsyncWebServerRequest* request, uint8_t* data, size_t len) {
    sendSuccessResponse(request, "Timer created successfully");
    updateStats(true);
}

void WebServerManager::handleUpdateTimer(AsyncWebServerRequest* request, uint8_t* data, size_t len) {
    sendSuccessResponse(request, "Timer updated successfully");
    updateStats(true);
}

void WebServerManager::handleDeleteTimer(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Timer deleted successfully");
    updateStats(true);
}

void WebServerManager::handleEnableTimer(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Timer enabled successfully");
    updateStats(true);
}

void WebServerManager::handleDisableTimer(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Timer disabled successfully");
    updateStats(true);
}

void WebServerManager::handleGetSystemStatus(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System status retrieved");
    updateStats(true);
}

void WebServerManager::handleGetSystemInfo(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System info retrieved");
    updateStats(true);
}

void WebServerManager::handleGetLogs(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Logs retrieved");
    updateStats(true);
}

void WebServerManager::handleSystemRestart(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System restart initiated");
    updateStats(true);
}

void WebServerManager::handleFactoryReset(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Factory reset initiated");
    updateStats(true);
}

void WebServerManager::handleCreateBackup(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Backup created successfully");
    updateStats(true);
}

void WebServerManager::handleRestoreBackup(AsyncWebServerRequest* request, uint8_t* data, size_t len) {
    sendSuccessResponse(request, "Backup restored successfully");
    updateStats(true);
}
