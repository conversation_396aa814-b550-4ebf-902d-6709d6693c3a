#pragma once

#include "core/DataStructures.h"
#include "data/DataManager.h"
#include "hardware/IRController.h"
#include "network/WSManager.h"
#include "services/SignalService.h"
#include "services/TaskService.h"
#include "services/TimerService.h"
#include "services/SystemService.h"
#include <ESPAsyncWebServer.h>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - Web服务器管理器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：Web服务器管理器
 */

class WebServerManager {
public:
    struct ServerConfig {
        uint16_t port;
        bool enableCORS;
        bool enableAuthentication;
        String authToken;
        bool enableStaticFiles;
        String staticPath;
        uint32_t maxConnections;
        
        ServerConfig() : port(80), enableCORS(true), enableAuthentication(false),
                        authToken(""), enableStaticFiles(true), staticPath("/www"),
                        maxConnections(10) {}
    };
    
    struct ServerStats {
        uint32_t totalRequests;
        uint32_t successfulRequests;
        uint32_t failedRequests;
        uint32_t authFailures;
        Timestamp lastRequest;
        Timestamp startTime;
        
        ServerStats() : totalRequests(0), successfulRequests(0), failedRequests(0),
                       authFailures(0), lastRequest(0), startTime(0) {}
    };

private:
    // 核心组件
    AsyncWebServer* m_server;
    DataManager* m_dataManager;
    IRController* m_irController;
    WSManager* m_wsManager;
    
    // 业务服务
    SignalService* m_signalService;
    TaskService* m_taskService;
    TimerService* m_timerService;
    SystemService* m_systemService;
    
    // 配置和状态
    ServerConfig m_config;
    bool m_initialized;
    ServerStats m_stats;
    
    // 线程安全
    mutable std::mutex m_mutex;

public:
    WebServerManager();
    ~WebServerManager();
    
    // 生命周期管理
    bool initialize(const ServerConfig& config = ServerConfig());
    void cleanup();
    bool isInitialized() const { return m_initialized; }
    
    // 依赖注入
    void setDataManager(DataManager* dataManager);
    void setIRController(IRController* irController);
    void setWSManager(WSManager* wsManager);
    void setSignalService(SignalService* signalService);
    void setTaskService(TaskService* taskService);
    void setTimerService(TimerService* timerService);
    void setSystemService(SystemService* systemService);
    
    // 服务器控制
    bool startServer();
    bool stopServer();
    bool restartServer();
    
    // 统计信息
    const ServerStats& getStatistics() const { return m_stats; }
    JsonObject getDetailedStatistics(JsonDocument& doc) const;
    
    // 配置管理
    bool updateConfig(const ServerConfig& config);
    const ServerConfig& getConfig() const { return m_config; }

private:
    // 路由设置
    void setupRoutes();
    void setupSignalRoutes();
    void setupTaskRoutes();
    void setupTimerRoutes();
    void setupSystemRoutes();
    void setupStaticRoutes();
    
    // 中间件
    bool authenticateRequest(AsyncWebServerRequest* request);
    void addCORSHeaders(AsyncWebServerResponse* response);
    void logRequest(AsyncWebServerRequest* request);
    void updateStats(bool success);
    
    // API处理器 - 信号管理
    void handleGetSignals(AsyncWebServerRequest* request);
    void handleGetSignal(AsyncWebServerRequest* request);
    void handleCreateSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len);
    void handleUpdateSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len);
    void handleDeleteSignal(AsyncWebServerRequest* request);
    void handleSendSignal(AsyncWebServerRequest* request);
    void handleStartLearning(AsyncWebServerRequest* request);
    void handleStopLearning(AsyncWebServerRequest* request);
    void handleGetLearnedSignal(AsyncWebServerRequest* request);
    
    // API处理器 - 任务管理
    void handleGetTasks(AsyncWebServerRequest* request);
    void handleGetTask(AsyncWebServerRequest* request);
    void handleCreateTask(AsyncWebServerRequest* request, uint8_t* data, size_t len);
    void handleUpdateTask(AsyncWebServerRequest* request, uint8_t* data, size_t len);
    void handleDeleteTask(AsyncWebServerRequest* request);
    void handleExecuteTask(AsyncWebServerRequest* request);
    
    // API处理器 - 定时器管理
    void handleGetTimers(AsyncWebServerRequest* request);
    void handleGetTimer(AsyncWebServerRequest* request);
    void handleCreateTimer(AsyncWebServerRequest* request, uint8_t* data, size_t len);
    void handleUpdateTimer(AsyncWebServerRequest* request, uint8_t* data, size_t len);
    void handleDeleteTimer(AsyncWebServerRequest* request);
    void handleEnableTimer(AsyncWebServerRequest* request);
    void handleDisableTimer(AsyncWebServerRequest* request);
    
    // API处理器 - 系统管理
    void handleGetSystemStatus(AsyncWebServerRequest* request);
    void handleGetSystemInfo(AsyncWebServerRequest* request);
    void handleGetLogs(AsyncWebServerRequest* request);
    void handleSystemRestart(AsyncWebServerRequest* request);
    void handleFactoryReset(AsyncWebServerRequest* request);
    void handleCreateBackup(AsyncWebServerRequest* request);
    void handleRestoreBackup(AsyncWebServerRequest* request, uint8_t* data, size_t len);
    
    // 工具方法
    void sendJsonResponse(AsyncWebServerRequest* request, const JsonDocument& doc, int statusCode = 200);
    void sendErrorResponse(AsyncWebServerRequest* request, int statusCode, const String& message);
    void sendSuccessResponse(AsyncWebServerRequest* request, const String& message, JsonObject* data = nullptr);
    bool parseJsonBody(uint8_t* data, size_t len, JsonDocument& doc);
    uint32_t getIdFromPath(AsyncWebServerRequest* request, const String& prefix);
    bool validateDependencies() const;
};
