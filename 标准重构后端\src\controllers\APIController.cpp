#include "APIController.h"
#include "../config/system-config.h"
#include <map>

/**
 * ESP32-S3 红外控制系统 - 基础API控制器实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五阶段：API控制器层
 */

// 静态成员初始化
std::map<String, std::pair<uint32_t, Timestamp>> APIController::s_rateLimitMap;
std::mutex APIController::s_rateLimitMutex;

// ==================== 统计信息方法 ====================

JsonObject APIController::getDetailedStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    JsonObject stats = doc.createNestedObject("api_controller_statistics");
    stats["controller_name"] = getControllerName();
    stats["total_requests"] = m_stats.totalRequests;
    stats["successful_requests"] = m_stats.successfulRequests;
    stats["failed_requests"] = m_stats.failedRequests;
    stats["validation_errors"] = m_stats.validationErrors;
    stats["authentication_errors"] = m_stats.authenticationErrors;
    stats["last_request"] = m_stats.lastRequest;
    
    // 计算成功率
    if (m_stats.totalRequests > 0) {
        float successRate = (float)m_stats.successfulRequests / m_stats.totalRequests * 100.0f;
        stats["success_rate"] = successRate;
    } else {
        stats["success_rate"] = 0.0f;
    }
    
    return stats;
}

// ==================== 响应处理方法实现 ====================

void APIController::sendSuccessResponse(AsyncWebServerRequest* request, const String& message, 
                                       JsonObject* data, StatusCode code) {
    DynamicJsonDocument doc(2048);
    doc["success"] = true;
    doc["message"] = message;
    doc["timestamp"] = millis();
    doc["status_code"] = static_cast<uint16_t>(code);
    
    if (data) {
        doc["data"] = *data;
    }
    
    sendJsonResponse(request, doc, code);
    updateStats(true);
}

void APIController::sendErrorResponse(AsyncWebServerRequest* request, const String& message, StatusCode code) {
    DynamicJsonDocument doc(1024);
    doc["success"] = false;
    doc["error"] = message;
    doc["timestamp"] = millis();
    doc["status_code"] = static_cast<uint16_t>(code);
    doc["error_id"] = generateErrorId();
    
    sendJsonResponse(request, doc, code);
    updateStats(false);
    logError(request, message, code);
}

void APIController::sendJsonResponse(AsyncWebServerRequest* request, const JsonDocument& doc, StatusCode code) {
    String jsonString;
    serializeJson(doc, jsonString);
    
    AsyncWebServerResponse* response = request->beginResponse(static_cast<int>(code), "application/json", jsonString);
    addCORSHeaders(response);
    addNoCacheHeaders(response);
    
    request->send(response);
    logRequest(request, code);
}

void APIController::sendPaginatedResponse(AsyncWebServerRequest* request, const JsonArray& data, 
                                         uint32_t total, const PaginationParams& pagination) {
    DynamicJsonDocument doc(4096);
    doc["success"] = true;
    doc["data"] = data;
    doc["timestamp"] = millis();
    
    JsonObject paginationInfo = doc.createNestedObject("pagination");
    paginationInfo["page"] = pagination.page;
    paginationInfo["limit"] = pagination.limit;
    paginationInfo["total"] = total;
    paginationInfo["total_pages"] = (total + pagination.limit - 1) / pagination.limit;
    paginationInfo["has_next"] = (pagination.page * pagination.limit) < total;
    paginationInfo["has_prev"] = pagination.page > 1;
    
    sendJsonResponse(request, doc);
}

// ==================== 请求验证方法实现 ====================

APIController::ValidationResult APIController::validateRequest(AsyncWebServerRequest* request) {
    // 检查请求大小
    auto sizeResult = validateRequestSize(request);
    if (!sizeResult.isValid) {
        return sizeResult;
    }
    
    // 检查速率限制
    auto rateLimitResult = checkRateLimit(request);
    if (!rateLimitResult.isValid) {
        return rateLimitResult;
    }
    
    // 检查认证（如果启用）
    if (SecurityConfig::ENABLE_AUTHENTICATION) {
        auto authResult = validateAuthToken(request);
        if (!authResult.isValid) {
            return authResult;
        }
    }
    
    return ValidationResult(true);
}

APIController::ValidationResult APIController::validateJsonBody(const String& body, JsonDocument& doc) {
    if (body.isEmpty()) {
        return ValidationResult("Request body is empty", StatusCode::BAD_REQUEST);
    }
    
    DeserializationError error = deserializeJson(doc, body);
    if (error) {
        String errorMsg = "Invalid JSON format: ";
        errorMsg += error.c_str();
        return ValidationResult(errorMsg, StatusCode::BAD_REQUEST);
    }
    
    return ValidationResult(true);
}

APIController::ValidationResult APIController::validateRequiredParams(AsyncWebServerRequest* request, 
                                                                      const std::vector<String>& requiredParams) {
    for (const String& param : requiredParams) {
        if (!request->hasParam(param)) {
            String errorMsg = "Missing required parameter: " + param;
            return ValidationResult(errorMsg, StatusCode::BAD_REQUEST);
        }
        
        String value = request->getParam(param)->value();
        if (value.isEmpty()) {
            String errorMsg = "Parameter '" + param + "' cannot be empty";
            return ValidationResult(errorMsg, StatusCode::BAD_REQUEST);
        }
    }
    
    return ValidationResult(true);
}

APIController::ValidationResult APIController::validateIdParameter(AsyncWebServerRequest* request, const String& paramName) {
    if (!request->hasParam(paramName)) {
        String errorMsg = "Missing " + paramName + " parameter";
        return ValidationResult(errorMsg, StatusCode::BAD_REQUEST);
    }
    
    String idStr = request->getParam(paramName)->value();
    if (idStr.isEmpty()) {
        String errorMsg = paramName + " parameter cannot be empty";
        return ValidationResult(errorMsg, StatusCode::BAD_REQUEST);
    }
    
    // 验证ID格式（数字）
    for (char c : idStr) {
        if (!isDigit(c)) {
            String errorMsg = "Invalid " + paramName + " format. Must be a number";
            return ValidationResult(errorMsg, StatusCode::BAD_REQUEST);
        }
    }
    
    uint32_t id = idStr.toInt();
    if (id == 0 || id == INVALID_ID) {
        String errorMsg = "Invalid " + paramName + " value";
        return ValidationResult(errorMsg, StatusCode::BAD_REQUEST);
    }
    
    return ValidationResult(true);
}

APIController::ValidationResult APIController::validatePaginationParams(AsyncWebServerRequest* request, PaginationParams& params) {
    // 获取页码
    String pageStr = getQueryParam(request, "page", "1");
    params.page = pageStr.toInt();
    if (params.page < 1) {
        params.page = 1;
    }
    
    // 获取每页数量
    String limitStr = getQueryParam(request, "limit", "20");
    params.limit = limitStr.toInt();
    if (params.limit < 1) {
        params.limit = 20;
    } else if (params.limit > 100) {
        params.limit = 100; // 限制最大每页数量
    }
    
    // 计算偏移量
    params.offset = (params.page - 1) * params.limit;
    
    return ValidationResult(true);
}

APIController::ValidationResult APIController::validateSortParams(AsyncWebServerRequest* request, SortParams& params) {
    params.field = getQueryParam(request, "sort", "");
    String orderStr = getQueryParam(request, "order", "asc");
    params.ascending = (orderStr.toLowerCase() != "desc");
    
    // 验证排序字段（如果提供）
    if (!params.field.isEmpty()) {
        // 这里可以添加字段白名单验证
        if (params.field.length() > 50) {
            return ValidationResult("Sort field name too long", StatusCode::BAD_REQUEST);
        }
    }
    
    return ValidationResult(true);
}

// ==================== 参数提取方法实现 ====================

uint32_t APIController::extractIdFromPath(AsyncWebServerRequest* request, const String& prefix) {
    String path = request->url();
    
    if (!path.startsWith(prefix)) {
        return INVALID_ID;
    }
    
    String idStr = path.substring(prefix.length());
    
    // 移除路径中的额外部分（如 /action）
    int slashIndex = idStr.indexOf('/');
    if (slashIndex > 0) {
        idStr = idStr.substring(0, slashIndex);
    }
    
    // 移除查询参数
    int questionIndex = idStr.indexOf('?');
    if (questionIndex > 0) {
        idStr = idStr.substring(0, questionIndex);
    }
    
    return idStr.toInt();
}

String APIController::getQueryParam(AsyncWebServerRequest* request, const String& name, const String& defaultValue) {
    if (request->hasParam(name)) {
        return request->getParam(name)->value();
    }
    return defaultValue;
}

int32_t APIController::getIntQueryParam(AsyncWebServerRequest* request, const String& name, int32_t defaultValue) {
    String value = getQueryParam(request, name, String(defaultValue));
    return value.toInt();
}

bool APIController::getBoolQueryParam(AsyncWebServerRequest* request, const String& name, bool defaultValue) {
    String value = getQueryParam(request, name, defaultValue ? "true" : "false");
    value.toLowerCase();
    return (value == "true" || value == "1" || value == "yes");
}

String APIController::getRequestBody(AsyncWebServerRequest* request) {
    // 这个方法在实际使用中需要通过onBody回调来获取请求体
    // 这里返回空字符串，具体实现在子类中处理
    return "";
}

// ==================== 认证和授权方法实现 ====================

APIController::ValidationResult APIController::validateAuthToken(AsyncWebServerRequest* request) {
    if (!SecurityConfig::ENABLE_AUTHENTICATION) {
        return ValidationResult(true);
    }
    
    String authHeader = "";
    if (request->hasHeader("Authorization")) {
        authHeader = request->getHeader("Authorization")->value();
    }
    
    if (authHeader.isEmpty()) {
        updateStats(false, false, true);
        return ValidationResult("Missing Authorization header", StatusCode::UNAUTHORIZED);
    }
    
    // 简单的Bearer token验证
    if (!authHeader.startsWith("Bearer ")) {
        updateStats(false, false, true);
        return ValidationResult("Invalid Authorization format", StatusCode::UNAUTHORIZED);
    }
    
    String token = authHeader.substring(7); // 移除 "Bearer "
    if (token.isEmpty()) {
        updateStats(false, false, true);
        return ValidationResult("Empty authorization token", StatusCode::UNAUTHORIZED);
    }
    
    // 这里应该验证token的有效性
    // 简化实现：检查token长度
    if (token.length() < 10) {
        updateStats(false, false, true);
        return ValidationResult("Invalid authorization token", StatusCode::UNAUTHORIZED);
    }
    
    return ValidationResult(true);
}

APIController::ValidationResult APIController::checkPermission(AsyncWebServerRequest* request, const String& permission) {
    // 简化实现：所有认证用户都有权限
    return validateAuthToken(request);
}

String APIController::getClientIP(AsyncWebServerRequest* request) {
    // 检查X-Forwarded-For头（代理情况）
    if (request->hasHeader("X-Forwarded-For")) {
        String forwardedFor = request->getHeader("X-Forwarded-For")->value();
        int commaIndex = forwardedFor.indexOf(',');
        if (commaIndex > 0) {
            return forwardedFor.substring(0, commaIndex);
        }
        return forwardedFor;
    }
    
    // 检查X-Real-IP头
    if (request->hasHeader("X-Real-IP")) {
        return request->getHeader("X-Real-IP")->value();
    }
    
    // 返回直接连接的IP
    return request->client()->remoteIP().toString();
}

// ==================== 速率限制方法实现 ====================

APIController::ValidationResult APIController::checkRateLimit(AsyncWebServerRequest* request) {
    if (!SecurityConfig::ENABLE_RATE_LIMITING) {
        return ValidationResult(true);
    }

    String clientIP = getClientIP(request);
    Timestamp currentTime = millis();

    std::lock_guard<std::mutex> lock(s_rateLimitMutex);

    // 清理过期记录
    cleanupRateLimitMap();

    auto it = s_rateLimitMap.find(clientIP);
    if (it != s_rateLimitMap.end()) {
        uint32_t requestCount = it->second.first;
        Timestamp windowStart = it->second.second;

        // 检查是否在同一个时间窗口内
        if (currentTime - windowStart < SecurityConfig::RATE_LIMIT_WINDOW_MS) {
            if (requestCount >= SecurityConfig::MAX_REQUESTS_PER_MINUTE) {
                return ValidationResult("Rate limit exceeded", StatusCode::TOO_MANY_REQUESTS);
            }
            // 增加请求计数
            it->second.first++;
        } else {
            // 新的时间窗口，重置计数
            it->second.first = 1;
            it->second.second = currentTime;
        }
    } else {
        // 新客户端
        s_rateLimitMap[clientIP] = std::make_pair(1, currentTime);
    }

    return ValidationResult(true);
}

void APIController::updateRateLimit(const String& clientIP) {
    // 这个方法在checkRateLimit中已经处理了更新逻辑
}

void APIController::cleanupRateLimitMap() {
    Timestamp currentTime = millis();
    auto it = s_rateLimitMap.begin();

    while (it != s_rateLimitMap.end()) {
        if (currentTime - it->second.second > SecurityConfig::RATE_LIMIT_WINDOW_MS) {
            it = s_rateLimitMap.erase(it);
        } else {
            ++it;
        }
    }
}

// ==================== CORS处理方法实现 ====================

void APIController::addCORSHeaders(AsyncWebServerResponse* response) {
    if (!SecurityConfig::ENABLE_CORS) {
        return;
    }

    response->addHeader("Access-Control-Allow-Origin", "*");
    response->addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response->addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
    response->addHeader("Access-Control-Max-Age", "86400");
    response->addHeader("Access-Control-Allow-Credentials", "false");
}

void APIController::handleOptionsRequest(AsyncWebServerRequest* request) {
    AsyncWebServerResponse* response = request->beginResponse(200);
    addCORSHeaders(response);
    request->send(response);
}

// ==================== 日志和监控方法实现 ====================

void APIController::logRequest(AsyncWebServerRequest* request, StatusCode statusCode) {
    String clientIP = getClientIP(request);
    String method = request->methodToString();
    String url = request->url();
    uint16_t code = static_cast<uint16_t>(statusCode);

    Serial.printf("[%lu] %s %s %s -> %d\n", millis(), clientIP.c_str(), method, url.c_str(), code);
}

void APIController::logError(AsyncWebServerRequest* request, const String& error, StatusCode statusCode) {
    String clientIP = getClientIP(request);
    String method = request->methodToString();
    String url = request->url();
    uint16_t code = static_cast<uint16_t>(statusCode);

    Serial.printf("[ERROR][%lu] %s %s %s -> %d: %s\n",
                  millis(), clientIP.c_str(), method, url.c_str(), code, error.c_str());
}

void APIController::updateStats(bool success, bool validationError, bool authError) {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_stats.totalRequests++;
    m_stats.lastRequest = millis();

    if (success) {
        m_stats.successfulRequests++;
    } else {
        m_stats.failedRequests++;

        if (validationError) {
            m_stats.validationErrors++;
        }

        if (authError) {
            m_stats.authenticationErrors++;
        }
    }
}

// ==================== 工具方法实现 ====================

String APIController::statusCodeToString(StatusCode code) const {
    switch (code) {
        case StatusCode::OK: return "OK";
        case StatusCode::CREATED: return "Created";
        case StatusCode::ACCEPTED: return "Accepted";
        case StatusCode::NO_CONTENT: return "No Content";
        case StatusCode::BAD_REQUEST: return "Bad Request";
        case StatusCode::UNAUTHORIZED: return "Unauthorized";
        case StatusCode::FORBIDDEN: return "Forbidden";
        case StatusCode::NOT_FOUND: return "Not Found";
        case StatusCode::METHOD_NOT_ALLOWED: return "Method Not Allowed";
        case StatusCode::CONFLICT: return "Conflict";
        case StatusCode::UNPROCESSABLE_ENTITY: return "Unprocessable Entity";
        case StatusCode::TOO_MANY_REQUESTS: return "Too Many Requests";
        case StatusCode::INTERNAL_SERVER_ERROR: return "Internal Server Error";
        case StatusCode::NOT_IMPLEMENTED: return "Not Implemented";
        case StatusCode::SERVICE_UNAVAILABLE: return "Service Unavailable";
        default: return "Unknown";
    }
}

bool APIController::isJsonContentType(AsyncWebServerRequest* request) {
    if (!request->hasHeader("Content-Type")) {
        return false;
    }

    String contentType = request->getHeader("Content-Type")->value();
    contentType.toLowerCase();
    return contentType.indexOf("application/json") >= 0;
}

String APIController::generateErrorId() const {
    return String(millis()) + "_" + String(random(1000, 9999));
}

bool APIController::isValidHttpMethod(AsyncWebServerRequest* request, WebRequestMethod expectedMethod) {
    return request->method() == expectedMethod;
}

APIController::ValidationResult APIController::validateRequestSize(AsyncWebServerRequest* request, size_t maxSize) {
    if (request->hasHeader("Content-Length")) {
        String lengthStr = request->getHeader("Content-Length")->value();
        size_t contentLength = lengthStr.toInt();

        if (contentLength > maxSize) {
            String errorMsg = "Request too large. Maximum size: " + String(maxSize) + " bytes";
            return ValidationResult(errorMsg, StatusCode::UNPROCESSABLE_ENTITY);
        }
    }

    return ValidationResult(true);
}

// ==================== 缓存控制方法实现 ====================

void APIController::addCacheHeaders(AsyncWebServerResponse* response, uint32_t maxAge) {
    if (maxAge > 0) {
        response->addHeader("Cache-Control", "public, max-age=" + String(maxAge));
        response->addHeader("Expires", String(millis() + maxAge * 1000));
    } else {
        addNoCacheHeaders(response);
    }
}

void APIController::addNoCacheHeaders(AsyncWebServerResponse* response) {
    response->addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response->addHeader("Pragma", "no-cache");
    response->addHeader("Expires", "0");
}

bool APIController::checkIfModifiedSince(AsyncWebServerRequest* request, Timestamp lastModified) {
    if (!request->hasHeader("If-Modified-Since")) {
        return true; // 没有If-Modified-Since头，需要返回内容
    }

    // 简化实现：总是返回内容
    return true;
}

// ==================== 内容协商方法实现 ====================

bool APIController::acceptsJson(AsyncWebServerRequest* request) {
    if (!request->hasHeader("Accept")) {
        return true; // 默认接受JSON
    }

    String accept = request->getHeader("Accept")->value();
    accept.toLowerCase();
    return (accept.indexOf("application/json") >= 0 || accept.indexOf("*/*") >= 0);
}

String APIController::getPreferredLanguage(AsyncWebServerRequest* request) {
    if (request->hasHeader("Accept-Language")) {
        String acceptLang = request->getHeader("Accept-Language")->value();
        // 简化实现：返回第一个语言
        int commaIndex = acceptLang.indexOf(',');
        if (commaIndex > 0) {
            return acceptLang.substring(0, commaIndex);
        }
        return acceptLang;
    }
    return "en"; // 默认英语
}

// ==================== 安全方法实现 ====================

String APIController::sanitizeInput(const String& input) {
    String sanitized = input;

    // 移除潜在的危险字符
    sanitized.replace("<", "&lt;");
    sanitized.replace(">", "&gt;");
    sanitized.replace("\"", "&quot;");
    sanitized.replace("'", "&#x27;");
    sanitized.replace("&", "&amp;");

    return sanitized;
}

bool APIController::isValidFilename(const String& filename) {
    if (filename.isEmpty() || filename.length() > 255) {
        return false;
    }

    // 检查非法字符
    String invalidChars = "<>:\"/\\|?*";
    for (char c : invalidChars) {
        if (filename.indexOf(c) >= 0) {
            return false;
        }
    }

    // 检查保留名称
    String reservedNames[] = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "LPT1", "LPT2"};
    String upperFilename = filename;
    upperFilename.toUpperCase();

    for (const String& reserved : reservedNames) {
        if (upperFilename == reserved) {
            return false;
        }
    }

    return true;
}

bool APIController::containsSQLInjection(const String& input) {
    String lowerInput = input;
    lowerInput.toLowerCase();

    // 简单的SQL注入检测
    String sqlKeywords[] = {"select", "insert", "update", "delete", "drop", "union", "exec", "script"};

    for (const String& keyword : sqlKeywords) {
        if (lowerInput.indexOf(keyword) >= 0) {
            return true;
        }
    }

    return false;
}

bool APIController::containsXSS(const String& input) {
    String lowerInput = input;
    lowerInput.toLowerCase();

    // 简单的XSS检测
    return (lowerInput.indexOf("<script") >= 0 ||
            lowerInput.indexOf("javascript:") >= 0 ||
            lowerInput.indexOf("onload=") >= 0 ||
            lowerInput.indexOf("onerror=") >= 0);
}
