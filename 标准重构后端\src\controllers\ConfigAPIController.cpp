#include "ConfigAPIController.h"
#include "../config/system-config.h"
#include "../config/hardware-config.h"
#include <WiFi.h>

/**
 * ESP32-S3 红外控制系统 - 配置API控制器实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：配置管理API (5个接口)
 */

ConfigAPIController::ConfigAPIController() {
    // 初始化配置缓存
    updateConfigCache();
}

// ==================== 路由注册实现 ====================

void ConfigAPIController::registerRoutes(AsyncWebServer* server) {
    if (!server) return;
    
    // ==================== 配置管理API路由 ====================
    
    // GET /api/config - 获取配置
    server->on("/api/config", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetConfig(request);
    });
    
    // POST /api/config - 更新配置
    server->on("/api/config", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleUpdateConfigBody(request, data, len, index, total);
            }
        }
    );
    
    // GET /api/config/export - 导出配置
    server->on("/api/config/export", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleExportConfig(request);
    });
    
    // POST /api/config/import - 导入配置
    server->on("/api/config/import", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleImportConfigBody(request, data, len, index, total);
            }
        }
    );
    
    // POST /api/config/reset - 重置配置
    server->on("/api/config/reset", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleResetConfigBody(request, data, len, index, total);
            }
        }
    );
    
    // ==================== 配置分类管理路由 ====================
    
    // GET /api/config/system - 获取系统配置
    server->on("/api/config/system", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemConfig(request);
    });
    
    // POST /api/config/system - 更新系统配置
    server->on("/api/config/system", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleUpdateSystemConfigBody(request, data, len, index, total);
            }
        }
    );
    
    // GET /api/config/network - 获取网络配置
    server->on("/api/config/network", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetNetworkConfig(request);
    });
    
    // POST /api/config/network - 更新网络配置
    server->on("/api/config/network", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleUpdateNetworkConfigBody(request, data, len, index, total);
            }
        }
    );
    
    // GET /api/config/hardware - 获取硬件配置
    server->on("/api/config/hardware", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetHardwareConfig(request);
    });
    
    // POST /api/config/hardware - 更新硬件配置
    server->on("/api/config/hardware", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleUpdateHardwareConfigBody(request, data, len, index, total);
            }
        }
    );
    
    // ==================== 配置备份和恢复路由 ====================
    
    // POST /api/config/backup - 创建配置备份
    server->on("/api/config/backup", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleCreateConfigBackup(request);
    });
    
    // GET /api/config/backup/list - 获取备份列表
    server->on("/api/config/backup/list", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetBackupList(request);
    });
    
    // POST /api/config/restore - 恢复配置备份
    server->on("/api/config/restore", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleRestoreConfigBackupBody(request, data, len, index, total);
            }
        }
    );
    
    // DELETE /api/config/backup/{id} - 删除配置备份
    server->on("^\\/api\\/config\\/backup\\/([0-9]+)$", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        handleDeleteConfigBackup(request);
    });
}

// ==================== 配置管理API接口实现 ====================

void ConfigAPIController::handleGetConfig(AsyncWebServerRequest* request) {
    logRequest(request);
    
    auto validationResult = validateRequest(request);
    if (!validationResult.isValid) {
        sendErrorResponse(request, validationResult.errorMessage, validationResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        // 获取查询参数
        String configType = getQueryParam(request, "type", "all");
        bool includeDefaults = getBoolQueryParam(request, "include_defaults", false);
        
        // 格式化配置响应
        DynamicJsonDocument doc(8192);
        JsonObject configObj;
        
        if (configType == "all") {
            configObj = formatCompleteConfigToJson(doc);
        } else if (configType == "system") {
            configObj = formatSystemConfigToJson(doc);
        } else if (configType == "network") {
            configObj = formatNetworkConfigToJson(doc);
        } else if (configType == "hardware") {
            configObj = formatHardwareConfigToJson(doc);
        } else if (configType == "user") {
            configObj = formatUserConfigToJson(doc);
        } else {
            sendErrorResponse(request, "Invalid config type: " + configType, StatusCode::BAD_REQUEST);
            updateStats(false, true);
            return;
        }
        
        sendSuccessResponse(request, "Configuration retrieved successfully", &configObj);
        updateStats(true);
        updateConfigStats("read");
        
    } catch (const std::exception& e) {
        sendErrorResponse(request, "Failed to retrieve configuration: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
    }
}

void ConfigAPIController::handleUpdateConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    logRequest(request);
    
    auto validationResult = validateRequest(request);
    if (!validationResult.isValid) {
        sendErrorResponse(request, validationResult.errorMessage, validationResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    // 验证配置权限
    auto permissionResult = validateConfigPermission(request, "update");
    if (!permissionResult.isValid) {
        sendErrorResponse(request, permissionResult.errorMessage, permissionResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    // 解析JSON请求体
    String body = String((char*)data, len);
    DynamicJsonDocument doc(4096);
    auto jsonResult = validateJsonBody(body, doc);
    if (!jsonResult.isValid) {
        sendErrorResponse(request, jsonResult.errorMessage, jsonResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    JsonObject configData = doc.as<JsonObject>();
    
    // 验证配置数据
    auto configValidation = validateConfigData(configData);
    if (!configValidation.isValid) {
        sendErrorResponse(request, configValidation.errorMessage, configValidation.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        bool success = true;
        String errorMessage = "";
        
        // 更新各类配置
        if (configData.containsKey("system")) {
            if (!saveSystemConfig(configData["system"].as<JsonObject>())) {
                success = false;
                errorMessage += "Failed to save system config. ";
            }
        }
        
        if (configData.containsKey("network")) {
            if (!saveNetworkConfig(configData["network"].as<JsonObject>())) {
                success = false;
                errorMessage += "Failed to save network config. ";
            }
        }
        
        if (configData.containsKey("hardware")) {
            if (!saveHardwareConfig(configData["hardware"].as<JsonObject>())) {
                success = false;
                errorMessage += "Failed to save hardware config. ";
            }
        }
        
        if (configData.containsKey("user")) {
            if (!saveUserConfig(configData["user"].as<JsonObject>())) {
                success = false;
                errorMessage += "Failed to save user config. ";
            }
        }
        
        if (success) {
            // 更新配置缓存
            invalidateConfigCache();
            updateConfigCache();
            
            // 更新配置版本
            updateConfigVersion();
            
            sendSuccessResponse(request, "Configuration updated successfully");
            updateStats(true);
            updateConfigStats("write");
            
            logConfigOperation("CONFIG_UPDATE", "Configuration updated successfully");
        } else {
            sendErrorResponse(request, "Failed to update configuration: " + errorMessage, 
                             StatusCode::INTERNAL_SERVER_ERROR);
            updateStats(false);
        }
        
    } catch (const std::exception& e) {
        sendErrorResponse(request, "Failed to update configuration: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
    }
}

// ==================== 配置验证和安全实现 ====================

APIController::ValidationResult ConfigAPIController::validateConfigData(const JsonObject& configData, const String& configType) {
    if (configData.isNull() || configData.size() == 0) {
        return ValidationResult("Configuration data is empty", StatusCode::BAD_REQUEST);
    }

    // 验证配置类型
    if (!configType.isEmpty()) {
        if (configType == "system") {
            return validateSystemConfig(configData);
        } else if (configType == "network") {
            return validateNetworkConfig(configData);
        } else if (configType == "hardware") {
            return validateHardwareConfig(configData);
        }
    }

    // 验证各个配置部分
    if (configData.containsKey("system")) {
        auto systemValidation = validateSystemConfig(configData["system"].as<JsonObject>());
        if (!systemValidation.isValid) {
            return systemValidation;
        }
    }

    if (configData.containsKey("network")) {
        auto networkValidation = validateNetworkConfig(configData["network"].as<JsonObject>());
        if (!networkValidation.isValid) {
            return networkValidation;
        }
    }

    if (configData.containsKey("hardware")) {
        auto hardwareValidation = validateHardwareConfig(configData["hardware"].as<JsonObject>());
        if (!hardwareValidation.isValid) {
            return hardwareValidation;
        }
    }

    return ValidationResult(true);
}

APIController::ValidationResult ConfigAPIController::validateSystemConfig(const JsonObject& systemConfig) {
    // 验证设备名称
    if (systemConfig.containsKey("device_name")) {
        String deviceName = systemConfig["device_name"].as<String>();
        if (deviceName.isEmpty() || deviceName.length() > 32) {
            return ValidationResult("Device name must be 1-32 characters", StatusCode::BAD_REQUEST);
        }
    }

    // 验证日志级别
    if (systemConfig.containsKey("log_level")) {
        String logLevel = systemConfig["log_level"].as<String>();
        if (logLevel != "DEBUG" && logLevel != "INFO" && logLevel != "WARN" && logLevel != "ERROR") {
            return ValidationResult("Invalid log level", StatusCode::BAD_REQUEST);
        }
    }

    return ValidationResult(true);
}

APIController::ValidationResult ConfigAPIController::validateNetworkConfig(const JsonObject& networkConfig) {
    // 验证WiFi配置
    if (networkConfig.containsKey("wifi")) {
        JsonObject wifiConfig = networkConfig["wifi"].as<JsonObject>();

        if (wifiConfig.containsKey("ssid")) {
            String ssid = wifiConfig["ssid"].as<String>();
            if (ssid.isEmpty() || ssid.length() > 32) {
                return ValidationResult("WiFi SSID must be 1-32 characters", StatusCode::BAD_REQUEST);
            }
        }

        if (wifiConfig.containsKey("password")) {
            String password = wifiConfig["password"].as<String>();
            if (password.length() > 64) {
                return ValidationResult("WiFi password too long", StatusCode::BAD_REQUEST);
            }
        }
    }

    // 验证AP配置
    if (networkConfig.containsKey("ap")) {
        JsonObject apConfig = networkConfig["ap"].as<JsonObject>();

        if (apConfig.containsKey("ssid")) {
            String apSSID = apConfig["ssid"].as<String>();
            if (apSSID.isEmpty() || apSSID.length() > 32) {
                return ValidationResult("AP SSID must be 1-32 characters", StatusCode::BAD_REQUEST);
            }
        }
    }

    return ValidationResult(true);
}

APIController::ValidationResult ConfigAPIController::validateHardwareConfig(const JsonObject& hardwareConfig) {
    // 验证红外引脚配置
    if (hardwareConfig.containsKey("ir_pins")) {
        JsonObject irPins = hardwareConfig["ir_pins"].as<JsonObject>();

        if (irPins.containsKey("send_pin")) {
            int sendPin = irPins["send_pin"].as<int>();
            if (sendPin < 0 || sendPin > 48) {
                return ValidationResult("Invalid IR send pin", StatusCode::BAD_REQUEST);
            }
        }

        if (irPins.containsKey("recv_pin")) {
            int recvPin = irPins["recv_pin"].as<int>();
            if (recvPin < 0 || recvPin > 48) {
                return ValidationResult("Invalid IR receive pin", StatusCode::BAD_REQUEST);
            }
        }
    }

    return ValidationResult(true);
}

APIController::ValidationResult ConfigAPIController::validateConfigPermission(AsyncWebServerRequest* request, const String& operation) {
    // 简化实现：检查基本认证
    auto authResult = validateAuthToken(request);
    if (!authResult.isValid) {
        return authResult;
    }

    return ValidationResult(true);
}

// ==================== 配置操作方法实现 ====================

bool ConfigAPIController::loadSystemConfig() {
    File configFile = LittleFS.open(SYSTEM_CONFIG_PATH, "r");
    if (!configFile) {
        return false;
    }

    DeserializationError error = deserializeJson(m_configCache.systemConfig, configFile);
    configFile.close();

    return error == DeserializationError::Ok;
}

bool ConfigAPIController::saveSystemConfig(const JsonObject& config) {
    File configFile = LittleFS.open(SYSTEM_CONFIG_PATH, "w");
    if (!configFile) {
        return false;
    }

    serializeJson(config, configFile);
    configFile.close();

    return true;
}

bool ConfigAPIController::loadNetworkConfig() {
    File configFile = LittleFS.open(NETWORK_CONFIG_PATH, "r");
    if (!configFile) {
        return false;
    }

    DeserializationError error = deserializeJson(m_configCache.networkConfig, configFile);
    configFile.close();

    return error == DeserializationError::Ok;
}

bool ConfigAPIController::saveNetworkConfig(const JsonObject& config) {
    File configFile = LittleFS.open(NETWORK_CONFIG_PATH, "w");
    if (!configFile) {
        return false;
    }

    serializeJson(config, configFile);
    configFile.close();

    return true;
}

bool ConfigAPIController::loadHardwareConfig() {
    File configFile = LittleFS.open(HARDWARE_CONFIG_PATH, "r");
    if (!configFile) {
        return false;
    }

    DeserializationError error = deserializeJson(m_configCache.hardwareConfig, configFile);
    configFile.close();

    return error == DeserializationError::Ok;
}

bool ConfigAPIController::saveHardwareConfig(const JsonObject& config) {
    File configFile = LittleFS.open(HARDWARE_CONFIG_PATH, "w");
    if (!configFile) {
        return false;
    }

    serializeJson(config, configFile);
    configFile.close();

    return true;
}

bool ConfigAPIController::loadUserConfig() {
    File configFile = LittleFS.open(USER_CONFIG_PATH, "r");
    if (!configFile) {
        return false;
    }

    DeserializationError error = deserializeJson(m_configCache.userConfig, configFile);
    configFile.close();

    return error == DeserializationError::Ok;
}

bool ConfigAPIController::saveUserConfig(const JsonObject& config) {
    File configFile = LittleFS.open(USER_CONFIG_PATH, "w");
    if (!configFile) {
        return false;
    }

    serializeJson(config, configFile);
    configFile.close();

    return true;
}

bool ConfigAPIController::exportConfigToFile(const String& filePath, const String& configType) {
    File exportFile = LittleFS.open(filePath, "w");
    if (!exportFile) {
        return false;
    }

    DynamicJsonDocument doc(8192);
    JsonObject exportConfig;

    if (configType == "all") {
        exportConfig = formatCompleteConfigToJson(doc);
    } else if (configType == "system") {
        exportConfig = formatSystemConfigToJson(doc);
    } else if (configType == "network") {
        exportConfig = formatNetworkConfigToJson(doc);
    } else if (configType == "hardware") {
        exportConfig = formatHardwareConfigToJson(doc);
    } else {
        exportFile.close();
        return false;
    }

    serializeJson(exportConfig, exportFile);
    exportFile.close();

    return true;
}

// ==================== 配置缓存管理实现 ====================

void ConfigAPIController::updateConfigCache() {
    loadSystemConfig();
    loadNetworkConfig();
    loadHardwareConfig();
    loadUserConfig();

    m_configCache.lastUpdate = millis();
    m_configCache.isDirty = false;
}

void ConfigAPIController::invalidateConfigCache() {
    m_configCache.isDirty = true;
}

bool ConfigAPIController::isConfigCacheValid() {
    if (m_configCache.isDirty) {
        return false;
    }

    return (millis() - m_configCache.lastUpdate) < CONFIG_CACHE_TIMEOUT;
}

// ==================== 格式化方法实现 ====================

JsonObject ConfigAPIController::formatCompleteConfigToJson(JsonDocument& doc) {
    JsonObject config = doc.createNestedObject();

    config["version"] = getCurrentConfigVersion();
    config["timestamp"] = millis();

    // 添加各类配置
    config["system"] = formatSystemConfigToJson(doc);
    config["network"] = formatNetworkConfigToJson(doc);
    config["hardware"] = formatHardwareConfigToJson(doc);
    config["user"] = formatUserConfigToJson(doc);

    return config;
}

JsonObject ConfigAPIController::formatSystemConfigToJson(JsonDocument& doc) {
    JsonObject systemConfig = doc.createNestedObject();

    systemConfig["device_name"] = SystemInfo::DEVICE_NAME;
    systemConfig["firmware_version"] = SystemInfo::FIRMWARE_VERSION;
    systemConfig["log_level"] = "INFO";
    systemConfig["auto_save"] = true;
    systemConfig["backup_enabled"] = true;

    return systemConfig;
}

JsonObject ConfigAPIController::formatNetworkConfigToJson(JsonDocument& doc) {
    JsonObject networkConfig = doc.createNestedObject();

    // WiFi配置
    JsonObject wifiConfig = networkConfig.createNestedObject("wifi");
    wifiConfig["ssid"] = WiFi.SSID();
    wifiConfig["auto_connect"] = true;
    wifiConfig["timeout"] = 30000;

    // AP配置
    JsonObject apConfig = networkConfig.createNestedObject("ap");
    apConfig["ssid"] = SystemInfo::DEVICE_NAME + "_AP";
    apConfig["enabled"] = true;
    apConfig["channel"] = 1;

    return networkConfig;
}

JsonObject ConfigAPIController::formatHardwareConfigToJson(JsonDocument& doc) {
    JsonObject hardwareConfig = doc.createNestedObject();

    // 红外引脚配置
    JsonObject irPins = hardwareConfig.createNestedObject("ir_pins");
    irPins["send_pin"] = IRPins::SEND_PIN;
    irPins["recv_pin"] = IRPins::RECV_PIN;

    // 用户IO引脚配置
    JsonObject userPins = hardwareConfig.createNestedObject("user_pins");
    userPins["status_led"] = UserIOPins::STATUS_LED;
    userPins["user_button"] = UserIOPins::USER_BUTTON;

    return hardwareConfig;
}

JsonObject ConfigAPIController::formatUserConfigToJson(JsonDocument& doc) {
    JsonObject userConfig = doc.createNestedObject();

    userConfig["theme"] = "default";
    userConfig["language"] = "zh-CN";
    userConfig["timezone"] = "Asia/Shanghai";
    userConfig["auto_backup"] = true;

    return userConfig;
}

// ==================== 工具方法实现 ====================

String ConfigAPIController::getCurrentConfigVersion() {
    return CONFIG_VERSION;
}

void ConfigAPIController::updateConfigVersion() {
    m_stats.lastConfigVersion = getCurrentConfigVersion();
}

String ConfigAPIController::generateConfigOperationId() {
    return String(millis()) + "_config_" + String(random(1000, 9999));
}

void ConfigAPIController::logConfigOperation(const String& operation, const String& details) {
    String logEntry = "[CONFIG][" + String(millis()) + "] " + operation;
    if (!details.isEmpty()) {
        logEntry += ": " + details;
    }
    Serial.println(logEntry);
}

void ConfigAPIController::updateConfigStats(const String& operation) {
    if (operation == "read") {
        m_stats.totalConfigReads++;
    } else if (operation == "write") {
        m_stats.totalConfigWrites++;
        m_stats.lastConfigUpdate = millis();
    } else if (operation == "import") {
        m_stats.totalImports++;
    } else if (operation == "export") {
        m_stats.totalExports++;
    } else if (operation == "reset") {
        m_stats.totalResets++;
    }
}

// ==================== 简化的其他方法实现 ====================

void ConfigAPIController::handleImportConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    sendSuccessResponse(request, "Configuration imported successfully");
    updateStats(true);
    updateConfigStats("import");
}

void ConfigAPIController::handleResetConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    sendSuccessResponse(request, "Configuration reset successfully");
    updateStats(true);
    updateConfigStats("reset");
}

void ConfigAPIController::handleGetSystemConfig(AsyncWebServerRequest* request) {
    DynamicJsonDocument doc(2048);
    JsonObject systemConfig = formatSystemConfigToJson(doc);
    sendSuccessResponse(request, "System configuration retrieved", &systemConfig);
    updateStats(true);
}

void ConfigAPIController::handleUpdateSystemConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    sendSuccessResponse(request, "System configuration updated");
    updateStats(true);
}

void ConfigAPIController::handleGetNetworkConfig(AsyncWebServerRequest* request) {
    DynamicJsonDocument doc(2048);
    JsonObject networkConfig = formatNetworkConfigToJson(doc);
    sendSuccessResponse(request, "Network configuration retrieved", &networkConfig);
    updateStats(true);
}

void ConfigAPIController::handleUpdateNetworkConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    sendSuccessResponse(request, "Network configuration updated");
    updateStats(true);
}

void ConfigAPIController::handleGetHardwareConfig(AsyncWebServerRequest* request) {
    DynamicJsonDocument doc(2048);
    JsonObject hardwareConfig = formatHardwareConfigToJson(doc);
    sendSuccessResponse(request, "Hardware configuration retrieved", &hardwareConfig);
    updateStats(true);
}

void ConfigAPIController::handleUpdateHardwareConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    sendSuccessResponse(request, "Hardware configuration updated");
    updateStats(true);
}

void ConfigAPIController::handleCreateConfigBackup(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Configuration backup created");
    updateStats(true);
}

void ConfigAPIController::handleGetBackupList(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Backup list retrieved");
    updateStats(true);
}

void ConfigAPIController::handleRestoreConfigBackupBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    sendSuccessResponse(request, "Configuration restored from backup");
    updateStats(true);
}

void ConfigAPIController::handleDeleteConfigBackup(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Configuration backup deleted");
    updateStats(true);
}

void ConfigAPIController::handleExportConfig(AsyncWebServerRequest* request) {
    logRequest(request);
    
    auto validationResult = validateRequest(request);
    if (!validationResult.isValid) {
        sendErrorResponse(request, validationResult.errorMessage, validationResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        // 获取查询参数
        String configType = getQueryParam(request, "type", "all");
        String format = getQueryParam(request, "format", "json");
        
        if (format != "json") {
            sendErrorResponse(request, "Unsupported export format: " + format, StatusCode::BAD_REQUEST);
            updateStats(false, true);
            return;
        }
        
        // 生成导出文件名
        String filename = "config_export_" + String(millis()) + ".json";
        String filePath = String(CONFIG_TEMP_DIR) + filename;
        
        // 导出配置到文件
        if (!exportConfigToFile(filePath, configType)) {
            sendErrorResponse(request, "Failed to export configuration", StatusCode::INTERNAL_SERVER_ERROR);
            updateStats(false);
            return;
        }
        
        // 读取导出文件
        File exportFile = LittleFS.open(filePath, "r");
        if (!exportFile) {
            sendErrorResponse(request, "Failed to read export file", StatusCode::INTERNAL_SERVER_ERROR);
            updateStats(false);
            return;
        }
        
        // 发送文件响应
        AsyncWebServerResponse* response = request->beginResponse(LittleFS, filePath, "application/json", true);
        response->addHeader("Content-Disposition", "attachment; filename=" + filename);
        addCORSHeaders(response);
        request->send(response);
        
        exportFile.close();
        
        // 清理临时文件
        LittleFS.remove(filePath);
        
        updateStats(true);
        updateConfigStats("export");
        
        logConfigOperation("CONFIG_EXPORT", "Configuration exported: " + configType);
        
    } catch (const std::exception& e) {
        sendErrorResponse(request, "Failed to export configuration: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
    }
}
