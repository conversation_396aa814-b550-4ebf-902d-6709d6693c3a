#pragma once

#include "APIController.h"
#include <LittleFS.h>

/**
 * ESP32-S3 红外控制系统 - 配置API控制器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：配置管理API (5个接口)
 * 
 * 配置API控制器职责：
 * - 处理所有配置相关的HTTP请求
 * - 系统配置的读取和更新
 * - 配置的导入导出管理
 * - 配置重置和备份恢复
 */

class ConfigAPIController : public APIController {
private:
    // 配置操作状态管理
    struct ConfigOperationState {
        bool inProgress;
        String operationType;       // "import", "export", "reset", "update"
        Timestamp startTime;
        String operationId;
        float progress;
        String errorMessage;
        
        ConfigOperationState() : inProgress(false), startTime(0), progress(0.0f) {}
    };
    
    ConfigOperationState m_operationState;
    
    // 配置缓存管理
    struct ConfigCache {
        JsonDocument systemConfig;
        JsonDocument userConfig;
        JsonDocument networkConfig;
        JsonDocument hardwareConfig;
        Timestamp lastUpdate;
        bool isDirty;
        
        ConfigCache() : lastUpdate(0), isDirty(true) {}
    };
    
    ConfigCache m_configCache;
    
    // 配置统计信息
    struct ConfigStats {
        uint32_t totalConfigReads;
        uint32_t totalConfigWrites;
        uint32_t totalImports;
        uint32_t totalExports;
        uint32_t totalResets;
        Timestamp lastConfigUpdate;
        String lastConfigVersion;
        
        ConfigStats() : totalConfigReads(0), totalConfigWrites(0), totalImports(0),
                       totalExports(0), totalResets(0), lastConfigUpdate(0) {}
    };
    
    ConfigStats m_stats;

public:
    ConfigAPIController();
    ~ConfigAPIController() override = default;
    
    // 基类接口实现
    void registerRoutes(AsyncWebServer* server) override;
    const char* getControllerName() const override { return "ConfigAPIController"; }

private:
    // ==================== 配置管理API接口 ====================
    
    // GET /api/config - 获取配置
    void handleGetConfig(AsyncWebServerRequest* request);
    
    // POST /api/config - 更新配置
    void handleUpdateConfig(AsyncWebServerRequest* request);
    void handleUpdateConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // GET /api/config/export - 导出配置
    void handleExportConfig(AsyncWebServerRequest* request);
    
    // POST /api/config/import - 导入配置
    void handleImportConfig(AsyncWebServerRequest* request);
    void handleImportConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/config/reset - 重置配置
    void handleResetConfig(AsyncWebServerRequest* request);
    void handleResetConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== 配置分类管理 ====================
    
    // GET /api/config/system - 获取系统配置
    void handleGetSystemConfig(AsyncWebServerRequest* request);
    
    // POST /api/config/system - 更新系统配置
    void handleUpdateSystemConfig(AsyncWebServerRequest* request);
    void handleUpdateSystemConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // GET /api/config/network - 获取网络配置
    void handleGetNetworkConfig(AsyncWebServerRequest* request);
    
    // POST /api/config/network - 更新网络配置
    void handleUpdateNetworkConfig(AsyncWebServerRequest* request);
    void handleUpdateNetworkConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // GET /api/config/hardware - 获取硬件配置
    void handleGetHardwareConfig(AsyncWebServerRequest* request);
    
    // POST /api/config/hardware - 更新硬件配置
    void handleUpdateHardwareConfig(AsyncWebServerRequest* request);
    void handleUpdateHardwareConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== 配置备份和恢复 ====================
    
    // POST /api/config/backup - 创建配置备份
    void handleCreateConfigBackup(AsyncWebServerRequest* request);
    
    // GET /api/config/backup/list - 获取备份列表
    void handleGetBackupList(AsyncWebServerRequest* request);
    
    // POST /api/config/restore - 恢复配置备份
    void handleRestoreConfigBackup(AsyncWebServerRequest* request);
    void handleRestoreConfigBackupBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // DELETE /api/config/backup/{id} - 删除配置备份
    void handleDeleteConfigBackup(AsyncWebServerRequest* request);
    
    // ==================== 配置验证和安全 ====================
    
    // 验证配置数据
    ValidationResult validateConfigData(const JsonObject& configData, const String& configType = "");
    
    // 验证系统配置
    ValidationResult validateSystemConfig(const JsonObject& systemConfig);
    
    // 验证网络配置
    ValidationResult validateNetworkConfig(const JsonObject& networkConfig);
    
    // 验证硬件配置
    ValidationResult validateHardwareConfig(const JsonObject& hardwareConfig);
    
    // 验证导入数据
    ValidationResult validateImportData(const JsonObject& importData);
    
    // 验证配置权限
    ValidationResult validateConfigPermission(AsyncWebServerRequest* request, const String& operation);
    
    // ==================== 配置操作方法 ====================
    
    // 加载配置
    bool loadSystemConfig();
    bool loadNetworkConfig();
    bool loadHardwareConfig();
    bool loadUserConfig();
    
    // 保存配置
    bool saveSystemConfig(const JsonObject& config);
    bool saveNetworkConfig(const JsonObject& config);
    bool saveHardwareConfig(const JsonObject& config);
    bool saveUserConfig(const JsonObject& config);
    
    // 重置配置
    bool resetSystemConfig();
    bool resetNetworkConfig();
    bool resetHardwareConfig();
    bool resetAllConfigs();
    
    // 导入导出配置
    bool exportConfigToFile(const String& filePath, const String& configType = "all");
    bool importConfigFromFile(const String& filePath);
    bool importConfigFromJson(const JsonObject& configData);
    
    // ==================== 配置缓存管理 ====================
    
    // 更新缓存
    void updateConfigCache();
    void invalidateConfigCache();
    bool isConfigCacheValid();
    
    // 获取缓存配置
    JsonObject getCachedSystemConfig();
    JsonObject getCachedNetworkConfig();
    JsonObject getCachedHardwareConfig();
    JsonObject getCachedUserConfig();
    
    // ==================== 状态管理方法 ====================
    
    // 生成操作ID
    String generateConfigOperationId();
    
    // 更新操作进度
    void updateConfigOperationProgress(float progress);
    
    // 重置操作状态
    void resetConfigOperationState();
    
    // 检查操作超时
    bool isConfigOperationTimeout();
    
    // ==================== 格式化方法 ====================
    
    // 格式化完整配置为JSON
    JsonObject formatCompleteConfigToJson(JsonDocument& doc);
    
    // 格式化系统配置为JSON
    JsonObject formatSystemConfigToJson(JsonDocument& doc);
    
    // 格式化网络配置为JSON
    JsonObject formatNetworkConfigToJson(JsonDocument& doc);
    
    // 格式化硬件配置为JSON
    JsonObject formatHardwareConfigToJson(JsonDocument& doc);
    
    // 格式化用户配置为JSON
    JsonObject formatUserConfigToJson(JsonDocument& doc);
    
    // 格式化配置统计为JSON
    JsonObject formatConfigStatsToJson(JsonDocument& doc);
    
    // 格式化备份列表为JSON
    JsonArray formatBackupListToJson(JsonDocument& doc);
    
    // ==================== 默认配置生成 ====================
    
    // 生成默认系统配置
    JsonObject generateDefaultSystemConfig(JsonDocument& doc);
    
    // 生成默认网络配置
    JsonObject generateDefaultNetworkConfig(JsonDocument& doc);
    
    // 生成默认硬件配置
    JsonObject generateDefaultHardwareConfig(JsonDocument& doc);
    
    // 生成默认用户配置
    JsonObject generateDefaultUserConfig(JsonDocument& doc);
    
    // ==================== 配置版本管理 ====================
    
    // 获取配置版本
    String getCurrentConfigVersion();
    
    // 更新配置版本
    void updateConfigVersion();
    
    // 检查配置兼容性
    bool isConfigVersionCompatible(const String& version);
    
    // 迁移配置版本
    bool migrateConfigVersion(JsonObject& config, const String& fromVersion, const String& toVersion);
    
    // ==================== 工具方法 ====================
    
    // 合并配置对象
    void mergeConfigObjects(JsonObject& target, const JsonObject& source);
    
    // 深度复制配置
    void deepCopyConfig(JsonObject& target, const JsonObject& source);
    
    // 配置差异比较
    JsonObject compareConfigs(const JsonObject& config1, const JsonObject& config2, JsonDocument& doc);
    
    // 生成配置校验和
    String generateConfigChecksum(const JsonObject& config);
    
    // 验证配置校验和
    bool verifyConfigChecksum(const JsonObject& config, const String& expectedChecksum);
    
    // ==================== 错误处理和日志 ====================
    
    // 处理配置错误
    void handleConfigError(const String& operation, const String& error);
    
    // 记录配置操作日志
    void logConfigOperation(const String& operation, const String& details = "");
    
    // 更新配置统计
    void updateConfigStats(const String& operation);
    
    // 清理临时文件
    void cleanupConfigTempFiles();

private:
    // 配置文件路径
    static constexpr const char* SYSTEM_CONFIG_PATH = "/config/system.json";
    static constexpr const char* NETWORK_CONFIG_PATH = "/config/network.json";
    static constexpr const char* HARDWARE_CONFIG_PATH = "/config/hardware.json";
    static constexpr const char* USER_CONFIG_PATH = "/config/user.json";
    static constexpr const char* CONFIG_BACKUP_DIR = "/backup/config/";
    static constexpr const char* CONFIG_TEMP_DIR = "/temp/config/";
    
    // 配置版本信息
    static constexpr const char* CONFIG_VERSION = "1.0.0";
    static constexpr uint32_t CONFIG_CACHE_TIMEOUT = 300000; // 5分钟缓存超时
    static constexpr uint32_t MAX_CONFIG_OPERATION_TIME = 60000; // 1分钟最大操作时间
};
