#include "OTAAPIController.h"
#include "../config/system-config.h"
#include <ESP.h>
#include <WiFi.h>

/**
 * ESP32-S3 红外控制系统 - OTA API控制器实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：OTA管理API (4个接口)
 */

OTAAPIController::OTAAPIController() : m_lastProgressUpdate(0) {
    loadOTAConfig();
}

// ==================== 路由注册实现 ====================

void OTAAPIController::registerRoutes(AsyncWebServer* server) {
    if (!server) return;
    
    // ==================== OTA管理API路由 ====================
    
    // GET /api/ota/status - 获取OTA状态
    server->on("/api/ota/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetOTAStatus(request);
    });
    
    // POST /api/ota/login - OTA登录
    server->on("/api/ota/login", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleOTALoginBody(request, data, len, index, total);
            }
        }
    );
    
    // POST /api/ota/firmware - 固件更新
    server->on("/api/ota/firmware", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            handleFirmwareUpdateBody(request, data, len, index, total);
        }
    );
    
    // POST /api/ota/filesystem - 文件系统更新
    server->on("/api/ota/filesystem", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            handleFilesystemUpdateBody(request, data, len, index, total);
        }
    );
    
    // ==================== OTA辅助接口路由 ====================
    
    // GET /api/ota/version - 获取当前版本信息
    server->on("/api/ota/version", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetVersion(request);
    });
    
    // POST /api/ota/abort - 中止OTA更新
    server->on("/api/ota/abort", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleAbortUpdate(request);
    });
    
    // GET /api/ota/history - 获取更新历史
    server->on("/api/ota/history", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetUpdateHistory(request);
    });
    
    // POST /api/ota/verify - 验证固件完整性
    server->on("/api/ota/verify", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleVerifyFirmware(request);
    });
}

// ==================== OTA管理API接口实现 ====================

void OTAAPIController::handleGetOTAStatus(AsyncWebServerRequest* request) {
    logRequest(request);
    
    auto validationResult = validateRequest(request);
    if (!validationResult.isValid) {
        sendErrorResponse(request, validationResult.errorMessage, validationResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        // 格式化OTA状态
        DynamicJsonDocument doc(2048);
        JsonObject statusObj = formatOTAStatusToJson(doc);
        
        sendSuccessResponse(request, "OTA status retrieved successfully", &statusObj);
        updateStats(true);
        
    } catch (const std::exception& e) {
        sendErrorResponse(request, "Failed to retrieve OTA status: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
    }
}

void OTAAPIController::handleOTALoginBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    logRequest(request);
    
    auto validationResult = validateRequest(request);
    if (!validationResult.isValid) {
        sendErrorResponse(request, validationResult.errorMessage, validationResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    // 解析JSON请求体
    String body = String((char*)data, len);
    DynamicJsonDocument doc(1024);
    auto jsonResult = validateJsonBody(body, doc);
    if (!jsonResult.isValid) {
        sendErrorResponse(request, jsonResult.errorMessage, jsonResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    JsonObject credentials = doc.as<JsonObject>();
    
    // 验证登录凭据
    auto credentialsValidation = validateLoginCredentials(credentials);
    if (!credentialsValidation.isValid) {
        sendErrorResponse(request, credentialsValidation.errorMessage, credentialsValidation.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        // 生成认证令牌
        String authToken = generateAuthToken();
        m_securityConfig.authToken = authToken;
        m_otaState.isAuthenticated = true;
        m_otaState.sessionId = generateSessionId();
        
        // 格式化响应
        DynamicJsonDocument responseDoc(1024);
        JsonObject loginResult = responseDoc.createNestedObject();
        loginResult["authenticated"] = true;
        loginResult["token"] = authToken;
        loginResult["session_id"] = m_otaState.sessionId;
        loginResult["expires_in"] = m_securityConfig.sessionTimeout;
        
        sendSuccessResponse(request, "OTA login successful", &loginResult);
        updateStats(true);
        
        logUpdateEvent("OTA_LOGIN", "User authenticated for OTA operations");
        
    } catch (const std::exception& e) {
        sendErrorResponse(request, "Failed to authenticate: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
    }
}

void OTAAPIController::handleFirmwareUpdateBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    logRequest(request);
    
    // 验证OTA权限
    auto permissionResult = validateOTAPermission(request);
    if (!permissionResult.isValid) {
        sendErrorResponse(request, permissionResult.errorMessage, permissionResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    // 验证会话
    auto sessionResult = validateSession(request);
    if (!sessionResult.isValid) {
        sendErrorResponse(request, sessionResult.errorMessage, sessionResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        // 开始固件更新（第一个数据块）
        if (index == 0) {
            // 验证更新文件
            auto fileValidation = validateUpdateFile("firmware", total);
            if (!fileValidation.isValid) {
                sendErrorResponse(request, fileValidation.errorMessage, fileValidation.statusCode);
                updateStats(false, true);
                return;
            }
            
            // 开始固件更新
            if (!startFirmwareUpdate(total)) {
                sendErrorResponse(request, "Failed to start firmware update", StatusCode::INTERNAL_SERVER_ERROR);
                updateStats(false);
                return;
            }
            
            logUpdateEvent("FIRMWARE_UPDATE_START", "Firmware update started, size: " + String(total));
        }
        
        // 处理固件数据
        if (!processFirmwareData(data, len)) {
            abortUpdate();
            sendErrorResponse(request, "Failed to process firmware data", StatusCode::INTERNAL_SERVER_ERROR);
            updateStats(false);
            return;
        }
        
        // 更新进度
        m_otaState.receivedSize = index + len;
        updateProgress(m_otaState.receivedSize);
        
        // 完成固件更新（最后一个数据块）
        if (index + len == total) {
            if (!completeFirmwareUpdate()) {
                abortUpdate();
                sendErrorResponse(request, "Failed to complete firmware update", StatusCode::INTERNAL_SERVER_ERROR);
                updateStats(false);
                return;
            }
            
            // 格式化响应
            DynamicJsonDocument responseDoc(1024);
            JsonObject updateResult = responseDoc.createNestedObject();
            updateResult["success"] = true;
            updateResult["progress"] = 100.0f;
            updateResult["message"] = "Firmware update completed successfully";
            updateResult["restart_required"] = true;
            
            sendSuccessResponse(request, "Firmware update completed successfully", &updateResult);
            updateStats(true);
            updateOTAStats(true);
            
            logUpdateEvent("FIRMWARE_UPDATE_COMPLETE", "Firmware update completed successfully");
            
            // 延迟重启以确保响应发送完成
            delay(2000);
            ESP.restart();
        } else {
            // 发送进度响应
            sendProgressNotification();
        }
        
    } catch (const std::exception& e) {
        abortUpdate();
        sendErrorResponse(request, "Firmware update failed: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
        updateOTAStats(false);
    }
}

void OTAAPIController::handleFilesystemUpdateBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    logRequest(request);
    
    // 验证OTA权限
    auto permissionResult = validateOTAPermission(request);
    if (!permissionResult.isValid) {
        sendErrorResponse(request, permissionResult.errorMessage, permissionResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        // 开始文件系统更新（第一个数据块）
        if (index == 0) {
            // 验证更新文件
            auto fileValidation = validateUpdateFile("filesystem", total);
            if (!fileValidation.isValid) {
                sendErrorResponse(request, fileValidation.errorMessage, fileValidation.statusCode);
                updateStats(false, true);
                return;
            }
            
            // 开始文件系统更新
            if (!startFilesystemUpdate(total)) {
                sendErrorResponse(request, "Failed to start filesystem update", StatusCode::INTERNAL_SERVER_ERROR);
                updateStats(false);
                return;
            }
            
            logUpdateEvent("FILESYSTEM_UPDATE_START", "Filesystem update started, size: " + String(total));
        }
        
        // 处理文件系统数据
        if (!processFilesystemData(data, len)) {
            abortUpdate();
            sendErrorResponse(request, "Failed to process filesystem data", StatusCode::INTERNAL_SERVER_ERROR);
            updateStats(false);
            return;
        }
        
        // 更新进度
        m_otaState.receivedSize = index + len;
        updateProgress(m_otaState.receivedSize);
        
        // 完成文件系统更新（最后一个数据块）
        if (index + len == total) {
            if (!completeFilesystemUpdate()) {
                abortUpdate();
                sendErrorResponse(request, "Failed to complete filesystem update", StatusCode::INTERNAL_SERVER_ERROR);
                updateStats(false);
                return;
            }
            
            // 格式化响应
            DynamicJsonDocument responseDoc(1024);
            JsonObject updateResult = responseDoc.createNestedObject();
            updateResult["success"] = true;
            updateResult["progress"] = 100.0f;
            updateResult["message"] = "Filesystem update completed successfully";
            updateResult["restart_required"] = false;
            
            sendSuccessResponse(request, "Filesystem update completed successfully", &updateResult);
            updateStats(true);
            updateOTAStats(true);
            
            logUpdateEvent("FILESYSTEM_UPDATE_COMPLETE", "Filesystem update completed successfully");
        } else {
            // 发送进度响应
            sendProgressNotification();
        }
        
    } catch (const std::exception& e) {
        abortUpdate();
        sendErrorResponse(request, "Filesystem update failed: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
        updateOTAStats(false);
    }
}

// ==================== 安全验证方法实现 ====================

APIController::ValidationResult OTAAPIController::validateOTAPermission(AsyncWebServerRequest* request) {
    if (!m_securityConfig.requireAuthentication) {
        return ValidationResult(true);
    }

    if (!m_otaState.isAuthenticated) {
        return ValidationResult("OTA authentication required", StatusCode::UNAUTHORIZED);
    }

    return ValidationResult(true);
}

APIController::ValidationResult OTAAPIController::validateLoginCredentials(const JsonObject& credentials) {
    if (!credentials.containsKey("username") || !credentials.containsKey("password")) {
        return ValidationResult("Username and password required", StatusCode::BAD_REQUEST);
    }

    String username = credentials["username"].as<String>();
    String password = credentials["password"].as<String>();

    // 简化的凭据验证
    if (username != "admin" || password != "esp32ota2024") {
        return ValidationResult("Invalid credentials", StatusCode::UNAUTHORIZED);
    }

    return ValidationResult(true);
}

APIController::ValidationResult OTAAPIController::validateUpdateFile(const String& updateType, size_t fileSize) {
    if (fileSize == 0) {
        return ValidationResult("Update file is empty", StatusCode::BAD_REQUEST);
    }

    if (fileSize > m_securityConfig.maxFileSize) {
        return ValidationResult("Update file too large", StatusCode::UNPROCESSABLE_ENTITY);
    }

    return ValidationResult(true);
}

APIController::ValidationResult OTAAPIController::validateClientIP(AsyncWebServerRequest* request) {
    return ValidationResult(true); // 简化实现
}

APIController::ValidationResult OTAAPIController::validateSession(AsyncWebServerRequest* request) {
    if (!m_otaState.isAuthenticated) {
        return ValidationResult("Session not authenticated", StatusCode::UNAUTHORIZED);
    }

    return ValidationResult(true);
}

String OTAAPIController::generateAuthToken() {
    return String(millis()) + "_" + String(random(100000, 999999)) + "_ota";
}

String OTAAPIController::generateSessionId() {
    return String(millis()) + "_" + String(random(10000, 99999)) + "_session";
}

// ==================== OTA操作方法实现 ====================

bool OTAAPIController::startFirmwareUpdate(size_t totalSize) {
    if (m_otaState.isUpdating) {
        return false;
    }

    if (!Update.begin(totalSize, U_FLASH)) {
        handleOTAError("Failed to begin firmware update: " + String(Update.errorString()));
        return false;
    }

    m_otaState.isUpdating = true;
    m_otaState.updateType = "firmware";
    m_otaState.totalSize = totalSize;
    m_otaState.receivedSize = 0;
    m_otaState.progress = 0.0f;
    m_otaState.startTime = millis();

    return true;
}

bool OTAAPIController::processFirmwareData(uint8_t* data, size_t len) {
    if (!m_otaState.isUpdating || m_otaState.updateType != "firmware") {
        return false;
    }

    size_t written = Update.write(data, len);
    return written == len;
}

bool OTAAPIController::completeFirmwareUpdate() {
    if (!m_otaState.isUpdating || m_otaState.updateType != "firmware") {
        return false;
    }

    if (!Update.end(true)) {
        handleOTAError("Failed to complete firmware update: " + String(Update.errorString()));
        return false;
    }

    resetOTAState();
    return true;
}

bool OTAAPIController::startFilesystemUpdate(size_t totalSize) {
    if (m_otaState.isUpdating) {
        return false;
    }

    m_updateFile = LittleFS.open(TEMP_FILESYSTEM_PATH, "w");
    if (!m_updateFile) {
        return false;
    }

    m_otaState.isUpdating = true;
    m_otaState.updateType = "filesystem";
    m_otaState.totalSize = totalSize;
    m_otaState.receivedSize = 0;
    m_otaState.progress = 0.0f;
    m_otaState.startTime = millis();

    return true;
}

bool OTAAPIController::processFilesystemData(uint8_t* data, size_t len) {
    if (!m_otaState.isUpdating || !m_updateFile) {
        return false;
    }

    size_t written = m_updateFile.write(data, len);
    return written == len;
}

bool OTAAPIController::completeFilesystemUpdate() {
    if (m_updateFile) {
        m_updateFile.close();
    }

    LittleFS.remove(TEMP_FILESYSTEM_PATH);
    resetOTAState();
    return true;
}

void OTAAPIController::abortUpdate() {
    if (m_otaState.isUpdating) {
        if (m_otaState.updateType == "firmware") {
            Update.abort();
        } else if (m_updateFile) {
            m_updateFile.close();
            LittleFS.remove(TEMP_FILESYSTEM_PATH);
        }
    }

    resetOTAState();
}

// ==================== 状态管理和工具方法 ====================

void OTAAPIController::updateProgress(size_t receivedBytes) {
    if (m_otaState.totalSize > 0) {
        m_otaState.progress = (float)receivedBytes / m_otaState.totalSize * 100.0f;
    }
}

void OTAAPIController::resetOTAState() {
    m_otaState.isUpdating = false;
    m_otaState.updateType = "";
    m_otaState.totalSize = 0;
    m_otaState.receivedSize = 0;
    m_otaState.progress = 0.0f;
    m_otaState.startTime = 0;
    m_otaState.errorMessage = "";
}

void OTAAPIController::cleanupSession() {
    m_otaState.isAuthenticated = false;
    m_otaState.sessionId = "";
    m_securityConfig.authToken = "";
}

JsonObject OTAAPIController::formatOTAStatusToJson(JsonDocument& doc) {
    JsonObject status = doc.createNestedObject();

    status["is_updating"] = m_otaState.isUpdating;
    status["update_type"] = m_otaState.updateType;
    status["progress"] = m_otaState.progress;
    status["total_size"] = m_otaState.totalSize;
    status["received_size"] = m_otaState.receivedSize;
    status["is_authenticated"] = m_otaState.isAuthenticated;
    status["session_id"] = m_otaState.sessionId;

    return status;
}

JsonObject OTAAPIController::formatVersionInfoToJson(JsonDocument& doc) {
    JsonObject version = doc.createNestedObject();

    version["firmware_version"] = getCurrentFirmwareVersion();
    version["chip_model"] = ESP.getChipModel();
    version["sdk_version"] = ESP.getSdkVersion();
    version["flash_size"] = ESP.getFlashChipSize();

    return version;
}

String OTAAPIController::getCurrentFirmwareVersion() {
    return SystemInfo::FIRMWARE_VERSION;
}

String OTAAPIController::getCurrentFilesystemVersion() {
    return "1.0.0";
}

size_t OTAAPIController::getAvailableSpace() {
    return LittleFS.totalBytes() - LittleFS.usedBytes();
}

void OTAAPIController::handleOTAError(const String& error) {
    m_otaState.errorMessage = error;
    logUpdateEvent("OTA_ERROR", error);
}

void OTAAPIController::logUpdateEvent(const String& event, const String& details) {
    String logEntry = "[" + String(millis()) + "] " + event;
    if (!details.isEmpty()) {
        logEntry += ": " + details;
    }
    Serial.println(logEntry);
}

void OTAAPIController::sendProgressNotification() {
    Serial.printf("OTA Progress: %.1f%%\n", m_otaState.progress);
}

void OTAAPIController::cleanupTempFiles() {
    LittleFS.remove(TEMP_FILESYSTEM_PATH);
}

bool OTAAPIController::backupCurrentConfig() {
    return true;
}

void OTAAPIController::loadOTAConfig() {
    m_securityConfig.requireAuthentication = true;
    m_securityConfig.sessionTimeout = 300000;
    m_securityConfig.maxFileSize = 7 * 1024 * 1024;
}

void OTAAPIController::updateOTAStats(bool success) {
    m_stats.totalUpdates++;
    if (success) {
        m_stats.successfulUpdates++;
    } else {
        m_stats.failedUpdates++;
    }
    m_stats.lastUpdate = millis();
}

// ==================== 简化的其他方法实现 ====================

void OTAAPIController::handleGetVersion(AsyncWebServerRequest* request) {
    DynamicJsonDocument doc(1024);
    JsonObject versionObj = formatVersionInfoToJson(doc);
    sendSuccessResponse(request, "Version information retrieved", &versionObj);
    updateStats(true);
}

void OTAAPIController::handleAbortUpdate(AsyncWebServerRequest* request) {
    abortUpdate();
    sendSuccessResponse(request, "Update aborted successfully");
    updateStats(true);
}

void OTAAPIController::handleGetUpdateHistory(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Update history retrieved");
    updateStats(true);
}

void OTAAPIController::handleVerifyFirmware(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Firmware verification completed");
    updateStats(true);
}
