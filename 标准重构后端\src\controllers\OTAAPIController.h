#pragma once

#include "APIController.h"
#include <Update.h>
#include <LittleFS.h>

/**
 * ESP32-S3 红外控制系统 - OTA API控制器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：OTA管理API (4个接口)
 * 
 * OTA API控制器职责：
 * - 处理所有OTA相关的HTTP请求
 * - 固件更新管理
 * - 文件系统更新管理
 * - OTA状态监控和安全验证
 */

class OTAAPIController : public APIController {
private:
    // OTA状态管理
    struct OTAState {
        bool isUpdating;
        String updateType;          // "firmware" 或 "filesystem"
        size_t totalSize;
        size_t receivedSize;
        float progress;
        Timestamp startTime;
        String sessionId;
        String errorMessage;
        bool isAuthenticated;
        
        OTAState() : isUpdating(false), totalSize(0), receivedSize(0), 
                    progress(0.0f), startTime(0), isAuthenticated(false) {}
    };
    
    OTAState m_otaState;
    
    // OTA安全配置
    struct OTASecurityConfig {
        bool requireAuthentication;
        String authToken;
        uint32_t sessionTimeout;
        uint32_t maxFileSize;
        std::vector<String> allowedIPs;
        bool enableChecksumVerification;
        
        OTASecurityConfig() : requireAuthentication(true), sessionTimeout(300000), // 5分钟
                             maxFileSize(7 * 1024 * 1024), enableChecksumVerification(true) {} // 7MB
    };
    
    OTASecurityConfig m_securityConfig;
    
    // OTA统计信息
    struct OTAStats {
        uint32_t totalUpdates;
        uint32_t successfulUpdates;
        uint32_t failedUpdates;
        Timestamp lastUpdate;
        String lastUpdateVersion;
        uint32_t averageUpdateTime;
        
        OTAStats() : totalUpdates(0), successfulUpdates(0), failedUpdates(0),
                    lastUpdate(0), averageUpdateTime(0) {}
    };
    
    OTAStats m_stats;

public:
    OTAAPIController();
    ~OTAAPIController() override = default;
    
    // 基类接口实现
    void registerRoutes(AsyncWebServer* server) override;
    const char* getControllerName() const override { return "OTAAPIController"; }

private:
    // ==================== OTA管理API接口 ====================
    
    // GET /api/ota/status - 获取OTA状态
    void handleGetOTAStatus(AsyncWebServerRequest* request);
    
    // POST /api/ota/login - OTA登录
    void handleOTALogin(AsyncWebServerRequest* request);
    void handleOTALoginBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/ota/firmware - 固件更新
    void handleFirmwareUpdate(AsyncWebServerRequest* request);
    void handleFirmwareUpdateBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/ota/filesystem - 文件系统更新
    void handleFilesystemUpdate(AsyncWebServerRequest* request);
    void handleFilesystemUpdateBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== OTA辅助接口 ====================
    
    // GET /api/ota/version - 获取当前版本信息
    void handleGetVersion(AsyncWebServerRequest* request);
    
    // POST /api/ota/abort - 中止OTA更新
    void handleAbortUpdate(AsyncWebServerRequest* request);
    
    // GET /api/ota/history - 获取更新历史
    void handleGetUpdateHistory(AsyncWebServerRequest* request);
    
    // POST /api/ota/verify - 验证固件完整性
    void handleVerifyFirmware(AsyncWebServerRequest* request);
    
    // ==================== 安全验证方法 ====================
    
    // 验证OTA权限
    ValidationResult validateOTAPermission(AsyncWebServerRequest* request);
    
    // 验证登录凭据
    ValidationResult validateLoginCredentials(const JsonObject& credentials);
    
    // 验证更新文件
    ValidationResult validateUpdateFile(const String& updateType, size_t fileSize);
    
    // 验证客户端IP
    ValidationResult validateClientIP(AsyncWebServerRequest* request);
    
    // 验证会话有效性
    ValidationResult validateSession(AsyncWebServerRequest* request);
    
    // 生成认证令牌
    String generateAuthToken();
    
    // 验证认证令牌
    bool validateAuthToken(const String& token);
    
    // ==================== OTA操作方法 ====================
    
    // 开始固件更新
    bool startFirmwareUpdate(size_t totalSize);
    
    // 处理固件数据
    bool processFirmwareData(uint8_t* data, size_t len);
    
    // 完成固件更新
    bool completeFirmwareUpdate();
    
    // 开始文件系统更新
    bool startFilesystemUpdate(size_t totalSize);
    
    // 处理文件系统数据
    bool processFilesystemData(uint8_t* data, size_t len);
    
    // 完成文件系统更新
    bool completeFilesystemUpdate();
    
    // 中止更新操作
    void abortUpdate();
    
    // ==================== 状态管理方法 ====================
    
    // 更新进度
    void updateProgress(size_t receivedBytes);
    
    // 重置OTA状态
    void resetOTAState();
    
    // 检查更新超时
    bool isUpdateTimeout();
    
    // 生成会话ID
    String generateSessionId();
    
    // 清理会话
    void cleanupSession();
    
    // ==================== 格式化方法 ====================
    
    // 格式化OTA状态为JSON
    JsonObject formatOTAStatusToJson(JsonDocument& doc);
    
    // 格式化版本信息为JSON
    JsonObject formatVersionInfoToJson(JsonDocument& doc);
    
    // 格式化更新历史为JSON
    JsonArray formatUpdateHistoryToJson(JsonDocument& doc);
    
    // 格式化OTA统计为JSON
    JsonObject formatOTAStatsToJson(JsonDocument& doc);
    
    // ==================== 工具方法 ====================
    
    // 获取当前固件版本
    String getCurrentFirmwareVersion();
    
    // 获取当前文件系统版本
    String getCurrentFilesystemVersion();
    
    // 计算文件校验和
    String calculateChecksum(const uint8_t* data, size_t len);
    
    // 验证文件校验和
    bool verifyChecksum(const String& expectedChecksum);
    
    // 获取可用存储空间
    size_t getAvailableSpace();
    
    // 检查系统兼容性
    bool checkSystemCompatibility(const String& version);
    
    // 备份当前配置
    bool backupCurrentConfig();
    
    // 恢复配置
    bool restoreConfig();
    
    // ==================== 错误处理方法 ====================
    
    // 处理OTA错误
    void handleOTAError(const String& error);
    
    // 记录更新日志
    void logUpdateEvent(const String& event, const String& details = "");
    
    // 发送OTA进度通知
    void sendProgressNotification();
    
    // 清理临时文件
    void cleanupTempFiles();
    
    // ==================== 安全检查方法 ====================
    
    // 检查固件签名
    bool verifyFirmwareSignature(const uint8_t* data, size_t len);
    
    // 检查文件系统完整性
    bool verifyFilesystemIntegrity();
    
    // 检查更新权限
    bool hasUpdatePermission(const String& updateType);
    
    // 防止回滚攻击
    bool preventRollbackAttack(const String& version);
    
    // ==================== 系统集成方法 ====================
    
    // 通知系统服务
    void notifySystemServices(const String& event);
    
    // 停止关键服务
    void stopCriticalServices();
    
    // 重启关键服务
    void restartCriticalServices();
    
    // 执行系统重启
    void performSystemRestart();
    
    // ==================== 配置管理 ====================
    
    // 加载OTA配置
    void loadOTAConfig();
    
    // 保存OTA配置
    void saveOTAConfig();
    
    // 重置OTA配置
    void resetOTAConfig();
    
    // 更新统计信息
    void updateOTAStats(bool success);

private:
    // 临时文件路径
    static constexpr const char* TEMP_FIRMWARE_PATH = "/temp/firmware.bin";
    static constexpr const char* TEMP_FILESYSTEM_PATH = "/temp/filesystem.bin";
    static constexpr const char* BACKUP_CONFIG_PATH = "/backup/config.json";
    static constexpr const char* UPDATE_LOG_PATH = "/logs/ota_update.log";
    
    // OTA配置常量
    static constexpr uint32_t MAX_UPDATE_TIME = 600000;     // 10分钟最大更新时间
    static constexpr uint32_t PROGRESS_UPDATE_INTERVAL = 1000; // 1秒进度更新间隔
    static constexpr size_t CHUNK_SIZE = 1024;              // 1KB数据块大小
    
    // 文件句柄
    File m_updateFile;
    
    // 校验和计算
    String m_currentChecksum;
    
    // 进度通知时间
    Timestamp m_lastProgressUpdate;
};
