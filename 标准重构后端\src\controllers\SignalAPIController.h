#pragma once

#include "APIController.h"
#include "../services/SignalService.h"
#include "../hardware/IRController.h"

/**
 * ESP32-S3 红外控制系统 - 信号API控制器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：信号管理API (22个接口)
 * 
 * 信号API控制器职责：
 * - 处理所有信号相关的HTTP请求
 * - 信号CRUD操作
 * - 信号学习和发送
 * - 批量操作和导入导出
 * - 信号搜索和统计
 */

class SignalAPIController : public APIController {
private:
    SignalService* m_signalService;
    IRController* m_irController;
    
    // 学习状态管理
    struct LearningState {
        bool isLearning;
        Timestamp startTime;
        String sessionId;
        IRSignal learnedSignal;
        
        LearningState() : isLearning(false), startTime(0) {}
    };
    
    LearningState m_learningState;
    
    // 批量操作状态
    struct BatchOperationState {
        bool inProgress;
        String operationType;
        uint32_t totalItems;
        uint32_t processedItems;
        uint32_t successCount;
        uint32_t errorCount;
        Timestamp startTime;
        
        BatchOperationState() : inProgress(false), totalItems(0), processedItems(0),
                               successCount(0), errorCount(0), startTime(0) {}
    };
    
    BatchOperationState m_batchState;

public:
    SignalAPIController(SignalService* signalService, IRController* irController);
    ~SignalAPIController() override = default;
    
    // 基类接口实现
    void registerRoutes(AsyncWebServer* server) override;
    const char* getControllerName() const override { return "SignalAPIController"; }

private:
    // ==================== 基础CRUD操作 ====================
    
    // GET /api/signals - 获取所有信号
    void handleGetSignals(AsyncWebServerRequest* request);
    
    // POST /api/signals - 创建新信号
    void handleCreateSignal(AsyncWebServerRequest* request);
    void handleCreateSignalBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // GET /api/signals/{id} - 获取特定信号
    void handleGetSignal(AsyncWebServerRequest* request);
    
    // PUT /api/signals/{id} - 更新信号
    void handleUpdateSignal(AsyncWebServerRequest* request);
    void handleUpdateSignalBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // DELETE /api/signals/{id} - 删除信号
    void handleDeleteSignal(AsyncWebServerRequest* request);
    
    // ==================== 信号发送操作 ====================
    
    // POST /api/signals/send - 发送信号（通过请求体指定）
    void handleSendSignal(AsyncWebServerRequest* request);
    void handleSendSignalBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/signals/{id}/send - 发送特定信号
    void handleSendSignalById(AsyncWebServerRequest* request);
    
    // POST /api/signals/batch-send - 批量发送信号
    void handleBatchSendSignals(AsyncWebServerRequest* request);
    void handleBatchSendSignalsBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== 批量操作 ====================
    
    // POST /api/signals/batch-delete - 批量删除信号
    void handleBatchDeleteSignals(AsyncWebServerRequest* request);
    void handleBatchDeleteSignalsBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== 信号学习操作 ====================
    
    // POST /api/signals/learn/start - 开始学习信号
    void handleStartLearning(AsyncWebServerRequest* request);
    
    // POST /api/signals/learn/stop - 停止学习信号
    void handleStopLearning(AsyncWebServerRequest* request);
    
    // POST /api/signals/learn/save - 保存学习的信号
    void handleSaveLearned(AsyncWebServerRequest* request);
    void handleSaveLearnedBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // GET /api/signals/learn/status - 获取学习状态
    void handleGetLearningStatus(AsyncWebServerRequest* request);
    
    // ==================== 导入导出操作 ====================
    
    // POST /api/signals/import - 导入信号
    void handleImportSignals(AsyncWebServerRequest* request);
    void handleImportSignalsBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/signals/import/text - 文本导入信号
    void handleImportSignalsText(AsyncWebServerRequest* request);
    void handleImportSignalsTextBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/signals/import/execute - 执行导入
    void handleExecuteImport(AsyncWebServerRequest* request);
    void handleExecuteImportBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/signals/import/text/execute - 执行文本导入
    void handleExecuteTextImport(AsyncWebServerRequest* request);
    void handleExecuteTextImportBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // GET /api/signals/export - 导出信号
    void handleExportSignals(AsyncWebServerRequest* request);
    
    // POST /api/signals/export/selected - 导出选中信号
    void handleExportSelectedSignals(AsyncWebServerRequest* request);
    void handleExportSelectedSignalsBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== 搜索和统计操作 ====================
    
    // GET /api/signals/search - 搜索信号
    void handleSearchSignals(AsyncWebServerRequest* request);
    
    // GET /api/signals/stats - 获取信号统计
    void handleGetSignalStats(AsyncWebServerRequest* request);
    
    // GET /api/signals/controller/status - 获取控制器状态
    void handleGetControllerStatus(AsyncWebServerRequest* request);
    
    // ==================== 辅助方法 ====================
    
    // 验证信号数据
    ValidationResult validateSignalData(const JsonObject& signalData);
    
    // 验证批量操作数据
    ValidationResult validateBatchData(const JsonArray& batchData);
    
    // 验证学习参数
    ValidationResult validateLearningParams(const JsonObject& params);
    
    // 验证发送参数
    ValidationResult validateSendParams(const JsonObject& params);
    
    // 验证搜索参数
    ValidationResult validateSearchParams(AsyncWebServerRequest* request);
    
    // 验证导入数据
    ValidationResult validateImportData(const JsonObject& importData);
    
    // 验证导出参数
    ValidationResult validateExportParams(const JsonObject& exportParams);
    
    // 生成学习会话ID
    String generateLearningSessionId();
    
    // 检查学习超时
    bool isLearningTimeout();
    
    // 清理学习状态
    void cleanupLearningState();
    
    // 更新批量操作进度
    void updateBatchProgress(uint32_t processed, bool success);
    
    // 重置批量操作状态
    void resetBatchState();
    
    // 格式化信号为JSON
    JsonObject formatSignalToJson(const IRSignal& signal, JsonDocument& doc);
    
    // 格式化信号列表为JSON
    JsonArray formatSignalsToJson(const std::vector<IRSignal>& signals, JsonDocument& doc);
    
    // 格式化学习状态为JSON
    JsonObject formatLearningStatusToJson(JsonDocument& doc);
    
    // 格式化批量操作状态为JSON
    JsonObject formatBatchStatusToJson(JsonDocument& doc);
    
    // 格式化统计信息为JSON
    JsonObject formatStatsToJson(JsonDocument& doc);
    
    // 格式化控制器状态为JSON
    JsonObject formatControllerStatusToJson(JsonDocument& doc);
    
    // 解析信号过滤参数
    struct SignalFilter {
        String name;
        String protocol;
        String category;
        bool onlyFavorites;
        Timestamp createdAfter;
        Timestamp createdBefore;
        
        SignalFilter() : onlyFavorites(false), createdAfter(0), createdBefore(0) {}
    };
    
    SignalFilter parseFilterParams(AsyncWebServerRequest* request);
    
    // 应用信号过滤
    std::vector<IRSignal> applySignalFilter(const std::vector<IRSignal>& signals, const SignalFilter& filter);
    
    // 处理信号排序
    void applySorting(std::vector<IRSignal>& signals, const SortParams& sortParams);
    
    // 生成导出文件名
    String generateExportFilename(const String& format = "json");
    
    // 验证导入文件格式
    ValidationResult validateImportFormat(const String& data, const String& format);
    
    // 解析导入数据
    std::vector<IRSignal> parseImportData(const String& data, const String& format);
    
    // 生成批量操作报告
    JsonObject generateBatchReport(JsonDocument& doc);
};
