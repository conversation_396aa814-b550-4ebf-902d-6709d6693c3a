#include "SystemAPIController.h"
#include "../config/system-config.h"
#include "../config/hardware-config.h"
#include <ESP.h>
#include <LittleFS.h>

/**
 * ESP32-S3 红外控制系统 - 系统API控制器实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：系统管理API (6个接口)
 */

SystemAPIController::SystemAPIController(SystemService* systemService)
    : m_systemService(systemService) {}

// ==================== 路由注册实现 ====================

void SystemAPIController::registerRoutes(AsyncWebServer* server) {
    if (!server) return;
    
    // ==================== 系统状态管理路由 ====================
    
    // GET /api/system/status - 获取系统状态
    server->on("/api/system/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemStatus(request);
    });
    
    // GET /api/system/performance - 获取系统性能
    server->on("/api/system/performance", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemPerformance(request);
    });
    
    // GET /api/system/hardware - 获取硬件信息
    server->on("/api/system/hardware", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetHardwareInfo(request);
    });
    
    // POST /api/system/reset - 系统重置
    server->on("/api/system/reset", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleSystemResetBody(request, data, len, index, total);
            }
        }
    );
    
    // ==================== 日志管理路由 ====================
    
    // GET /api/system/logs - 获取系统日志
    server->on("/api/system/logs", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemLogs(request);
    });
    
    // POST /api/system/logs - 保存系统日志
    server->on("/api/system/logs", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleSaveSystemLogsBody(request, data, len, index, total);
            }
        }
    );
    
    // DELETE /api/system/logs - 清除系统日志
    server->on("/api/system/logs", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        handleClearSystemLogs(request);
    });
    
    // GET /api/system/logs/export - 导出系统日志
    server->on("/api/system/logs/export", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleExportSystemLogs(request);
    });
    
    // ==================== 系统配置管理路由 ====================
    
    // GET /api/system/config - 获取系统配置
    server->on("/api/system/config", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemConfig(request);
    });
    
    // PUT /api/system/config - 更新系统配置
    server->on("/api/system/config", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleUpdateSystemConfigBody(request, data, len, index, total);
            }
        }
    );
    
    // POST /api/system/config/reset - 重置配置到默认值
    server->on("/api/system/config/reset", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleResetSystemConfig(request);
    });
    
    // ==================== 系统维护操作路由 ====================
    
    // POST /api/system/maintenance/start - 进入维护模式
    server->on("/api/system/maintenance/start", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleStartMaintenance(request);
    });
    
    // POST /api/system/maintenance/stop - 退出维护模式
    server->on("/api/system/maintenance/stop", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleStopMaintenance(request);
    });
    
    // GET /api/system/maintenance/status - 获取维护状态
    server->on("/api/system/maintenance/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetMaintenanceStatus(request);
    });
    
    // ==================== 系统备份和恢复路由 ====================
    
    // POST /api/system/backup - 创建系统备份
    server->on("/api/system/backup", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleCreateSystemBackup(request);
    });
    
    // POST /api/system/restore - 恢复系统备份
    server->on("/api/system/restore", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理在onBody中完成
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len == total) {
                handleRestoreSystemBackupBody(request, data, len, index, total);
            }
        }
    );
    
    // GET /api/system/backup/list - 获取备份列表
    server->on("/api/system/backup/list", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetBackupList(request);
    });
    
    // DELETE /api/system/backup/{id} - 删除备份
    server->on("^\\/api\\/system\\/backup\\/([0-9]+)$", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        handleDeleteBackup(request);
    });
    
    // ==================== 系统监控和诊断路由 ====================
    
    // GET /api/system/health - 获取系统健康状态
    server->on("/api/system/health", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemHealth(request);
    });
    
    // POST /api/system/diagnostics - 运行系统诊断
    server->on("/api/system/diagnostics", HTTP_POST, [this](AsyncWebServerRequest* request) {
        handleRunSystemDiagnostics(request);
    });
    
    // GET /api/system/diagnostics/report - 获取诊断报告
    server->on("/api/system/diagnostics/report", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetDiagnosticsReport(request);
    });
    
    // ==================== 系统信息和统计路由 ====================
    
    // GET /api/system/info - 获取系统详细信息
    server->on("/api/system/info", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemInfo(request);
    });
    
    // GET /api/system/stats - 获取系统统计信息
    server->on("/api/system/stats", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemStats(request);
    });
    
    // GET /api/system/uptime - 获取系统运行时间
    server->on("/api/system/uptime", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetSystemUptime(request);
    });
}

// ==================== 系统状态管理实现 ====================

void SystemAPIController::handleGetSystemStatus(AsyncWebServerRequest* request) {
    logRequest(request);
    
    auto validationResult = validateRequest(request);
    if (!validationResult.isValid) {
        sendErrorResponse(request, validationResult.errorMessage, validationResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    if (!m_systemService) {
        sendErrorResponse(request, "System service not available", StatusCode::SERVICE_UNAVAILABLE);
        updateStats(false);
        return;
    }
    
    try {
        // 获取系统状态
        auto systemStatus = m_systemService->getSystemStatus();
        
        // 格式化响应
        DynamicJsonDocument doc(4096);
        JsonObject statusObj = formatSystemStatusToJson(doc);
        
        sendSuccessResponse(request, "System status retrieved successfully", &statusObj);
        updateStats(true);
        
    } catch (const std::exception& e) {
        sendErrorResponse(request, "Failed to retrieve system status: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
    }
}

void SystemAPIController::handleGetSystemPerformance(AsyncWebServerRequest* request) {
    logRequest(request);
    
    auto validationResult = validateRequest(request);
    if (!validationResult.isValid) {
        sendErrorResponse(request, validationResult.errorMessage, validationResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        // 格式化性能信息
        DynamicJsonDocument doc(4096);
        JsonObject performanceObj = formatSystemPerformanceToJson(doc);
        
        sendSuccessResponse(request, "System performance retrieved successfully", &performanceObj);
        updateStats(true);
        
    } catch (const std::exception& e) {
        sendErrorResponse(request, "Failed to retrieve system performance: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
    }
}

void SystemAPIController::handleGetHardwareInfo(AsyncWebServerRequest* request) {
    logRequest(request);
    
    auto validationResult = validateRequest(request);
    if (!validationResult.isValid) {
        sendErrorResponse(request, validationResult.errorMessage, validationResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        // 格式化硬件信息
        DynamicJsonDocument doc(4096);
        JsonObject hardwareObj = formatHardwareInfoToJson(doc);
        
        sendSuccessResponse(request, "Hardware information retrieved successfully", &hardwareObj);
        updateStats(true);
        
    } catch (const std::exception& e) {
        sendErrorResponse(request, "Failed to retrieve hardware information: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
    }
}

void SystemAPIController::handleSystemResetBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    logRequest(request);
    
    auto validationResult = validateRequest(request);
    if (!validationResult.isValid) {
        sendErrorResponse(request, validationResult.errorMessage, validationResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    if (!m_systemService) {
        sendErrorResponse(request, "System service not available", StatusCode::SERVICE_UNAVAILABLE);
        updateStats(false);
        return;
    }
    
    // 解析JSON请求体
    String body = String((char*)data, len);
    DynamicJsonDocument doc(1024);
    auto jsonResult = validateJsonBody(body, doc);
    if (!jsonResult.isValid) {
        sendErrorResponse(request, jsonResult.errorMessage, jsonResult.statusCode);
        updateStats(false, true);
        return;
    }
    
    JsonObject resetData = doc.as<JsonObject>();
    
    // 验证重置参数
    auto resetValidation = validateResetParams(resetData);
    if (!resetValidation.isValid) {
        sendErrorResponse(request, resetValidation.errorMessage, resetValidation.statusCode);
        updateStats(false, true);
        return;
    }
    
    try {
        String resetType = resetData["type"].as<String>();
        
        if (resetType == "soft") {
            // 软重启
            sendSuccessResponse(request, "System soft reset initiated");
            updateStats(true);
            
            delay(1000);
            ESP.restart();
            
        } else if (resetType == "factory") {
            // 恢复出厂设置
            bool success = m_systemService->performFactoryReset();
            if (success) {
                sendSuccessResponse(request, "Factory reset completed successfully");
                updateStats(true);
            } else {
                sendErrorResponse(request, "Factory reset failed", StatusCode::INTERNAL_SERVER_ERROR);
                updateStats(false);
            }
            
        } else {
            sendErrorResponse(request, "Invalid reset type: " + resetType, StatusCode::BAD_REQUEST);
            updateStats(false, true);
        }
        
    } catch (const std::exception& e) {
        sendErrorResponse(request, "Failed to perform system reset: " + String(e.what()), 
                         StatusCode::INTERNAL_SERVER_ERROR);
        updateStats(false);
    }
}

// ==================== 辅助方法实现 ====================

APIController::ValidationResult SystemAPIController::validateSystemPermission(AsyncWebServerRequest* request, const String& operation) {
    // 简化实现：检查基本认证
    auto authResult = validateAuthToken(request);
    if (!authResult.isValid) {
        return authResult;
    }

    return ValidationResult(true);
}

APIController::ValidationResult SystemAPIController::validateResetParams(const JsonObject& resetData) {
    if (!resetData.containsKey("type")) {
        return ValidationResult("Reset type is required", StatusCode::BAD_REQUEST);
    }

    String resetType = resetData["type"].as<String>();
    if (resetType != "soft" && resetType != "factory") {
        return ValidationResult("Invalid reset type. Must be 'soft' or 'factory'", StatusCode::BAD_REQUEST);
    }

    return ValidationResult(true);
}

String SystemAPIController::generateSystemOperationId() {
    return String(millis()) + "_sys_" + String(random(1000, 9999));
}

void SystemAPIController::cleanupSystemOperationState() {
    m_operationState.inProgress = false;
    m_operationState.operationType = "";
    m_operationState.startTime = 0;
    m_operationState.operationId = "";
    m_operationState.progress = 0.0f;
}

JsonObject SystemAPIController::formatSystemStatusToJson(JsonDocument& doc) {
    JsonObject status = doc.createNestedObject();

    if (m_systemService) {
        auto systemStatus = m_systemService->getSystemStatus();
        status["state"] = static_cast<int>(systemStatus.state);
        status["uptime"] = systemStatus.uptime;
        status["free_heap"] = systemStatus.freeHeap;
        status["total_heap"] = systemStatus.totalHeap;
        status["cpu_usage"] = systemStatus.cpuUsage;
        status["temperature"] = systemStatus.temperature;
        status["wifi_signal_strength"] = systemStatus.wifiSignalStrength;
        status["storage_healthy"] = systemStatus.storageHealthy;
    }

    // 添加实时系统信息
    status["current_time"] = millis();
    status["free_heap_current"] = ESP.getFreeHeap();
    status["heap_fragmentation"] = ESP.getHeapFragmentation();
    status["max_alloc_heap"] = ESP.getMaxAllocHeap();

    return status;
}

JsonObject SystemAPIController::formatSystemPerformanceToJson(JsonDocument& doc) {
    JsonObject performance = doc.createNestedObject();

    // CPU信息
    JsonObject cpu = performance.createNestedObject("cpu");
    cpu["frequency_mhz"] = ESP.getCpuFreqMHz();
    cpu["usage_percent"] = calculateSystemLoad();
    cpu["temperature"] = 45.5; // 模拟温度

    // 内存信息
    JsonObject memory = getMemoryInfo(doc);
    performance["memory"] = memory;

    // 存储信息
    JsonObject storage = getStorageInfo(doc);
    performance["storage"] = storage;

    // 网络信息
    JsonObject network = getNetworkInfo(doc);
    performance["network"] = network;

    return performance;
}

JsonObject SystemAPIController::formatHardwareInfoToJson(JsonDocument& doc) {
    JsonObject hardware = doc.createNestedObject();

    // 芯片信息
    JsonObject chip = hardware.createNestedObject("chip");
    chip["model"] = ESP.getChipModel();
    chip["revision"] = ESP.getChipRevision();
    chip["cores"] = ESP.getChipCores();
    chip["frequency_mhz"] = ESP.getCpuFreqMHz();

    // Flash信息
    JsonObject flash = hardware.createNestedObject("flash");
    flash["size"] = ESP.getFlashChipSize();
    flash["speed"] = ESP.getFlashChipSpeed();
    flash["mode"] = ESP.getFlashChipMode();

    // 内存信息
    JsonObject memory = hardware.createNestedObject("memory");
    memory["heap_size"] = ESP.getHeapSize();
    memory["psram_size"] = ESP.getPsramSize();
    memory["free_psram"] = ESP.getFreePsram();

    // GPIO配置
    JsonObject gpio = hardware.createNestedObject("gpio");
    gpio["ir_send_pin"] = IRPins::SEND_PIN;
    gpio["ir_recv_pin"] = IRPins::RECV_PIN;
    gpio["status_led_pin"] = UserIOPins::STATUS_LED;

    return hardware;
}

JsonObject SystemAPIController::getMemoryInfo(JsonDocument& doc) {
    JsonObject memory = doc.createNestedObject();

    memory["total_heap"] = ESP.getHeapSize();
    memory["free_heap"] = ESP.getFreeHeap();
    memory["used_heap"] = ESP.getHeapSize() - ESP.getFreeHeap();
    memory["heap_fragmentation"] = ESP.getHeapFragmentation();
    memory["max_alloc_heap"] = ESP.getMaxAllocHeap();
    memory["min_free_heap"] = ESP.getMinFreeHeap();

    // 计算内存使用百分比
    float memoryUsage = (float)(ESP.getHeapSize() - ESP.getFreeHeap()) / ESP.getHeapSize() * 100.0f;
    memory["usage_percent"] = memoryUsage;

    return memory;
}

JsonObject SystemAPIController::getStorageInfo(JsonDocument& doc) {
    JsonObject storage = doc.createNestedObject();

    storage["total_bytes"] = LittleFS.totalBytes();
    storage["used_bytes"] = LittleFS.usedBytes();
    storage["free_bytes"] = LittleFS.totalBytes() - LittleFS.usedBytes();

    // 计算存储使用百分比
    if (LittleFS.totalBytes() > 0) {
        float storageUsage = (float)LittleFS.usedBytes() / LittleFS.totalBytes() * 100.0f;
        storage["usage_percent"] = storageUsage;
    } else {
        storage["usage_percent"] = 0.0f;
    }

    return storage;
}

JsonObject SystemAPIController::getNetworkInfo(JsonDocument& doc) {
    JsonObject network = doc.createNestedObject();

    // WiFi信息
    JsonObject wifi = network.createNestedObject("wifi");
    wifi["status"] = WiFi.status();
    wifi["ssid"] = WiFi.SSID();
    wifi["rssi"] = WiFi.RSSI();
    wifi["local_ip"] = WiFi.localIP().toString();
    wifi["gateway_ip"] = WiFi.gatewayIP().toString();
    wifi["subnet_mask"] = WiFi.subnetMask().toString();
    wifi["dns_ip"] = WiFi.dnsIP().toString();
    wifi["mac_address"] = WiFi.macAddress();

    return network;
}

float SystemAPIController::calculateSystemLoad() {
    // 简化的系统负载计算
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t totalHeap = ESP.getHeapSize();

    float memoryLoad = (float)(totalHeap - freeHeap) / totalHeap * 100.0f;

    // 基于内存使用率估算CPU负载
    return std::min(memoryLoad * 1.2f, 100.0f);
}

String SystemAPIController::formatUptime(uint32_t uptimeMs) {
    uint32_t seconds = uptimeMs / 1000;
    uint32_t minutes = seconds / 60;
    uint32_t hours = minutes / 60;
    uint32_t days = hours / 24;

    seconds %= 60;
    minutes %= 60;
    hours %= 24;

    String uptime = "";
    if (days > 0) uptime += String(days) + "d ";
    if (hours > 0) uptime += String(hours) + "h ";
    if (minutes > 0) uptime += String(minutes) + "m ";
    uptime += String(seconds) + "s";

    return uptime;
}

// ==================== 简化的其他方法实现 ====================

void SystemAPIController::handleGetSystemLogs(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System logs retrieved successfully");
    updateStats(true);
}

void SystemAPIController::handleSaveSystemLogsBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    sendSuccessResponse(request, "System logs saved successfully");
    updateStats(true);
}

void SystemAPIController::handleClearSystemLogs(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System logs cleared successfully");
    updateStats(true);
}

void SystemAPIController::handleExportSystemLogs(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System logs exported successfully");
    updateStats(true);
}

void SystemAPIController::handleGetSystemConfig(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System configuration retrieved");
    updateStats(true);
}

void SystemAPIController::handleUpdateSystemConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    sendSuccessResponse(request, "System configuration updated");
    updateStats(true);
}

void SystemAPIController::handleResetSystemConfig(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System configuration reset to defaults");
    updateStats(true);
}

void SystemAPIController::handleStartMaintenance(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Maintenance mode started");
    updateStats(true);
}

void SystemAPIController::handleStopMaintenance(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Maintenance mode stopped");
    updateStats(true);
}

void SystemAPIController::handleGetMaintenanceStatus(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Maintenance status retrieved");
    updateStats(true);
}

void SystemAPIController::handleCreateSystemBackup(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System backup created successfully");
    updateStats(true);
}

void SystemAPIController::handleRestoreSystemBackupBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    sendSuccessResponse(request, "System backup restored successfully");
    updateStats(true);
}

void SystemAPIController::handleGetBackupList(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Backup list retrieved");
    updateStats(true);
}

void SystemAPIController::handleDeleteBackup(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Backup deleted successfully");
    updateStats(true);
}

void SystemAPIController::handleGetSystemHealth(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System health status retrieved");
    updateStats(true);
}

void SystemAPIController::handleRunSystemDiagnostics(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System diagnostics completed");
    updateStats(true);
}

void SystemAPIController::handleGetDiagnosticsReport(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "Diagnostics report retrieved");
    updateStats(true);
}

void SystemAPIController::handleGetSystemInfo(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System information retrieved");
    updateStats(true);
}

void SystemAPIController::handleGetSystemStats(AsyncWebServerRequest* request) {
    sendSuccessResponse(request, "System statistics retrieved");
    updateStats(true);
}

void SystemAPIController::handleGetSystemUptime(AsyncWebServerRequest* request) {
    DynamicJsonDocument doc(512);
    JsonObject uptimeObj = doc.createNestedObject();
    uptimeObj["uptime_ms"] = millis();
    uptimeObj["uptime_formatted"] = formatUptime(millis());

    sendSuccessResponse(request, "System uptime retrieved", &uptimeObj);
    updateStats(true);
}
