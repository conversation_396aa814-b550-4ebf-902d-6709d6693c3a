#pragma once

#include "APIController.h"
#include "../services/SystemService.h"

/**
 * ESP32-S3 红外控制系统 - 系统API控制器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：系统管理API (6个接口)
 * 
 * 系统API控制器职责：
 * - 处理所有系统相关的HTTP请求
 * - 系统状态监控和管理
 * - 系统性能和硬件信息获取
 * - 系统重置和日志管理
 */

class SystemAPIController : public APIController {
private:
    SystemService* m_systemService;
    
    // 系统操作状态管理
    struct SystemOperationState {
        bool inProgress;
        String operationType;
        Timestamp startTime;
        String operationId;
        float progress;
        
        SystemOperationState() : inProgress(false), startTime(0), progress(0.0f) {}
    };
    
    SystemOperationState m_operationState;
    
    // 系统监控状态
    struct SystemMonitoringState {
        bool isMonitoring;
        uint32_t monitoringInterval;
        Timestamp lastCheck;
        uint32_t checkCount;
        
        SystemMonitoringState() : isMonitoring(false), monitoringInterval(5000), 
                                 lastCheck(0), checkCount(0) {}
    };
    
    SystemMonitoringState m_monitoringState;

public:
    SystemAPIController(SystemService* systemService);
    ~SystemAPIController() override = default;
    
    // 基类接口实现
    void registerRoutes(AsyncWebServer* server) override;
    const char* getControllerName() const override { return "SystemAPIController"; }

private:
    // ==================== 系统状态管理 ====================
    
    // GET /api/system/status - 获取系统状态
    void handleGetSystemStatus(AsyncWebServerRequest* request);
    
    // GET /api/system/performance - 获取系统性能
    void handleGetSystemPerformance(AsyncWebServerRequest* request);
    
    // GET /api/system/hardware - 获取硬件信息
    void handleGetHardwareInfo(AsyncWebServerRequest* request);
    
    // POST /api/system/reset - 系统重置
    void handleSystemReset(AsyncWebServerRequest* request);
    void handleSystemResetBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== 日志管理 ====================
    
    // GET /api/system/logs - 获取系统日志
    void handleGetSystemLogs(AsyncWebServerRequest* request);
    
    // POST /api/system/logs - 保存系统日志
    void handleSaveSystemLogs(AsyncWebServerRequest* request);
    void handleSaveSystemLogsBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // DELETE /api/system/logs - 清除系统日志
    void handleClearSystemLogs(AsyncWebServerRequest* request);
    
    // GET /api/system/logs/export - 导出系统日志
    void handleExportSystemLogs(AsyncWebServerRequest* request);
    
    // ==================== 系统配置管理 ====================
    
    // GET /api/system/config - 获取系统配置
    void handleGetSystemConfig(AsyncWebServerRequest* request);
    
    // PUT /api/system/config - 更新系统配置
    void handleUpdateSystemConfig(AsyncWebServerRequest* request);
    void handleUpdateSystemConfigBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/system/config/reset - 重置配置到默认值
    void handleResetSystemConfig(AsyncWebServerRequest* request);
    
    // ==================== 系统维护操作 ====================
    
    // POST /api/system/maintenance/start - 进入维护模式
    void handleStartMaintenance(AsyncWebServerRequest* request);
    
    // POST /api/system/maintenance/stop - 退出维护模式
    void handleStopMaintenance(AsyncWebServerRequest* request);
    
    // GET /api/system/maintenance/status - 获取维护状态
    void handleGetMaintenanceStatus(AsyncWebServerRequest* request);
    
    // ==================== 系统备份和恢复 ====================
    
    // POST /api/system/backup - 创建系统备份
    void handleCreateSystemBackup(AsyncWebServerRequest* request);
    
    // POST /api/system/restore - 恢复系统备份
    void handleRestoreSystemBackup(AsyncWebServerRequest* request);
    void handleRestoreSystemBackupBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // GET /api/system/backup/list - 获取备份列表
    void handleGetBackupList(AsyncWebServerRequest* request);
    
    // DELETE /api/system/backup/{id} - 删除备份
    void handleDeleteBackup(AsyncWebServerRequest* request);
    
    // ==================== 系统监控和诊断 ====================
    
    // GET /api/system/health - 获取系统健康状态
    void handleGetSystemHealth(AsyncWebServerRequest* request);
    
    // POST /api/system/diagnostics - 运行系统诊断
    void handleRunSystemDiagnostics(AsyncWebServerRequest* request);
    
    // GET /api/system/diagnostics/report - 获取诊断报告
    void handleGetDiagnosticsReport(AsyncWebServerRequest* request);
    
    // ==================== 系统信息和统计 ====================
    
    // GET /api/system/info - 获取系统详细信息
    void handleGetSystemInfo(AsyncWebServerRequest* request);
    
    // GET /api/system/stats - 获取系统统计信息
    void handleGetSystemStats(AsyncWebServerRequest* request);
    
    // GET /api/system/uptime - 获取系统运行时间
    void handleGetSystemUptime(AsyncWebServerRequest* request);
    
    // ==================== 辅助方法 ====================
    
    // 验证系统操作权限
    ValidationResult validateSystemPermission(AsyncWebServerRequest* request, const String& operation);
    
    // 验证重置参数
    ValidationResult validateResetParams(const JsonObject& resetData);
    
    // 验证配置数据
    ValidationResult validateConfigData(const JsonObject& configData);
    
    // 验证备份数据
    ValidationResult validateBackupData(const JsonObject& backupData);
    
    // 生成操作ID
    String generateSystemOperationId();
    
    // 检查操作超时
    bool isSystemOperationTimeout();
    
    // 清理操作状态
    void cleanupSystemOperationState();
    
    // 更新操作进度
    void updateSystemOperationProgress(float progress);
    
    // 格式化系统状态为JSON
    JsonObject formatSystemStatusToJson(JsonDocument& doc);
    
    // 格式化系统性能为JSON
    JsonObject formatSystemPerformanceToJson(JsonDocument& doc);
    
    // 格式化硬件信息为JSON
    JsonObject formatHardwareInfoToJson(JsonDocument& doc);
    
    // 格式化系统日志为JSON
    JsonArray formatSystemLogsToJson(const std::vector<SystemService::LogEntry>& logs, JsonDocument& doc);
    
    // 格式化系统配置为JSON
    JsonObject formatSystemConfigToJson(JsonDocument& doc);
    
    // 格式化系统健康状态为JSON
    JsonObject formatSystemHealthToJson(JsonDocument& doc);
    
    // 格式化系统统计为JSON
    JsonObject formatSystemStatsToJson(JsonDocument& doc);
    
    // 格式化备份列表为JSON
    JsonArray formatBackupListToJson(JsonDocument& doc);
    
    // 格式化诊断报告为JSON
    JsonObject formatDiagnosticsReportToJson(JsonDocument& doc);
    
    // 解析日志过滤参数
    struct LogFilter {
        SystemService::LogLevel minLevel;
        String component;
        Timestamp startTime;
        Timestamp endTime;
        uint32_t maxEntries;
        
        LogFilter() : minLevel(SystemService::LogLevel::DEBUG), startTime(0), 
                     endTime(0), maxEntries(1000) {}
    };
    
    LogFilter parseLogFilterParams(AsyncWebServerRequest* request);
    
    // 应用日志过滤
    std::vector<SystemService::LogEntry> applyLogFilter(const std::vector<SystemService::LogEntry>& logs, 
                                                       const LogFilter& filter);
    
    // 获取系统内存信息
    JsonObject getMemoryInfo(JsonDocument& doc);
    
    // 获取系统CPU信息
    JsonObject getCPUInfo(JsonDocument& doc);
    
    // 获取系统网络信息
    JsonObject getNetworkInfo(JsonDocument& doc);
    
    // 获取系统存储信息
    JsonObject getStorageInfo(JsonDocument& doc);
    
    // 获取系统温度信息
    JsonObject getTemperatureInfo(JsonDocument& doc);
    
    // 执行系统健康检查
    JsonObject performHealthCheck(JsonDocument& doc);
    
    // 生成系统报告
    JsonObject generateSystemReport(JsonDocument& doc);
    
    // 验证系统安全状态
    bool validateSystemSecurity();
    
    // 检查系统资源使用
    JsonObject checkResourceUsage(JsonDocument& doc);
    
    // 格式化运行时间
    String formatUptime(uint32_t uptimeMs);
    
    // 计算系统负载
    float calculateSystemLoad();
    
    // 获取系统版本信息
    JsonObject getVersionInfo(JsonDocument& doc);
};
