#pragma once

#include "APIController.h"
#include "../services/TaskService.h"

/**
 * ESP32-S3 红外控制系统 - 任务API控制器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：任务管理API (2个接口)
 * 
 * 任务API控制器职责：
 * - 处理所有任务相关的HTTP请求
 * - 任务CRUD操作
 * - 任务执行和调度
 * - 任务状态管理
 */

class TaskAPIController : public APIController {
private:
    TaskService* m_taskService;
    
    // 任务执行状态管理
    struct TaskExecutionState {
        bool isExecuting;
        uint32_t currentTaskId;
        Timestamp startTime;
        String executionId;
        
        TaskExecutionState() : isExecuting(false), currentTaskId(INVALID_ID), startTime(0) {}
    };
    
    TaskExecutionState m_executionState;
    
    // 批量任务操作状态
    struct BatchTaskState {
        bool inProgress;
        String operationType;
        std::vector<uint32_t> taskIds;
        uint32_t totalTasks;
        uint32_t processedTasks;
        uint32_t successCount;
        uint32_t errorCount;
        Timestamp startTime;
        
        BatchTaskState() : inProgress(false), totalTasks(0), processedTasks(0),
                          successCount(0), errorCount(0), startTime(0) {}
    };
    
    BatchTaskState m_batchState;

public:
    TaskAPIController(TaskService* taskService);
    ~TaskAPIController() override = default;
    
    // 基类接口实现
    void registerRoutes(AsyncWebServer* server) override;
    const char* getControllerName() const override { return "TaskAPIController"; }

private:
    // ==================== 基础CRUD操作 ====================
    
    // GET /api/tasks - 获取所有任务
    void handleGetTasks(AsyncWebServerRequest* request);
    
    // POST /api/tasks - 创建新任务
    void handleCreateTask(AsyncWebServerRequest* request);
    void handleCreateTaskBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // GET /api/tasks/{id} - 获取特定任务
    void handleGetTask(AsyncWebServerRequest* request);
    
    // PUT /api/tasks/{id} - 更新任务
    void handleUpdateTask(AsyncWebServerRequest* request);
    void handleUpdateTaskBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // DELETE /api/tasks/{id} - 删除任务
    void handleDeleteTask(AsyncWebServerRequest* request);
    
    // ==================== 任务执行操作 ====================
    
    // POST /api/tasks/{id}/execute - 执行任务
    void handleExecuteTask(AsyncWebServerRequest* request);
    
    // POST /api/tasks/{id}/stop - 停止任务执行
    void handleStopTask(AsyncWebServerRequest* request);
    
    // GET /api/tasks/{id}/status - 获取任务执行状态
    void handleGetTaskStatus(AsyncWebServerRequest* request);
    
    // ==================== 批量操作 ====================
    
    // POST /api/tasks/batch-execute - 批量执行任务
    void handleBatchExecuteTasks(AsyncWebServerRequest* request);
    void handleBatchExecuteTasksBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/tasks/batch-delete - 批量删除任务
    void handleBatchDeleteTasks(AsyncWebServerRequest* request);
    void handleBatchDeleteTasksBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== 任务调度操作 ====================
    
    // POST /api/tasks/{id}/schedule - 调度任务
    void handleScheduleTask(AsyncWebServerRequest* request);
    void handleScheduleTaskBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // DELETE /api/tasks/{id}/schedule - 取消任务调度
    void handleUnscheduleTask(AsyncWebServerRequest* request);
    
    // GET /api/tasks/scheduled - 获取已调度任务
    void handleGetScheduledTasks(AsyncWebServerRequest* request);
    
    // ==================== 任务统计和监控 ====================
    
    // GET /api/tasks/stats - 获取任务统计
    void handleGetTaskStats(AsyncWebServerRequest* request);
    
    // GET /api/tasks/execution/history - 获取执行历史
    void handleGetExecutionHistory(AsyncWebServerRequest* request);
    
    // GET /api/tasks/queue/status - 获取任务队列状态
    void handleGetQueueStatus(AsyncWebServerRequest* request);
    
    // ==================== 辅助方法 ====================
    
    // 验证任务数据
    ValidationResult validateTaskData(const JsonObject& taskData);
    
    // 验证批量任务数据
    ValidationResult validateBatchTaskData(const JsonArray& batchData);
    
    // 验证调度参数
    ValidationResult validateScheduleParams(const JsonObject& scheduleData);
    
    // 验证执行参数
    ValidationResult validateExecutionParams(const JsonObject& executionData);
    
    // 验证任务ID列表
    ValidationResult validateTaskIdList(const JsonArray& taskIds);
    
    // 生成执行ID
    String generateExecutionId();
    
    // 检查执行超时
    bool isExecutionTimeout();
    
    // 清理执行状态
    void cleanupExecutionState();
    
    // 更新批量操作进度
    void updateBatchTaskProgress(uint32_t processed, bool success);
    
    // 重置批量操作状态
    void resetBatchTaskState();
    
    // 格式化任务为JSON
    JsonObject formatTaskToJson(const Task& task, JsonDocument& doc);
    
    // 格式化任务列表为JSON
    JsonArray formatTasksToJson(const std::vector<Task>& tasks, JsonDocument& doc);
    
    // 格式化执行状态为JSON
    JsonObject formatExecutionStatusToJson(JsonDocument& doc);
    
    // 格式化批量操作状态为JSON
    JsonObject formatBatchTaskStatusToJson(JsonDocument& doc);
    
    // 格式化任务统计为JSON
    JsonObject formatTaskStatsToJson(JsonDocument& doc);
    
    // 格式化队列状态为JSON
    JsonObject formatQueueStatusToJson(JsonDocument& doc);
    
    // 解析任务过滤参数
    struct TaskFilter {
        String name;
        TaskStatus status;
        TaskType type;
        TaskPriority priority;
        bool onlyScheduled;
        Timestamp createdAfter;
        Timestamp createdBefore;
        
        TaskFilter() : status(TaskStatus::PENDING), type(TaskType::SIGNAL_SEND), 
                      priority(TaskPriority::NORMAL), onlyScheduled(false),
                      createdAfter(0), createdBefore(0) {}
    };
    
    TaskFilter parseTaskFilterParams(AsyncWebServerRequest* request);
    
    // 应用任务过滤
    std::vector<Task> applyTaskFilter(const std::vector<Task>& tasks, const TaskFilter& filter);
    
    // 处理任务排序
    void applyTaskSorting(std::vector<Task>& tasks, const SortParams& sortParams);
    
    // 验证任务权限
    ValidationResult validateTaskPermission(AsyncWebServerRequest* request, uint32_t taskId, const String& action);
    
    // 检查任务依赖
    ValidationResult validateTaskDependencies(const Task& task);
    
    // 生成任务执行报告
    JsonObject generateTaskExecutionReport(const Task& task, JsonDocument& doc);
    
    // 生成批量操作报告
    JsonObject generateBatchTaskReport(JsonDocument& doc);
    
    // 检查任务冲突
    ValidationResult checkTaskConflicts(const Task& task);
    
    // 验证任务时间窗口
    ValidationResult validateTaskTimeWindow(const JsonObject& scheduleData);
    
    // 计算任务优先级
    uint8_t calculateTaskPriority(const Task& task);
    
    // 格式化执行历史为JSON
    JsonArray formatExecutionHistoryToJson(const std::vector<TaskExecution>& history, JsonDocument& doc);
};
