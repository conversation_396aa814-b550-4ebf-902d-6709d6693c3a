#pragma once

#include "APIController.h"
#include "../services/TimerService.h"

/**
 * ESP32-S3 红外控制系统 - 定时器API控制器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第五部分：定时器管理API (6个接口)
 * 
 * 定时器API控制器职责：
 * - 处理所有定时器相关的HTTP请求
 * - 定时器CRUD操作
 * - 定时器启用/禁用控制
 * - 定时器状态管理和监控
 */

class TimerAPIController : public APIController {
private:
    TimerService* m_timerService;
    
    // 定时器操作状态管理
    struct TimerOperationState {
        bool inProgress;
        uint32_t currentTimerId;
        String operationType;
        Timestamp startTime;
        String operationId;
        
        TimerOperationState() : inProgress(false), currentTimerId(INVALID_ID), startTime(0) {}
    };
    
    TimerOperationState m_operationState;
    
    // 批量定时器操作状态
    struct BatchTimerState {
        bool inProgress;
        String operationType;
        std::vector<uint32_t> timerIds;
        uint32_t totalTimers;
        uint32_t processedTimers;
        uint32_t successCount;
        uint32_t errorCount;
        Timestamp startTime;
        
        BatchTimerState() : inProgress(false), totalTimers(0), processedTimers(0),
                           successCount(0), errorCount(0), startTime(0) {}
    };
    
    BatchTimerState m_batchState;

public:
    TimerAPIController(TimerService* timerService);
    ~TimerAPIController() override = default;
    
    // 基类接口实现
    void registerRoutes(AsyncWebServer* server) override;
    const char* getControllerName() const override { return "TimerAPIController"; }

private:
    // ==================== 基础CRUD操作 ====================
    
    // GET /api/timers - 获取所有定时器
    void handleGetTimers(AsyncWebServerRequest* request);
    
    // POST /api/timers - 创建新定时器
    void handleCreateTimer(AsyncWebServerRequest* request);
    void handleCreateTimerBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // GET /api/timers/{id} - 获取特定定时器
    void handleGetTimer(AsyncWebServerRequest* request);
    
    // PUT /api/timers/{id} - 更新定时器
    void handleUpdateTimer(AsyncWebServerRequest* request);
    void handleUpdateTimerBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // DELETE /api/timers/{id} - 删除定时器
    void handleDeleteTimer(AsyncWebServerRequest* request);
    
    // POST /api/timers/{id}/toggle - 切换定时器状态
    void handleToggleTimer(AsyncWebServerRequest* request);
    
    // ==================== 定时器控制操作 ====================
    
    // POST /api/timers/{id}/enable - 启用定时器
    void handleEnableTimer(AsyncWebServerRequest* request);
    
    // POST /api/timers/{id}/disable - 禁用定时器
    void handleDisableTimer(AsyncWebServerRequest* request);
    
    // POST /api/timers/{id}/trigger - 手动触发定时器
    void handleTriggerTimer(AsyncWebServerRequest* request);
    
    // GET /api/timers/{id}/status - 获取定时器状态
    void handleGetTimerStatus(AsyncWebServerRequest* request);
    
    // ==================== 批量操作 ====================
    
    // POST /api/timers/batch-enable - 批量启用定时器
    void handleBatchEnableTimers(AsyncWebServerRequest* request);
    void handleBatchEnableTimersBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/timers/batch-disable - 批量禁用定时器
    void handleBatchDisableTimers(AsyncWebServerRequest* request);
    void handleBatchDisableTimersBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/timers/batch-delete - 批量删除定时器
    void handleBatchDeleteTimers(AsyncWebServerRequest* request);
    void handleBatchDeleteTimersBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== 定时器监控和统计 ====================
    
    // GET /api/timers/stats - 获取定时器统计
    void handleGetTimerStats(AsyncWebServerRequest* request);
    
    // GET /api/timers/active - 获取活跃定时器
    void handleGetActiveTimers(AsyncWebServerRequest* request);
    
    // GET /api/timers/upcoming - 获取即将触发的定时器
    void handleGetUpcomingTimers(AsyncWebServerRequest* request);
    
    // GET /api/timers/history - 获取定时器执行历史
    void handleGetTimerHistory(AsyncWebServerRequest* request);
    
    // ==================== 定时器配置操作 ====================
    
    // POST /api/timers/{id}/schedule - 重新调度定时器
    void handleRescheduleTimer(AsyncWebServerRequest* request);
    void handleRescheduleTimerBody(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // POST /api/timers/{id}/pause - 暂停定时器
    void handlePauseTimer(AsyncWebServerRequest* request);
    
    // POST /api/timers/{id}/resume - 恢复定时器
    void handleResumeTimer(AsyncWebServerRequest* request);
    
    // ==================== 辅助方法 ====================
    
    // 验证定时器数据
    ValidationResult validateTimerData(const JsonObject& timerData);
    
    // 验证批量定时器数据
    ValidationResult validateBatchTimerData(const JsonArray& batchData);
    
    // 验证定时器时间配置
    ValidationResult validateTimerTimeConfig(const JsonObject& timeConfig);
    
    // 验证定时器重复配置
    ValidationResult validateTimerRepeatConfig(const JsonObject& repeatConfig);
    
    // 验证定时器ID列表
    ValidationResult validateTimerIdList(const JsonArray& timerIds);
    
    // 生成操作ID
    String generateOperationId();
    
    // 检查操作超时
    bool isOperationTimeout();
    
    // 清理操作状态
    void cleanupOperationState();
    
    // 更新批量操作进度
    void updateBatchTimerProgress(uint32_t processed, bool success);
    
    // 重置批量操作状态
    void resetBatchTimerState();
    
    // 格式化定时器为JSON
    JsonObject formatTimerToJson(const Timer& timer, JsonDocument& doc);
    
    // 格式化定时器列表为JSON
    JsonArray formatTimersToJson(const std::vector<Timer>& timers, JsonDocument& doc);
    
    // 格式化定时器状态为JSON
    JsonObject formatTimerStatusToJson(const Timer& timer, JsonDocument& doc);
    
    // 格式化批量操作状态为JSON
    JsonObject formatBatchTimerStatusToJson(JsonDocument& doc);
    
    // 格式化定时器统计为JSON
    JsonObject formatTimerStatsToJson(JsonDocument& doc);
    
    // 格式化定时器历史为JSON
    JsonArray formatTimerHistoryToJson(const std::vector<TimerExecution>& history, JsonDocument& doc);
    
    // 解析定时器过滤参数
    struct TimerFilter {
        String name;
        bool onlyEnabled;
        bool onlyActive;
        TimerType type;
        Timestamp nextTriggerAfter;
        Timestamp nextTriggerBefore;
        Timestamp createdAfter;
        Timestamp createdBefore;
        
        TimerFilter() : onlyEnabled(false), onlyActive(false), type(TimerType::ONCE),
                       nextTriggerAfter(0), nextTriggerBefore(0),
                       createdAfter(0), createdBefore(0) {}
    };
    
    TimerFilter parseTimerFilterParams(AsyncWebServerRequest* request);
    
    // 应用定时器过滤
    std::vector<Timer> applyTimerFilter(const std::vector<Timer>& timers, const TimerFilter& filter);
    
    // 处理定时器排序
    void applyTimerSorting(std::vector<Timer>& timers, const SortParams& sortParams);
    
    // 验证定时器权限
    ValidationResult validateTimerPermission(AsyncWebServerRequest* request, uint32_t timerId, const String& action);
    
    // 检查定时器冲突
    ValidationResult checkTimerConflicts(const Timer& timer);
    
    // 验证定时器时间有效性
    ValidationResult validateTimerTime(const Timer& timer);
    
    // 计算下次触发时间
    Timestamp calculateNextTriggerTime(const Timer& timer);
    
    // 生成定时器执行报告
    JsonObject generateTimerExecutionReport(const Timer& timer, JsonDocument& doc);
    
    // 生成批量操作报告
    JsonObject generateBatchTimerReport(JsonDocument& doc);
    
    // 检查定时器依赖
    ValidationResult validateTimerDependencies(const Timer& timer);
    
    // 格式化时间为可读字符串
    String formatTimeToString(Timestamp timestamp);
    
    // 解析时间字符串
    Timestamp parseTimeString(const String& timeStr);
    
    // 验证Cron表达式
    ValidationResult validateCronExpression(const String& cronExpr);
};
