#include "DataStructures.h"
#include "JSONConverter.h"

/**
 * ESP32-S3 红外控制系统 - 核心数据结构实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第三部分：统一数据结构定义
 * 
 * 实现数据结构的JSON转换成员方法，符合标准文档第3.3节第208-209行要求
 */

// ==================== SignalData JSON转换实现 ====================

JsonObject SignalData::toJson(JsonDocument& doc) const {
    return JSONConverter::signalToJson(*this, doc);
}

Result<SignalData> SignalData::fromJson(const JsonObject& json) {
    return JSONConverter::signalFromJson(json);
}

// ==================== TaskData JSON转换实现 ====================

JsonObject TaskData::toJson(JsonDocument& doc) const {
    return JSONConverter::taskToJson(*this, doc);
}

Result<TaskData> TaskData::fromJson(const JsonObject& json) {
    return JSONConverter::taskFromJson(json);
}

// ==================== TimerData JSON转换实现 ====================

JsonObject TimerData::toJson(JsonDocument& doc) const {
    return JSONConverter::timerToJson(*this, doc);
}

Result<TimerData> TimerData::fromJson(const JsonObject& json) {
    return JSONConverter::timerFromJson(json);
}

// ==================== ConfigData JSON转换实现 ====================

JsonObject ConfigData::toJson(JsonDocument& doc) const {
    return JSONConverter::configToJson(*this, doc);
}

Result<ConfigData> ConfigData::fromJson(const JsonObject& json) {
    return JSONConverter::configFromJson(json);
}
