#pragma once

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>

/**
 * ESP32-S3 红外控制系统 - 核心数据结构定义
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 * 第三部分：统一数据结构定义 (第100-181行)
 */

// ==================== 基础类型定义 ====================

// 统一ID类型
using EntityID = uint32_t;
using SignalID = EntityID;
using TaskID = EntityID;
using TimerID = EntityID;
using SessionID = EntityID;
using ConfigID = EntityID;

// 时间戳类型
using Timestamp = uint32_t;

// 特殊ID值
constexpr EntityID INVALID_ID = 0;
constexpr EntityID RESERVED_ID_START = 1;
constexpr EntityID RESERVED_ID_END = 100;
constexpr EntityID USER_ID_START = 101;

// ==================== 枚举类型定义 ====================

// 红外协议类型
enum class IRProtocol : uint8_t {
    UNKNOWN = 0, NEC = 1, SONY = 2, LG = 3, SAMSUNG = 4,
    PANASONIC = 5, JVC = 6, RC5 = 7, RC6 = 8, RAW = 255
};

// 设备类型
enum class DeviceType : uint8_t {
    UNKNOWN = 0, TV = 1, AC = 2, FAN = 3, LIGHT = 4,
    AUDIO = 5, PROJECTOR = 6, OTHER = 255
};

// 任务状态
enum class TaskStatus : uint8_t {
    PENDING = 0, RUNNING = 1, COMPLETED = 2,
    FAILED = 3, CANCELLED = 4, PAUSED = 5
};

// 任务类型
enum class TaskType : uint8_t {
    SINGLE_SIGNAL = 0, BATCH_SIGNALS = 1, SCHEDULED = 2,
    REPEATED = 3, CONDITIONAL = 4
};

// 优先级
enum class Priority : uint8_t {
    LOW = 0, NORMAL = 1, HIGH = 2, CRITICAL = 3
};

// 错误级别
enum class ErrorLevel : uint8_t {
    INFO = 0, WARNING = 1, ERROR = 2, CRITICAL = 3
};

// 网络模式
enum class NetworkMode : uint8_t {
    WIFI_CLIENT = 0, WIFI_AP = 1, ETHERNET = 2
};

// 配置类型
enum class ConfigType : uint8_t {
    SYSTEM = 0, NETWORK = 1, INFRARED = 2, 
    OTA = 3, STORAGE = 4, SECURITY = 5
};

// 系统状态
enum class SystemState : uint8_t {
    INITIALIZING = 0, RUNNING = 1, ERROR = 2,
    MAINTENANCE = 3, SHUTDOWN = 4
};

// 操作结果
enum class OperationResult : uint8_t {
    SUCCESS = 0, FAILED = 1, TIMEOUT = 2,
    INVALID_PARAM = 3, RESOURCE_BUSY = 4
};

// ==================== 核心数据结构 ====================

// 信号数据结构
struct SignalData {
    SignalID id;                    // 信号ID
    String name;                    // 信号名称
    String description;             // 信号描述
    IRProtocol protocol;            // 红外协议
    DeviceType deviceType;          // 设备类型
    
    // 信号数据
    uint64_t code;                  // 信号代码
    uint8_t bits;                   // 数据位数
    uint16_t frequency;             // 载波频率
    std::vector<uint16_t> rawData;  // 原始数据
    
    // 元数据
    Timestamp createdTime;          // 创建时间
    Timestamp modifiedTime;         // 修改时间
    uint32_t usageCount;            // 使用次数
    Timestamp lastUsed;             // 最后使用时间
    
    // 构造函数
    SignalData() : id(INVALID_ID), protocol(IRProtocol::UNKNOWN), deviceType(DeviceType::UNKNOWN),
                   code(0), bits(0), frequency(38000), createdTime(0), modifiedTime(0),
                   usageCount(0), lastUsed(0) {}
    
    // 方法
    bool isValid() const {
        return id != INVALID_ID && !name.isEmpty() && 
               (protocol != IRProtocol::UNKNOWN || !rawData.empty());
    }
    
    void updateUsage() {
        usageCount++;
        lastUsed = millis();
        modifiedTime = lastUsed;
    }

    // JSON转换方法（符合标准文档第3.3节第208-209行要求）
    JsonObject toJson(JsonDocument& doc) const;
    static Result<SignalData> fromJson(const JsonObject& json);
};

// 任务数据结构
struct TaskData {
    TaskID id;                      // 任务ID
    String name;                    // 任务名称
    String description;             // 任务描述
    TaskType type;                  // 任务类型
    TaskStatus status;              // 任务状态
    Priority priority;              // 优先级
    
    // 任务配置
    std::vector<SignalID> signals;  // 关联的信号列表
    uint32_t interval;              // 重复间隔（毫秒）
    uint32_t signalDelay;           // 信号间延迟（毫秒）
    Timestamp nextExecution;        // 下次执行时间
    uint32_t maxExecutions;         // 最大执行次数
    uint32_t executionCount;        // 已执行次数
    
    // 元数据
    Timestamp createdTime;          // 创建时间
    Timestamp modifiedTime;         // 修改时间
    Timestamp lastExecuted;         // 最后执行时间
    
    // 构造函数
    TaskData() : id(INVALID_ID), type(TaskType::SINGLE_SIGNAL), status(TaskStatus::PENDING),
                 priority(Priority::NORMAL), interval(0), signalDelay(0), nextExecution(0),
                 maxExecutions(0), executionCount(0), createdTime(0), modifiedTime(0),
                 lastExecuted(0) {}
    
    // 方法
    bool isValid() const {
        return id != INVALID_ID && !name.isEmpty() && !signals.empty();
    }
    
    bool canExecute() const {
        return status == TaskStatus::PENDING &&
               (maxExecutions == 0 || executionCount < maxExecutions);
    }

    // JSON转换方法（符合标准文档第3.3节要求）
    JsonObject toJson(JsonDocument& doc) const;
    static Result<TaskData> fromJson(const JsonObject& json);
};

// 定时器数据结构
struct TimerData {
    TimerID id;                     // 定时器ID
    String name;                    // 定时器名称
    String description;             // 定时器描述
    TaskID taskId;                  // 关联的任务ID
    
    // 时间配置
    uint8_t hour;                   // 小时 (0-23)
    uint8_t minute;                 // 分钟 (0-59)
    uint8_t second;                 // 秒 (0-59)
    uint8_t weekdays;               // 星期掩码 (bit0=周日, bit1=周一, ...)
    bool isRepeating;               // 是否重复
    bool enabled;                   // 是否启用
    
    // 执行状态
    Timestamp nextTriggerTime;      // 下次触发时间
    Timestamp lastTriggerTime;      // 上次触发时间
    uint32_t triggerCount;          // 触发次数
    
    // 元数据
    Timestamp createdTime;          // 创建时间
    Timestamp modifiedTime;         // 修改时间
    
    // 构造函数
    TimerData() : id(INVALID_ID), taskId(INVALID_ID), hour(0), minute(0), second(0),
                  weekdays(0), isRepeating(false), enabled(false), nextTriggerTime(0),
                  lastTriggerTime(0), triggerCount(0), createdTime(0), modifiedTime(0) {}
    
    // 方法
    bool isValid() const {
        return id != INVALID_ID && taskId != INVALID_ID && !name.isEmpty() &&
               hour <= 23 && minute <= 59 && second <= 59;
    }
    
    bool shouldTrigger(Timestamp currentTime) const {
        return enabled && nextTriggerTime > 0 && currentTime >= nextTriggerTime;
    }

    // JSON转换方法（符合标准文档第3.3节要求）
    JsonObject toJson(JsonDocument& doc) const;
    static Result<TimerData> fromJson(const JsonObject& json);
};

// 配置数据结构
struct ConfigData {
    ConfigID id;                    // 配置ID
    ConfigType type;                // 配置类型
    String key;                     // 配置键
    String value;                   // 配置值
    String description;             // 配置描述
    
    // 元数据
    Timestamp createdTime;          // 创建时间
    Timestamp modifiedTime;         // 修改时间
    
    // 构造函数
    ConfigData() : id(INVALID_ID), type(ConfigType::SYSTEM), createdTime(0), modifiedTime(0) {}
    
    // 方法
    bool isValid() const {
        return id != INVALID_ID && !key.isEmpty();
    }

    // JSON转换方法（符合标准文档第3.3节要求）
    JsonObject toJson(JsonDocument& doc) const;
    static Result<ConfigData> fromJson(const JsonObject& json);
};

// 错误代码枚举
enum class ErrorCode : uint16_t {
    SUCCESS = 0,
    
    // 通用错误 (1-99)
    UNKNOWN_ERROR = 1,
    INVALID_PARAMETER = 2,
    NULL_POINTER = 3,
    OUT_OF_MEMORY = 4,
    TIMEOUT = 5,
    OPERATION_FAILED = 6,
    RESOURCE_BUSY = 7,
    RESOURCE_NOT_FOUND = 8,
    PERMISSION_DENIED = 9,
    INVALID_STATE = 10,
    
    // 系统错误 (100-199)
    SYSTEM_INITIALIZATION_FAILED = 100,
    SYSTEM_NOT_READY = 101,
    HARDWARE_ERROR = 102,
    STORAGE_ERROR = 103,
    NETWORK_ERROR = 104,
    
    // 数据错误 (200-299)
    DATA_CORRUPTION = 200,
    DATA_NOT_FOUND = 201,
    DATA_ALREADY_EXISTS = 202,
    DATA_VALIDATION_FAILED = 203,
    
    // 业务逻辑错误 (300-399)
    SIGNAL_NOT_FOUND = 300,
    TASK_NOT_FOUND = 301,
    TIMER_NOT_FOUND = 302,
    INVALID_SIGNAL_DATA = 303,
    INVALID_TASK_DATA = 304,
    INVALID_TIMER_DATA = 305,
    
    // 网络错误 (400-499)
    CONNECTION_FAILED = 400,
    AUTHENTICATION_FAILED = 401,
    AUTHORIZATION_FAILED = 402,
    REQUEST_TIMEOUT = 403,
    
    // 硬件错误 (500-599)
    IR_CONTROLLER_ERROR = 500,
    IR_SEND_FAILED = 501,
    IR_RECEIVE_FAILED = 502
};

// 结果模板类
template<typename T>
class Result {
private:
    bool m_success;
    T m_value;
    ErrorCode m_errorCode;
    String m_errorMessage;

public:
    // 成功结果构造函数
    Result(const T& value) : m_success(true), m_value(value), m_errorCode(ErrorCode::SUCCESS) {}
    
    // 错误结果构造函数
    Result(ErrorCode errorCode, const String& errorMessage) 
        : m_success(false), m_errorCode(errorCode), m_errorMessage(errorMessage) {}
    
    // 静态工厂方法
    static Result<T> Success(const T& value) {
        return Result<T>(value);
    }
    
    static Result<T> Error(ErrorCode errorCode, const String& errorMessage) {
        return Result<T>(errorCode, errorMessage);
    }
    
    // 访问方法
    bool isSuccess() const { return m_success; }
    bool isError() const { return !m_success; }
    
    const T& getValue() const { return m_value; }
    ErrorCode getErrorCode() const { return m_errorCode; }
    const String& getErrorMessage() const { return m_errorMessage; }
};
