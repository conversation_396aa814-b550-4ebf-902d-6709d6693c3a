#include "IDGenerator.h"
#include "JSONConverter.h"
#include <LittleFS.h>

// 全局ID生成器实例
static IDGenerator* g_idGenerator = nullptr;

IDGenerator::IDGenerator() 
    : m_currentSignalId(USER_ID_START), m_currentTaskId(USER_ID_START),
      m_currentTimerId(USER_ID_START), m_currentConfigId(USER_ID_START),
      m_currentSessionId(USER_ID_START), m_initialized(false) {
}

IDGenerator::~IDGenerator() {
    cleanup();
}

bool IDGenerator::initialize(const GeneratorConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    m_config = config;
    
    // 初始化默认ID值
    initializeDefaultIds();
    
    // 如果启用持久化，尝试加载状态
    if (m_config.enablePersistence) {
        loadState(); // 忽略加载失败，使用默认值
    }
    
    m_initialized = true;
    
    Serial.println("✅ IDGenerator: Initialized successfully");
    return true;
}

void IDGenerator::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        // 如果启用持久化，保存当前状态
        if (m_config.enablePersistence) {
            saveState();
        }
        
        m_initialized = false;
        
        Serial.println("✅ IDGenerator: Cleanup completed");
    }
}

// ==================== ID生成方法实现 ====================

SignalID IDGenerator::generateSignalId() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return INVALID_ID;
    }
    
    return generateNextId(m_currentSignalId);
}

TaskID IDGenerator::generateTaskId() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return INVALID_ID;
    }
    
    return generateNextId(m_currentTaskId);
}

TimerID IDGenerator::generateTimerId() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return INVALID_ID;
    }
    
    return generateNextId(m_currentTimerId);
}

ConfigID IDGenerator::generateConfigId() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return INVALID_ID;
    }
    
    return generateNextId(m_currentConfigId);
}

SessionID IDGenerator::generateSessionId() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return INVALID_ID;
    }
    
    return generateNextId(m_currentSessionId);
}

// ==================== ID验证方法实现 ====================

bool IDGenerator::isValidId(EntityID id) const {
    return id != INVALID_ID && validateIdRange(id);
}

bool IDGenerator::isUserRangeId(EntityID id) const {
    return id >= USER_ID_START && id <= m_config.maxId;
}

bool IDGenerator::isReservedId(EntityID id) const {
    return id >= RESERVED_ID_START && id <= RESERVED_ID_END;
}

// ==================== ID状态管理实现 ====================

SignalID IDGenerator::getCurrentSignalId() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_currentSignalId;
}

TaskID IDGenerator::getCurrentTaskId() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_currentTaskId;
}

TimerID IDGenerator::getCurrentTimerId() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_currentTimerId;
}

ConfigID IDGenerator::getCurrentConfigId() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_currentConfigId;
}

SessionID IDGenerator::getCurrentSessionId() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_currentSessionId;
}

bool IDGenerator::setCurrentSignalId(SignalID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!validateIdRange(id)) {
        return false;
    }
    
    m_currentSignalId = id;
    return true;
}

bool IDGenerator::setCurrentTaskId(TaskID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!validateIdRange(id)) {
        return false;
    }
    
    m_currentTaskId = id;
    return true;
}

bool IDGenerator::setCurrentTimerId(TimerID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!validateIdRange(id)) {
        return false;
    }
    
    m_currentTimerId = id;
    return true;
}

bool IDGenerator::setCurrentConfigId(ConfigID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!validateIdRange(id)) {
        return false;
    }
    
    m_currentConfigId = id;
    return true;
}

bool IDGenerator::setCurrentSessionId(SessionID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!validateIdRange(id)) {
        return false;
    }
    
    m_currentSessionId = id;
    return true;
}

// ==================== 持久化管理实现 ====================

bool IDGenerator::saveState() {
    if (!m_config.enablePersistence) {
        return false;
    }
    
    DynamicJsonDocument doc(1024);
    JsonObject stateObj = createStateJson(doc);
    
    String jsonString;
    serializeJson(stateObj, jsonString);
    
    File file = LittleFS.open(m_config.persistenceFile, "w");
    if (!file) {
        Serial.println("❌ IDGenerator: Failed to open state file for writing");
        return false;
    }
    
    size_t bytesWritten = file.print(jsonString);
    file.close();
    
    if (bytesWritten == 0) {
        Serial.println("❌ IDGenerator: Failed to write state to file");
        return false;
    }
    
    Serial.println("✅ IDGenerator: State saved successfully");
    return true;
}

bool IDGenerator::loadState() {
    if (!m_config.enablePersistence) {
        return false;
    }
    
    if (!LittleFS.exists(m_config.persistenceFile)) {
        Serial.println("⚠️ IDGenerator: State file does not exist, using defaults");
        return false;
    }
    
    File file = LittleFS.open(m_config.persistenceFile, "r");
    if (!file) {
        Serial.println("❌ IDGenerator: Failed to open state file for reading");
        return false;
    }
    
    String jsonString = file.readString();
    file.close();
    
    if (jsonString.isEmpty()) {
        Serial.println("❌ IDGenerator: State file is empty");
        return false;
    }
    
    DynamicJsonDocument doc(1024);
    DeserializationError error = deserializeJson(doc, jsonString);
    
    if (error) {
        Serial.printf("❌ IDGenerator: Failed to parse state JSON: %s\n", error.c_str());
        return false;
    }
    
    bool success = loadStateFromJson(doc.as<JsonObject>());
    if (success) {
        Serial.println("✅ IDGenerator: State loaded successfully");
    } else {
        Serial.println("❌ IDGenerator: Failed to load state from JSON");
    }
    
    return success;
}

bool IDGenerator::resetAllCounters() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    initializeDefaultIds();
    
    // 如果启用持久化，保存重置后的状态
    if (m_config.enablePersistence) {
        saveState();
    }
    
    Serial.println("✅ IDGenerator: All counters reset");
    return true;
}

// ==================== 统计信息实现 ====================

JsonObject IDGenerator::getStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("id_generator_statistics");

    stats["initialized"] = m_initialized;
    stats["current_signal_id"] = m_currentSignalId;
    stats["current_task_id"] = m_currentTaskId;
    stats["current_timer_id"] = m_currentTimerId;
    stats["current_config_id"] = m_currentConfigId;
    stats["current_session_id"] = m_currentSessionId;

    // 配置信息
    JsonObject config = stats.createNestedObject("config");
    config["start_id"] = m_config.startId;
    config["max_id"] = m_config.maxId;
    config["enable_persistence"] = m_config.enablePersistence;
    config["persistence_file"] = m_config.persistenceFile;

    // ID范围信息
    JsonObject ranges = stats.createNestedObject("id_ranges");
    ranges["invalid_id"] = INVALID_ID;
    ranges["reserved_start"] = RESERVED_ID_START;
    ranges["reserved_end"] = RESERVED_ID_END;
    ranges["user_start"] = USER_ID_START;

    return stats;
}

// ==================== 内部实现方法 ====================

EntityID IDGenerator::generateNextId(EntityID& currentId) {
    EntityID newId = currentId;

    // 递增ID
    currentId++;

    // 检查是否超出范围
    if (currentId > m_config.maxId) {
        // 回绕到起始ID
        currentId = m_config.startId;
        Serial.println("⚠️ IDGenerator: ID counter wrapped around");
    }

    return newId;
}

bool IDGenerator::validateIdRange(EntityID id) const {
    return (id >= m_config.startId && id <= m_config.maxId) || isReservedId(id);
}

void IDGenerator::initializeDefaultIds() {
    m_currentSignalId = m_config.startId;
    m_currentTaskId = m_config.startId;
    m_currentTimerId = m_config.startId;
    m_currentConfigId = m_config.startId;
    m_currentSessionId = m_config.startId;
}

JsonObject IDGenerator::createStateJson(JsonDocument& doc) const {
    JsonObject state = doc.createNestedObject("id_generator_state");

    state["version"] = 1;
    state["timestamp"] = millis();
    state["current_signal_id"] = m_currentSignalId;
    state["current_task_id"] = m_currentTaskId;
    state["current_timer_id"] = m_currentTimerId;
    state["current_config_id"] = m_currentConfigId;
    state["current_session_id"] = m_currentSessionId;

    return state;
}

bool IDGenerator::loadStateFromJson(const JsonObject& json) {
    if (!json.containsKey("id_generator_state")) {
        return false;
    }

    JsonObject state = json["id_generator_state"];

    // 验证版本
    uint32_t version = JSONConverter::getUIntValue(state, "version", 0);
    if (version != 1) {
        Serial.printf("⚠️ IDGenerator: Unsupported state version: %d\n", version);
        return false;
    }

    // 加载ID状态
    EntityID signalId = JSONConverter::getUIntValue(state, "current_signal_id", m_config.startId);
    EntityID taskId = JSONConverter::getUIntValue(state, "current_task_id", m_config.startId);
    EntityID timerId = JSONConverter::getUIntValue(state, "current_timer_id", m_config.startId);
    EntityID configId = JSONConverter::getUIntValue(state, "current_config_id", m_config.startId);
    EntityID sessionId = JSONConverter::getUIntValue(state, "current_session_id", m_config.startId);

    // 验证ID范围
    if (!validateIdRange(signalId) || !validateIdRange(taskId) ||
        !validateIdRange(timerId) || !validateIdRange(configId) ||
        !validateIdRange(sessionId)) {
        Serial.println("❌ IDGenerator: Invalid ID range in state file");
        return false;
    }

    // 应用加载的状态
    m_currentSignalId = signalId;
    m_currentTaskId = taskId;
    m_currentTimerId = timerId;
    m_currentConfigId = configId;
    m_currentSessionId = sessionId;

    return true;
}

// ==================== 全局实例管理 ====================

IDGenerator* getGlobalIDGenerator() {
    if (!g_idGenerator) {
        g_idGenerator = new IDGenerator();
        g_idGenerator->initialize();
    }
    return g_idGenerator;
}
