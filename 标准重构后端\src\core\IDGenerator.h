#pragma once

#include "DataStructures.h"
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - ID生成器
 * 
 * 功能：
 * 1. 统一的ID生成管理
 * 2. 线程安全的ID分配
 * 3. ID范围管理和验证
 * 4. 持久化ID状态管理
 * 
 * 设计原则：
 * - 唯一性：确保生成的ID在系统中唯一
 * - 线程安全：支持多线程并发访问
 * - 持久化：ID状态可以持久化存储
 * - 性能优化：高效的ID生成算法
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 * 核心数据结构定义中的ID类型要求
 */

class IDGenerator {
public:
    // ID生成器配置
    struct GeneratorConfig {
        EntityID startId;              // 起始ID
        EntityID maxId;                // 最大ID
        bool enablePersistence;        // 是否启用持久化
        String persistenceFile;        // 持久化文件路径
        
        GeneratorConfig() : startId(USER_ID_START), maxId(UINT32_MAX - 1),
                           enablePersistence(true), persistenceFile("/id_state.json") {}
    };

private:
    // 当前ID状态
    EntityID m_currentSignalId;
    EntityID m_currentTaskId;
    EntityID m_currentTimerId;
    EntityID m_currentConfigId;
    EntityID m_currentSessionId;
    
    // 配置
    GeneratorConfig m_config;
    
    // 线程安全
    mutable std::mutex m_mutex;
    
    // 初始化状态
    bool m_initialized;

public:
    /**
     * 构造函数
     */
    IDGenerator();
    
    /**
     * 析构函数
     */
    ~IDGenerator();
    
    /**
     * 初始化ID生成器
     * @param config 配置参数
     * @return 是否成功
     */
    bool initialize(const GeneratorConfig& config = GeneratorConfig());
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    // ==================== ID生成方法 ====================
    
    /**
     * 生成新的信号ID
     * @return 信号ID
     */
    SignalID generateSignalId();
    
    /**
     * 生成新的任务ID
     * @return 任务ID
     */
    TaskID generateTaskId();
    
    /**
     * 生成新的定时器ID
     * @return 定时器ID
     */
    TimerID generateTimerId();
    
    /**
     * 生成新的配置ID
     * @return 配置ID
     */
    ConfigID generateConfigId();
    
    /**
     * 生成新的会话ID
     * @return 会话ID
     */
    SessionID generateSessionId();
    
    // ==================== ID验证方法 ====================
    
    /**
     * 验证ID是否有效
     * @param id 要验证的ID
     * @return 是否有效
     */
    bool isValidId(EntityID id) const;
    
    /**
     * 验证ID是否在用户范围内
     * @param id 要验证的ID
     * @return 是否在用户范围内
     */
    bool isUserRangeId(EntityID id) const;
    
    /**
     * 验证ID是否在保留范围内
     * @param id 要验证的ID
     * @return 是否在保留范围内
     */
    bool isReservedId(EntityID id) const;
    
    // ==================== ID状态管理 ====================
    
    /**
     * 获取当前信号ID计数器
     * @return 当前信号ID
     */
    SignalID getCurrentSignalId() const;
    
    /**
     * 获取当前任务ID计数器
     * @return 当前任务ID
     */
    TaskID getCurrentTaskId() const;
    
    /**
     * 获取当前定时器ID计数器
     * @return 当前定时器ID
     */
    TimerID getCurrentTimerId() const;
    
    /**
     * 获取当前配置ID计数器
     * @return 当前配置ID
     */
    ConfigID getCurrentConfigId() const;
    
    /**
     * 获取当前会话ID计数器
     * @return 当前会话ID
     */
    SessionID getCurrentSessionId() const;
    
    /**
     * 设置信号ID计数器
     * @param id 新的计数器值
     * @return 是否成功
     */
    bool setCurrentSignalId(SignalID id);
    
    /**
     * 设置任务ID计数器
     * @param id 新的计数器值
     * @return 是否成功
     */
    bool setCurrentTaskId(TaskID id);
    
    /**
     * 设置定时器ID计数器
     * @param id 新的计数器值
     * @return 是否成功
     */
    bool setCurrentTimerId(TimerID id);
    
    /**
     * 设置配置ID计数器
     * @param id 新的计数器值
     * @return 是否成功
     */
    bool setCurrentConfigId(ConfigID id);
    
    /**
     * 设置会话ID计数器
     * @param id 新的计数器值
     * @return 是否成功
     */
    bool setCurrentSessionId(SessionID id);
    
    // ==================== 持久化管理 ====================
    
    /**
     * 保存ID状态到文件
     * @return 是否成功
     */
    bool saveState();
    
    /**
     * 从文件加载ID状态
     * @return 是否成功
     */
    bool loadState();
    
    /**
     * 重置所有ID计数器
     * @return 是否成功
     */
    bool resetAllCounters();
    
    // ==================== 统计信息 ====================
    
    /**
     * 获取ID生成统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getStatistics(JsonDocument& doc) const;
    
    /**
     * 获取配置信息
     * @return 配置对象
     */
    const GeneratorConfig& getConfig() const { return m_config; }

private:
    // ==================== 内部实现方法 ====================
    
    /**
     * 生成下一个ID
     * @param currentId 当前ID引用
     * @return 新生成的ID
     */
    EntityID generateNextId(EntityID& currentId);
    
    /**
     * 验证ID范围
     * @param id 要验证的ID
     * @return 是否在有效范围内
     */
    bool validateIdRange(EntityID id) const;
    
    /**
     * 初始化默认ID值
     */
    void initializeDefaultIds();
    
    /**
     * 创建状态JSON对象
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject createStateJson(JsonDocument& doc) const;
    
    /**
     * 从JSON对象加载状态
     * @param json JSON对象
     * @return 是否成功
     */
    bool loadStateFromJson(const JsonObject& json);
};

// ==================== 全局ID生成器实例 ====================

/**
 * 获取全局ID生成器实例
 * @return ID生成器指针
 */
IDGenerator* getGlobalIDGenerator();

// ==================== 便捷宏定义 ====================

#define GENERATE_SIGNAL_ID() (getGlobalIDGenerator()->generateSignalId())
#define GENERATE_TASK_ID() (getGlobalIDGenerator()->generateTaskId())
#define GENERATE_TIMER_ID() (getGlobalIDGenerator()->generateTimerId())
#define GENERATE_CONFIG_ID() (getGlobalIDGenerator()->generateConfigId())
#define GENERATE_SESSION_ID() (getGlobalIDGenerator()->generateSessionId())
