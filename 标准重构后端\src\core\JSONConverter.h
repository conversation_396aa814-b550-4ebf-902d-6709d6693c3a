#pragma once

#include "DataStructures.h"
#include <ArduinoJson.h>

/**
 * ESP32-S3 红外控制系统 - JSON转换器
 * 
 * 功能：
 * 1. 数据结构与JSON的双向转换
 * 2. 统一的序列化/反序列化接口
 * 3. 错误处理和验证
 * 4. 性能优化的转换方法
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 * 第八部分：重构优先级 (第707行) - JSON序列化/反序列化
 */

class JSONConverter {
public:
    // ==================== 信号数据转换 ====================
    
    /**
     * 信号数据转换为JSON
     * @param signal 信号数据
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject signalToJson(const SignalData& signal, JsonDocument& doc);
    
    /**
     * JSON转换为信号数据
     * @param json JSON对象
     * @return 转换结果
     */
    static Result<SignalData> signalFromJson(const JsonObject& json);
    
    /**
     * 信号数组转换为JSON数组
     * @param signals 信号数组
     * @param doc JSON文档
     * @return JSON数组
     */
    static JsonArray signalsToJsonArray(const std::vector<SignalData>& signals, JsonDocument& doc);
    
    // ==================== 任务数据转换 ====================
    
    /**
     * 任务数据转换为JSON
     * @param task 任务数据
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject taskToJson(const TaskData& task, JsonDocument& doc);
    
    /**
     * JSON转换为任务数据
     * @param json JSON对象
     * @return 转换结果
     */
    static Result<TaskData> taskFromJson(const JsonObject& json);
    
    /**
     * 任务数组转换为JSON数组
     * @param tasks 任务数组
     * @param doc JSON文档
     * @return JSON数组
     */
    static JsonArray tasksToJsonArray(const std::vector<TaskData>& tasks, JsonDocument& doc);
    
    // ==================== 定时器数据转换 ====================
    
    /**
     * 定时器数据转换为JSON
     * @param timer 定时器数据
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject timerToJson(const TimerData& timer, JsonDocument& doc);
    
    /**
     * JSON转换为定时器数据
     * @param json JSON对象
     * @return 转换结果
     */
    static Result<TimerData> timerFromJson(const JsonObject& json);
    
    /**
     * 定时器数组转换为JSON数组
     * @param timers 定时器数组
     * @param doc JSON文档
     * @return JSON数组
     */
    static JsonArray timersToJsonArray(const std::vector<TimerData>& timers, JsonDocument& doc);
    
    // ==================== 配置数据转换 ====================
    
    /**
     * 配置数据转换为JSON
     * @param config 配置数据
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject configToJson(const ConfigData& config, JsonDocument& doc);
    
    /**
     * JSON转换为配置数据
     * @param json JSON对象
     * @return 转换结果
     */
    static Result<ConfigData> configFromJson(const JsonObject& json);
    
    // ==================== 枚举转换 ====================
    
    /**
     * 红外协议枚举转字符串
     * @param protocol 协议枚举
     * @return 字符串表示
     */
    static String irProtocolToString(IRProtocol protocol);
    
    /**
     * 字符串转红外协议枚举
     * @param str 字符串
     * @return 协议枚举
     */
    static IRProtocol stringToIRProtocol(const String& str);
    
    /**
     * 设备类型枚举转字符串
     * @param deviceType 设备类型枚举
     * @return 字符串表示
     */
    static String deviceTypeToString(DeviceType deviceType);
    
    /**
     * 字符串转设备类型枚举
     * @param str 字符串
     * @return 设备类型枚举
     */
    static DeviceType stringToDeviceType(const String& str);
    
    /**
     * 任务状态枚举转字符串
     * @param status 任务状态枚举
     * @return 字符串表示
     */
    static String taskStatusToString(TaskStatus status);
    
    /**
     * 字符串转任务状态枚举
     * @param str 字符串
     * @return 任务状态枚举
     */
    static TaskStatus stringToTaskStatus(const String& str);
    
    /**
     * 任务类型枚举转字符串
     * @param type 任务类型枚举
     * @return 字符串表示
     */
    static String taskTypeToString(TaskType type);
    
    /**
     * 字符串转任务类型枚举
     * @param str 字符串
     * @return 任务类型枚举
     */
    static TaskType stringToTaskType(const String& str);
    
    /**
     * 优先级枚举转字符串
     * @param priority 优先级枚举
     * @return 字符串表示
     */
    static String priorityToString(Priority priority);
    
    /**
     * 字符串转优先级枚举
     * @param str 字符串
     * @return 优先级枚举
     */
    static Priority stringToPriority(const String& str);
    
    /**
     * 配置类型枚举转字符串
     * @param type 配置类型枚举
     * @return 字符串表示
     */
    static String configTypeToString(ConfigType type);
    
    /**
     * 字符串转配置类型枚举
     * @param str 字符串
     * @return 配置类型枚举
     */
    static ConfigType stringToConfigType(const String& str);
    
    // ==================== 工具方法 ====================
    
    /**
     * 验证JSON对象是否包含必需字段
     * @param json JSON对象
     * @param requiredFields 必需字段列表
     * @return 是否包含所有必需字段
     */
    static bool validateRequiredFields(const JsonObject& json, const std::vector<String>& requiredFields);
    
    /**
     * 安全获取JSON字符串值
     * @param json JSON对象
     * @param key 键名
     * @param defaultValue 默认值
     * @return 字符串值
     */
    static String getStringValue(const JsonObject& json, const char* key, const String& defaultValue = "");
    
    /**
     * 安全获取JSON整数值
     * @param json JSON对象
     * @param key 键名
     * @param defaultValue 默认值
     * @return 整数值
     */
    static uint32_t getUIntValue(const JsonObject& json, const char* key, uint32_t defaultValue = 0);
    
    /**
     * 安全获取JSON布尔值
     * @param json JSON对象
     * @param key 键名
     * @param defaultValue 默认值
     * @return 布尔值
     */
    static bool getBoolValue(const JsonObject& json, const char* key, bool defaultValue = false);
    
    /**
     * 安全获取JSON数组
     * @param json JSON对象
     * @param key 键名
     * @return JSON数组（可能为空）
     */
    static JsonArray getArrayValue(const JsonObject& json, const char* key);
    
    /**
     * 创建错误响应JSON
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject createErrorResponse(ErrorCode errorCode, const String& message, JsonDocument& doc);
    
    /**
     * 创建成功响应JSON
     * @param message 成功消息
     * @param data 数据对象（可选）
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject createSuccessResponse(const String& message, JsonObject* data, JsonDocument& doc);

private:
    // ==================== 内部辅助方法 ====================
    
    /**
     * 验证信号数据的JSON格式
     * @param json JSON对象
     * @return 是否有效
     */
    static bool validateSignalJson(const JsonObject& json);
    
    /**
     * 验证任务数据的JSON格式
     * @param json JSON对象
     * @return 是否有效
     */
    static bool validateTaskJson(const JsonObject& json);
    
    /**
     * 验证定时器数据的JSON格式
     * @param json JSON对象
     * @return 是否有效
     */
    static bool validateTimerJson(const JsonObject& json);
    
    /**
     * 验证配置数据的JSON格式
     * @param json JSON对象
     * @return 是否有效
     */
    static bool validateConfigJson(const JsonObject& json);
};
