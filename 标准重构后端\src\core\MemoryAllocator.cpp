#include "MemoryAllocator.h"
#include <esp_heap_caps.h>
#include <esp_system.h>
#include <cstring>
#include <algorithm>

/**
 * ESP32-S3 红外控制系统 - 内存分配器实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第六部分：内存管理最佳实践
 */

// 静态成员初始化
MemoryAllocator* MemoryAllocator::s_instance = nullptr;
std::mutex MemoryAllocator::s_mutex;

MemoryAllocator::MemoryAllocator() 
    : m_trackingEnabled(true), m_leakDetectionEnabled(true), m_maxAllowedUsage(SIZE_MAX) {
    // 初始化统计信息
    memset(&m_stats, 0, sizeof(m_stats));
}

MemoryAllocator::~MemoryAllocator() {
    if (m_leakDetectionEnabled) {
        reportLeaks();
    }
}

// ==================== 单例模式实现 ====================

MemoryAllocator& MemoryAllocator::getInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (!s_instance) {
        s_instance = new MemoryAllocator();
    }
    return *s_instance;
}

void MemoryAllocator::destroyInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

// ==================== 内存分配接口实现 ====================

void* MemoryAllocator::allocate(size_t size, MemoryType type, uint32_t flags) {
    return allocateInternal(size, type, flags, nullptr, 0, nullptr);
}

void* MemoryAllocator::allocateAligned(size_t size, size_t alignment, MemoryType type) {
    size_t alignedSize = alignSize(size, alignment);
    void* ptr = allocate(alignedSize, type, 0);
    
    if (ptr && !isAligned(ptr, alignment)) {
        // 如果分配的内存不满足对齐要求，重新分配
        deallocate(ptr);
        
        // 分配更大的内存以确保对齐
        size_t extraSize = alignedSize + alignment - 1;
        void* rawPtr = allocate(extraSize, type, 0);
        if (!rawPtr) {
            return nullptr;
        }
        
        // 计算对齐后的指针
        ptr = alignPointer(rawPtr, alignment);
        
        // 注意：这里简化处理，实际应该记录原始指针用于释放
    }
    
    return ptr;
}

void* MemoryAllocator::reallocate(void* ptr, size_t newSize) {
    if (!ptr) {
        return allocate(newSize);
    }
    
    if (newSize == 0) {
        deallocate(ptr);
        return nullptr;
    }
    
    // 查找原始块信息
    MemoryBlock* block = findBlock(ptr);
    if (!block) {
        return nullptr;
    }
    
    // 分配新内存
    void* newPtr = allocate(newSize, block->type, block->flags);
    if (!newPtr) {
        return nullptr;
    }
    
    // 复制数据
    size_t copySize = std::min(block->size, newSize);
    memcpy(newPtr, ptr, copySize);
    
    // 释放原内存
    deallocate(ptr);
    
    return newPtr;
}

void MemoryAllocator::deallocate(void* ptr) {
    deallocateInternal(ptr);
}

char* MemoryAllocator::allocateString(size_t length, MemoryType type) {
    char* str = static_cast<char*>(allocate(length + 1, type, static_cast<uint32_t>(AllocFlags::ZERO_INIT)));
    return str;
}

char* MemoryAllocator::duplicateString(const char* str, MemoryType type) {
    if (!str) {
        return nullptr;
    }
    
    size_t length = strlen(str);
    char* newStr = allocateString(length, type);
    if (newStr) {
        strcpy(newStr, str);
    }
    
    return newStr;
}

void MemoryAllocator::deallocateString(char* str) {
    deallocate(str);
}

// ==================== 内存池实现 ====================

MemoryAllocator::MemoryPool::MemoryPool(size_t blockSize, size_t blockCount, MemoryType type) 
    : m_blockSize(blockSize), m_blockCount(blockCount), m_freeBlocks(blockCount, true) {
    
    size_t totalSize = blockSize * blockCount;
    m_pool = MemoryAllocator::getInstance().allocate(totalSize, type, 0);
    
    if (!m_pool) {
        throw std::bad_alloc();
    }
}

MemoryAllocator::MemoryPool::~MemoryPool() {
    if (m_pool) {
        MemoryAllocator::getInstance().deallocate(m_pool);
    }
}

void* MemoryAllocator::MemoryPool::allocate() {
    std::lock_guard<std::mutex> lock(m_poolMutex);
    
    for (size_t i = 0; i < m_blockCount; ++i) {
        if (m_freeBlocks[i]) {
            m_freeBlocks[i] = false;
            return static_cast<char*>(m_pool) + (i * m_blockSize);
        }
    }
    
    return nullptr; // 池已满
}

void MemoryAllocator::MemoryPool::deallocate(void* ptr) {
    if (!isFromPool(ptr)) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_poolMutex);
    
    size_t offset = static_cast<char*>(ptr) - static_cast<char*>(m_pool);
    size_t blockIndex = offset / m_blockSize;
    
    if (blockIndex < m_blockCount) {
        m_freeBlocks[blockIndex] = true;
    }
}

bool MemoryAllocator::MemoryPool::isFromPool(void* ptr) const {
    if (!ptr || !m_pool) {
        return false;
    }
    
    char* charPtr = static_cast<char*>(ptr);
    char* poolStart = static_cast<char*>(m_pool);
    char* poolEnd = poolStart + (m_blockSize * m_blockCount);
    
    return charPtr >= poolStart && charPtr < poolEnd;
}

size_t MemoryAllocator::MemoryPool::getAvailableBlocks() const {
    std::lock_guard<std::mutex> lock(m_poolMutex);
    return std::count(m_freeBlocks.begin(), m_freeBlocks.end(), true);
}

size_t MemoryAllocator::MemoryPool::getTotalBlocks() const {
    return m_blockCount;
}

std::unique_ptr<MemoryAllocator::MemoryPool> MemoryAllocator::createPool(size_t blockSize, size_t blockCount, MemoryType type) {
    try {
        return std::make_unique<MemoryPool>(blockSize, blockCount, type);
    } catch (const std::bad_alloc&) {
        return nullptr;
    }
}

// ==================== 内存监控和统计实现 ====================

const MemoryAllocator::MemoryStats& MemoryAllocator::getStats() const {
    return m_stats;
}

void MemoryAllocator::resetStats() {
    std::lock_guard<std::mutex> lock(s_mutex);
    memset(&m_stats, 0, sizeof(m_stats));
}

size_t MemoryAllocator::getCurrentUsage() const {
    return m_stats.currentUsage;
}

size_t MemoryAllocator::getPeakUsage() const {
    return m_stats.peakUsage;
}

size_t MemoryAllocator::getAvailableMemory(MemoryType type) const {
    uint32_t caps = getHeapCaps(type);
    return heap_caps_get_free_size(caps);
}

size_t MemoryAllocator::getTotalMemory(MemoryType type) const {
    uint32_t caps = getHeapCaps(type);
    return heap_caps_get_total_size(caps);
}

float MemoryAllocator::getMemoryUsagePercent(MemoryType type) const {
    size_t total = getTotalMemory(type);
    if (total == 0) {
        return 0.0f;
    }
    
    size_t used = total - getAvailableMemory(type);
    return (float)used / total * 100.0f;
}

size_t MemoryAllocator::getLargestFreeBlock(MemoryType type) const {
    uint32_t caps = getHeapCaps(type);
    return heap_caps_get_largest_free_block(caps);
}

float MemoryAllocator::getFragmentationPercent(MemoryType type) const {
    size_t available = getAvailableMemory(type);
    size_t largest = getLargestFreeBlock(type);
    
    if (available == 0) {
        return 0.0f;
    }
    
    return (1.0f - (float)largest / available) * 100.0f;
}

// ==================== 内存泄漏检测实现 ====================

void MemoryAllocator::enableLeakDetection(bool enable) {
    m_leakDetectionEnabled = enable;
}

void MemoryAllocator::enableTracking(bool enable) {
    m_trackingEnabled = enable;
}

bool MemoryAllocator::isLeakDetectionEnabled() const {
    return m_leakDetectionEnabled;
}

bool MemoryAllocator::isTrackingEnabled() const {
    return m_trackingEnabled;
}

std::vector<MemoryAllocator::MemoryBlock> MemoryAllocator::detectLeaks() const {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    std::vector<MemoryBlock> leaks;
    for (const auto& pair : m_allocatedBlocks) {
        leaks.push_back(pair.second);
    }
    
    return leaks;
}

void MemoryAllocator::reportLeaks() const {
    auto leaks = detectLeaks();
    
    if (leaks.empty()) {
        Serial.println("[MemoryAllocator] No memory leaks detected.");
        return;
    }
    
    Serial.printf("[MemoryAllocator] Detected %u memory leaks:\n", leaks.size());
    
    for (const auto& leak : leaks) {
        Serial.printf("  Leak: %p, Size: %u bytes, Type: %d", 
                     leak.ptr, leak.size, static_cast<int>(leak.type));
        
        if (leak.file) {
            Serial.printf(", File: %s:%d", leak.file, leak.line);
        }
        
        if (leak.function) {
            Serial.printf(", Function: %s", leak.function);
        }
        
        Serial.println();
    }
}

void MemoryAllocator::dumpAllocatedBlocks() const {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    Serial.printf("[MemoryAllocator] Currently allocated blocks: %u\n", m_allocatedBlocks.size());
    
    for (const auto& pair : m_allocatedBlocks) {
        const MemoryBlock& block = pair.second;
        Serial.printf("  Block: %p, Size: %u bytes, Type: %d, Time: %u\n", 
                     block.ptr, block.size, static_cast<int>(block.type), block.timestamp);
    }
}

void MemoryAllocator::setMaxAllowedUsage(size_t maxUsage) {
    m_maxAllowedUsage = maxUsage;
}

size_t MemoryAllocator::getMaxAllowedUsage() const {
    return m_maxAllowedUsage;
}

// ==================== 内部实现方法 ====================

void* MemoryAllocator::allocateInternal(size_t size, MemoryType type, uint32_t flags, const char* file, int line, const char* function) {
    if (size == 0) {
        return nullptr;
    }

    // 检查内存使用限制
    if (m_stats.currentUsage + size > m_maxAllowedUsage) {
        handleAllocationFailure(size, type);
        return nullptr;
    }

    // 选择最优内存类型
    if (type == MemoryType::AUTO) {
        type = selectOptimalMemoryType(size);
    }

    // 获取堆能力标志
    uint32_t caps = getHeapCaps(type);

    // 分配内存
    void* ptr = heap_caps_malloc(size, caps);
    if (!ptr) {
        handleAllocationFailure(size, type);
        return nullptr;
    }

    // 零初始化
    if (flags & static_cast<uint32_t>(AllocFlags::ZERO_INIT)) {
        memset(ptr, 0, size);
    }

    // 注册内存块
    if (m_trackingEnabled) {
        registerBlock(ptr, size, type, flags, file, line, function);
    }

    // 更新统计信息
    updateStats(size, type, true);

    return ptr;
}

void MemoryAllocator::deallocateInternal(void* ptr) {
    if (!ptr) {
        return;
    }

    std::lock_guard<std::mutex> lock(s_mutex);

    // 查找内存块
    auto it = m_allocatedBlocks.find(ptr);
    if (it != m_allocatedBlocks.end()) {
        const MemoryBlock& block = it->second;

        // 更新统计信息
        updateStats(block.size, block.type, false);

        // 移除跟踪记录
        m_allocatedBlocks.erase(it);
    }

    // 释放内存
    heap_caps_free(ptr);
}

MemoryAllocator::MemoryType MemoryAllocator::selectOptimalMemoryType(size_t size) const {
    // 大于64KB的分配优先使用PSRAM
    if (size > 65536) {
        size_t psramAvailable = getAvailableMemory(MemoryType::PSRAM);
        if (psramAvailable >= size) {
            return MemoryType::PSRAM;
        }
    }

    // 小内存分配使用DRAM
    return MemoryType::DRAM;
}

uint32_t MemoryAllocator::getHeapCaps(MemoryType type) const {
    switch (type) {
        case MemoryType::DRAM:
            return MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT;
        case MemoryType::PSRAM:
            return MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT;
        case MemoryType::DMA_CAPABLE:
            return MALLOC_CAP_DMA | MALLOC_CAP_8BIT;
        case MemoryType::IRAM:
            return MALLOC_CAP_EXEC | MALLOC_CAP_8BIT;
        default:
            return MALLOC_CAP_8BIT;
    }
}

size_t MemoryAllocator::alignSize(size_t size, size_t alignment) const {
    return (size + alignment - 1) & ~(alignment - 1);
}

void* MemoryAllocator::alignPointer(void* ptr, size_t alignment) const {
    uintptr_t addr = reinterpret_cast<uintptr_t>(ptr);
    uintptr_t aligned = (addr + alignment - 1) & ~(alignment - 1);
    return reinterpret_cast<void*>(aligned);
}

void MemoryAllocator::updateStats(size_t size, MemoryType type, bool isAllocation) {
    std::lock_guard<std::mutex> lock(s_mutex);

    if (isAllocation) {
        m_stats.totalAllocated += size;
        m_stats.currentUsage += size;
        m_stats.allocCount++;

        if (type == MemoryType::DRAM) {
            m_stats.dramUsage += size;
        } else if (type == MemoryType::PSRAM) {
            m_stats.psramUsage += size;
        }

        updatePeakUsage();
    } else {
        m_stats.totalFreed += size;
        m_stats.currentUsage -= size;
        m_stats.freeCount++;

        if (type == MemoryType::DRAM) {
            m_stats.dramUsage -= size;
        } else if (type == MemoryType::PSRAM) {
            m_stats.psramUsage -= size;
        }
    }
}

void MemoryAllocator::updatePeakUsage() {
    if (m_stats.currentUsage > m_stats.peakUsage) {
        m_stats.peakUsage = m_stats.currentUsage;
    }
}

void MemoryAllocator::registerBlock(void* ptr, size_t size, MemoryType type, uint32_t flags, const char* file, int line, const char* function) {
    std::lock_guard<std::mutex> lock(s_mutex);

    MemoryBlock block;
    block.ptr = ptr;
    block.size = size;
    block.type = type;
    block.flags = flags;
    block.timestamp = millis();
    block.file = file;
    block.line = line;
    block.function = function;

    m_allocatedBlocks[ptr] = block;
}

void MemoryAllocator::unregisterBlock(void* ptr) {
    std::lock_guard<std::mutex> lock(s_mutex);
    m_allocatedBlocks.erase(ptr);
}

MemoryAllocator::MemoryBlock* MemoryAllocator::findBlock(void* ptr) {
    std::lock_guard<std::mutex> lock(s_mutex);

    auto it = m_allocatedBlocks.find(ptr);
    if (it != m_allocatedBlocks.end()) {
        return &it->second;
    }

    return nullptr;
}

bool MemoryAllocator::isValidPointer(void* ptr) const {
    if (!ptr) {
        return false;
    }

    // 检查指针是否在有效的堆范围内
    return heap_caps_check_integrity_all(true);
}

bool MemoryAllocator::isAligned(void* ptr, size_t alignment) const {
    uintptr_t addr = reinterpret_cast<uintptr_t>(ptr);
    return (addr & (alignment - 1)) == 0;
}

void MemoryAllocator::handleAllocationFailure(size_t size, MemoryType type) const {
    Serial.printf("[MemoryAllocator] Allocation failed: size=%u, type=%d\n", size, static_cast<int>(type));
    Serial.printf("  Available DRAM: %u bytes\n", getAvailableMemory(MemoryType::DRAM));
    Serial.printf("  Available PSRAM: %u bytes\n", getAvailableMemory(MemoryType::PSRAM));
    Serial.printf("  Current usage: %u bytes\n", m_stats.currentUsage);
}

void MemoryAllocator::handleMemoryCorruption(void* ptr) const {
    Serial.printf("[MemoryAllocator] Memory corruption detected at %p\n", ptr);
}

// ==================== 调试和诊断实现 ====================

bool MemoryAllocator::validateMemoryIntegrity() const {
    return heap_caps_check_integrity_all(true);
}

bool MemoryAllocator::checkForCorruption() const {
    return validateMemoryIntegrity();
}

void MemoryAllocator::printMemoryReport() const {
    Serial.println("=== Memory Allocator Report ===");
    Serial.printf("Total allocated: %u bytes\n", m_stats.totalAllocated);
    Serial.printf("Total freed: %u bytes\n", m_stats.totalFreed);
    Serial.printf("Current usage: %u bytes\n", m_stats.currentUsage);
    Serial.printf("Peak usage: %u bytes\n", m_stats.peakUsage);
    Serial.printf("Allocation count: %u\n", m_stats.allocCount);
    Serial.printf("Free count: %u\n", m_stats.freeCount);
    Serial.printf("DRAM usage: %u bytes\n", m_stats.dramUsage);
    Serial.printf("PSRAM usage: %u bytes\n", m_stats.psramUsage);
    Serial.printf("Active blocks: %u\n", m_allocatedBlocks.size());
    Serial.println("===============================");
}

void MemoryAllocator::printDetailedStats() const {
    printMemoryReport();

    Serial.println("=== System Memory Info ===");
    Serial.printf("DRAM total: %u bytes\n", getTotalMemory(MemoryType::DRAM));
    Serial.printf("DRAM available: %u bytes\n", getAvailableMemory(MemoryType::DRAM));
    Serial.printf("DRAM usage: %.1f%%\n", getMemoryUsagePercent(MemoryType::DRAM));
    Serial.printf("DRAM fragmentation: %.1f%%\n", getFragmentationPercent(MemoryType::DRAM));

    Serial.printf("PSRAM total: %u bytes\n", getTotalMemory(MemoryType::PSRAM));
    Serial.printf("PSRAM available: %u bytes\n", getAvailableMemory(MemoryType::PSRAM));
    Serial.printf("PSRAM usage: %.1f%%\n", getMemoryUsagePercent(MemoryType::PSRAM));
    Serial.printf("PSRAM fragmentation: %.1f%%\n", getFragmentationPercent(MemoryType::PSRAM));
    Serial.println("==========================");
}

void MemoryAllocator::printAllocationMap() const {
    dumpAllocatedBlocks();
}

// ==================== 快照管理实现 ====================

void MemoryAllocator::takeSnapshot() {
    MemorySnapshot snapshot;
    snapshot.timestamp = millis();
    snapshot.totalUsage = m_stats.currentUsage;
    snapshot.dramUsage = m_stats.dramUsage;
    snapshot.psramUsage = m_stats.psramUsage;
    snapshot.allocCount = m_stats.allocCount;

    m_snapshots.push_back(snapshot);

    // 限制快照数量
    if (m_snapshots.size() > MAX_SNAPSHOTS) {
        m_snapshots.erase(m_snapshots.begin());
    }
}

std::vector<MemoryAllocator::MemorySnapshot> MemoryAllocator::getSnapshots() const {
    return m_snapshots;
}

void MemoryAllocator::clearSnapshots() {
    m_snapshots.clear();
}

// ==================== 简化的其他方法实现 ====================

void MemoryAllocator::compactMemory() {
    // ESP32不支持内存压缩，这里是空实现
}

void MemoryAllocator::defragmentMemory() {
    // ESP32不支持内存整理，这里是空实现
}

void MemoryAllocator::garbageCollect() {
    // 简化实现：检查内存完整性
    validateMemoryIntegrity();
}

bool MemoryAllocator::preallocateMemory(size_t size, MemoryType type) {
    void* ptr = allocate(size, type, 0);
    if (ptr) {
        deallocate(ptr);
        return true;
    }
    return false;
}
