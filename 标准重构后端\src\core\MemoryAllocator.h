#pragma once

#include <cstddef>
#include <cstdint>
#include <mutex>
#include <map>
#include <vector>
#include <esp_heap_caps.h>

/**
 * ESP32-S3 红外控制系统 - 内存分配器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第六部分：内存管理最佳实践
 * 
 * 内存分配器职责：
 * - 统一的内存分配和释放管理
 * - 内存泄漏检测和防护
 * - 内存使用统计和监控
 * - PSRAM和DRAM的智能分配
 */

class MemoryAllocator {
public:
    // 内存类型枚举
    enum class MemoryType {
        DRAM,           // 内部DRAM
        PSRAM,          // 外部PSRAM
        DMA_CAPABLE,    // DMA兼容内存
        IRAM,           // 指令RAM
        AUTO            // 自动选择
    };
    
    // 内存分配标志
    enum class AllocFlags {
        NONE = 0,
        ZERO_INIT = 1,      // 零初始化
        ALIGN_4 = 2,        // 4字节对齐
        ALIGN_8 = 4,        // 8字节对齐
        ALIGN_16 = 8,       // 16字节对齐
        TRACK_USAGE = 16    // 跟踪使用情况
    };
    
    // 内存块信息
    struct MemoryBlock {
        void* ptr;
        size_t size;
        MemoryType type;
        uint32_t flags;
        uint32_t timestamp;
        const char* file;
        int line;
        const char* function;
        
        MemoryBlock() : ptr(nullptr), size(0), type(MemoryType::AUTO), 
                       flags(0), timestamp(0), file(nullptr), line(0), function(nullptr) {}
    };
    
    // 内存统计信息
    struct MemoryStats {
        size_t totalAllocated;      // 总分配内存
        size_t totalFreed;          // 总释放内存
        size_t currentUsage;        // 当前使用量
        size_t peakUsage;           // 峰值使用量
        uint32_t allocCount;        // 分配次数
        uint32_t freeCount;         // 释放次数
        uint32_t leakCount;         // 泄漏次数
        size_t dramUsage;           // DRAM使用量
        size_t psramUsage;          // PSRAM使用量
        
        MemoryStats() : totalAllocated(0), totalFreed(0), currentUsage(0), peakUsage(0),
                       allocCount(0), freeCount(0), leakCount(0), dramUsage(0), psramUsage(0) {}
    };

private:
    static MemoryAllocator* s_instance;
    static std::mutex s_mutex;
    
    std::map<void*, MemoryBlock> m_allocatedBlocks;
    MemoryStats m_stats;
    bool m_trackingEnabled;
    bool m_leakDetectionEnabled;
    size_t m_maxAllowedUsage;
    
    MemoryAllocator();

public:
    ~MemoryAllocator();
    
    // 单例模式
    static MemoryAllocator& getInstance();
    static void destroyInstance();
    
    // ==================== 内存分配接口 ====================
    
    // 基础分配
    void* allocate(size_t size, MemoryType type = MemoryType::AUTO, uint32_t flags = 0);
    void* allocateAligned(size_t size, size_t alignment, MemoryType type = MemoryType::AUTO);
    void* reallocate(void* ptr, size_t newSize);
    void deallocate(void* ptr);
    
    // 类型化分配
    template<typename T>
    T* allocateArray(size_t count, MemoryType type = MemoryType::AUTO);
    
    template<typename T>
    void deallocateArray(T* ptr);
    
    // 字符串分配
    char* allocateString(size_t length, MemoryType type = MemoryType::AUTO);
    char* duplicateString(const char* str, MemoryType type = MemoryType::AUTO);
    void deallocateString(char* str);
    
    // ==================== 智能指针支持 ====================
    
    // 自定义删除器
    template<typename T>
    struct Deleter {
        void operator()(T* ptr) {
            if (ptr) {
                ptr->~T();
                MemoryAllocator::getInstance().deallocate(ptr);
            }
        }
    };
    
    // 创建智能指针
    template<typename T, typename... Args>
    std::unique_ptr<T, Deleter<T>> makeUnique(Args&&... args);
    
    // ==================== 内存池管理 ====================
    
    // 内存池
    class MemoryPool {
    private:
        void* m_pool;
        size_t m_blockSize;
        size_t m_blockCount;
        std::vector<bool> m_freeBlocks;
        std::mutex m_poolMutex;
        
    public:
        MemoryPool(size_t blockSize, size_t blockCount, MemoryType type = MemoryType::AUTO);
        ~MemoryPool();
        
        void* allocate();
        void deallocate(void* ptr);
        bool isFromPool(void* ptr) const;
        size_t getAvailableBlocks() const;
        size_t getTotalBlocks() const;
    };
    
    // 创建内存池
    std::unique_ptr<MemoryPool> createPool(size_t blockSize, size_t blockCount, MemoryType type = MemoryType::AUTO);
    
    // ==================== 内存监控和统计 ====================
    
    // 获取统计信息
    const MemoryStats& getStats() const;
    void resetStats();
    
    // 内存使用监控
    size_t getCurrentUsage() const;
    size_t getPeakUsage() const;
    size_t getAvailableMemory(MemoryType type = MemoryType::AUTO) const;
    size_t getTotalMemory(MemoryType type = MemoryType::AUTO) const;
    float getMemoryUsagePercent(MemoryType type = MemoryType::AUTO) const;
    
    // 内存碎片分析
    size_t getLargestFreeBlock(MemoryType type = MemoryType::AUTO) const;
    float getFragmentationPercent(MemoryType type = MemoryType::AUTO) const;
    
    // ==================== 内存泄漏检测 ====================
    
    // 泄漏检测控制
    void enableLeakDetection(bool enable = true);
    void enableTracking(bool enable = true);
    bool isLeakDetectionEnabled() const;
    bool isTrackingEnabled() const;
    
    // 泄漏检测和报告
    std::vector<MemoryBlock> detectLeaks() const;
    void reportLeaks() const;
    void dumpAllocatedBlocks() const;
    
    // 设置最大允许使用量
    void setMaxAllowedUsage(size_t maxUsage);
    size_t getMaxAllowedUsage() const;
    
    // ==================== 内存优化 ====================
    
    // 内存压缩和整理
    void compactMemory();
    void defragmentMemory();
    
    // 垃圾回收
    void garbageCollect();
    
    // 内存预分配
    bool preallocateMemory(size_t size, MemoryType type = MemoryType::AUTO);
    
    // ==================== 调试和诊断 ====================
    
    // 内存状态检查
    bool validateMemoryIntegrity() const;
    bool checkForCorruption() const;
    
    // 内存使用报告
    void printMemoryReport() const;
    void printDetailedStats() const;
    void printAllocationMap() const;
    
    // 内存使用历史
    struct MemorySnapshot {
        uint32_t timestamp;
        size_t totalUsage;
        size_t dramUsage;
        size_t psramUsage;
        uint32_t allocCount;
    };
    
    void takeSnapshot();
    std::vector<MemorySnapshot> getSnapshots() const;
    void clearSnapshots();

private:
    // ==================== 内部实现方法 ====================
    
    // 内存分配实现
    void* allocateInternal(size_t size, MemoryType type, uint32_t flags, const char* file, int line, const char* function);
    void deallocateInternal(void* ptr);
    
    // 内存类型选择
    MemoryType selectOptimalMemoryType(size_t size) const;
    uint32_t getHeapCaps(MemoryType type) const;
    
    // 内存对齐
    size_t alignSize(size_t size, size_t alignment) const;
    void* alignPointer(void* ptr, size_t alignment) const;
    
    // 统计更新
    void updateStats(size_t size, MemoryType type, bool isAllocation);
    void updatePeakUsage();
    
    // 内存块管理
    void registerBlock(void* ptr, size_t size, MemoryType type, uint32_t flags, const char* file, int line, const char* function);
    void unregisterBlock(void* ptr);
    MemoryBlock* findBlock(void* ptr);
    
    // 内存验证
    bool isValidPointer(void* ptr) const;
    bool isAligned(void* ptr, size_t alignment) const;
    
    // 错误处理
    void handleAllocationFailure(size_t size, MemoryType type) const;
    void handleMemoryCorruption(void* ptr) const;
    
    // 快照管理
    std::vector<MemorySnapshot> m_snapshots;
    static constexpr size_t MAX_SNAPSHOTS = 100;
};

// ==================== 便利宏定义 ====================

#define MALLOC(size) MemoryAllocator::getInstance().allocate(size, MemoryAllocator::MemoryType::AUTO, 0)
#define MALLOC_PSRAM(size) MemoryAllocator::getInstance().allocate(size, MemoryAllocator::MemoryType::PSRAM, 0)
#define MALLOC_DMA(size) MemoryAllocator::getInstance().allocate(size, MemoryAllocator::MemoryType::DMA_CAPABLE, 0)
#define FREE(ptr) MemoryAllocator::getInstance().deallocate(ptr)

#define NEW_ARRAY(type, count) MemoryAllocator::getInstance().allocateArray<type>(count)
#define DELETE_ARRAY(ptr) MemoryAllocator::getInstance().deallocateArray(ptr)

// 调试版本的分配宏
#ifdef DEBUG
#define DEBUG_MALLOC(size) MemoryAllocator::getInstance().allocateInternal(size, MemoryAllocator::MemoryType::AUTO, static_cast<uint32_t>(MemoryAllocator::AllocFlags::TRACK_USAGE), __FILE__, __LINE__, __FUNCTION__)
#define DEBUG_FREE(ptr) MemoryAllocator::getInstance().deallocate(ptr)
#else
#define DEBUG_MALLOC(size) MALLOC(size)
#define DEBUG_FREE(ptr) FREE(ptr)
#endif

// ==================== 模板实现 ====================

template<typename T>
T* MemoryAllocator::allocateArray(size_t count, MemoryType type) {
    size_t totalSize = sizeof(T) * count;
    void* ptr = allocate(totalSize, type, static_cast<uint32_t>(AllocFlags::ZERO_INIT));
    return static_cast<T*>(ptr);
}

template<typename T>
void MemoryAllocator::deallocateArray(T* ptr) {
    if (ptr) {
        deallocate(ptr);
    }
}

template<typename T, typename... Args>
std::unique_ptr<T, MemoryAllocator::Deleter<T>> MemoryAllocator::makeUnique(Args&&... args) {
    void* ptr = allocate(sizeof(T), MemoryType::AUTO, 0);
    if (!ptr) {
        return nullptr;
    }
    
    T* obj = new(ptr) T(std::forward<Args>(args)...);
    return std::unique_ptr<T, Deleter<T>>(obj);
}
