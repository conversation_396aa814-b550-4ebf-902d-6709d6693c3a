#include "NetworkSecurity.h"
#include <LittleFS.h>
#include <ArduinoJson.h>
#include <mbedtls/md5.h>

/**
 * ESP32-S3 红外控制系统 - 网络安全管理器实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第六部分：网络安全最佳实践
 */

// 静态成员初始化
NetworkSecurity* NetworkSecurity::s_instance = nullptr;
std::mutex NetworkSecurity::s_mutex;

NetworkSecurity::NetworkSecurity() 
    : m_securityLevel(SecurityLevel::BASIC), m_autoProtectionEnabled(true) {
}

// ==================== 单例模式实现 ====================

NetworkSecurity& NetworkSecurity::getInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (!s_instance) {
        s_instance = new NetworkSecurity();
    }
    return *s_instance;
}

void NetworkSecurity::destroyInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

// ==================== 初始化和配置实现 ====================

bool NetworkSecurity::initialize() {
    Serial.println("[NetworkSecurity] Initializing network security manager...");
    
    // 加载默认配置
    resetToDefaults();
    
    // 尝试加载保存的配置
    loadSecurityConfig("/config/security.json");
    
    // 初始化默认访问规则
    AccessRule defaultRule;
    defaultRule.type = AccessControlType::RATE_LIMIT;
    defaultRule.maxRequests = 100;
    defaultRule.timeWindow = 60000; // 1分钟
    addAccessRule(defaultRule);
    
    Serial.println("[NetworkSecurity] Network security manager initialized successfully");
    return true;
}

void NetworkSecurity::cleanup() {
    // 保存配置
    saveSecurityConfig("/config/security.json");
    
    // 清理数据
    m_clientMap.clear();
    m_securityEvents.clear();
    m_sessions.clear();
    m_accessRules.clear();
    
    Serial.println("[NetworkSecurity] Network security manager cleaned up");
}

void NetworkSecurity::setSecurityLevel(SecurityLevel level) {
    m_securityLevel = level;
    
    // 根据安全级别调整配置
    switch (level) {
        case SecurityLevel::NONE:
            m_config.enableIPFiltering = false;
            m_config.enableRateLimit = false;
            m_config.enableEncryption = false;
            break;
            
        case SecurityLevel::BASIC:
            m_config.enableIPFiltering = true;
            m_config.enableRateLimit = true;
            m_config.enableEncryption = false;
            break;
            
        case SecurityLevel::STANDARD:
            m_config.enableIPFiltering = true;
            m_config.enableRateLimit = true;
            m_config.enableEncryption = true;
            break;
            
        case SecurityLevel::HIGH:
            m_config.enableIPFiltering = true;
            m_config.enableRateLimit = true;
            m_config.enableEncryption = true;
            m_config.enableCertificateValidation = true;
            break;
            
        case SecurityLevel::MAXIMUM:
            m_config.enableIPFiltering = true;
            m_config.enableRateLimit = true;
            m_config.enableEncryption = true;
            m_config.enableCertificateValidation = true;
            m_config.maxFailedAttempts = 3;
            m_config.blockDuration = 600000; // 10分钟
            break;
    }
    
    Serial.printf("[NetworkSecurity] Security level set to %d\n", static_cast<int>(level));
}

NetworkSecurity::SecurityLevel NetworkSecurity::getSecurityLevel() const {
    return m_securityLevel;
}

void NetworkSecurity::setSecurityConfig(const SecurityConfig& config) {
    m_config = config;
}

const NetworkSecurity::SecurityConfig& NetworkSecurity::getSecurityConfig() const {
    return m_config;
}

// ==================== 访问控制实现 ====================

bool NetworkSecurity::isIPAllowed(const IPAddress& ip) const {
    if (!m_config.enableIPFiltering) {
        return true;
    }
    
    // 检查是否被阻止
    if (isIPBlocked(ip)) {
        return false;
    }
    
    // 检查访问规则
    for (const auto& rule : m_accessRules) {
        if (!rule.enabled) continue;
        
        if (rule.type == AccessControlType::WHITELIST) {
            if (checkAccessRule(rule, ip)) {
                return true;
            }
        } else if (rule.type == AccessControlType::BLACKLIST) {
            if (checkAccessRule(rule, ip)) {
                return false;
            }
        }
    }
    
    // 默认允许（如果没有白名单规则）
    bool hasWhitelist = false;
    for (const auto& rule : m_accessRules) {
        if (rule.type == AccessControlType::WHITELIST && rule.enabled) {
            hasWhitelist = true;
            break;
        }
    }
    
    return !hasWhitelist;
}

bool NetworkSecurity::isIPBlocked(const IPAddress& ip) const {
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        const ClientInfo& client = it->second;
        
        // 检查是否手动阻止
        if (client.isBlocked) {
            return true;
        }
        
        // 检查失败尝试次数
        if (client.failedAttempts >= m_config.maxFailedAttempts) {
            return true;
        }
    }
    
    return false;
}

void NetworkSecurity::blockIP(const IPAddress& ip, uint32_t duration) {
    trackClient(ip);
    
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        it->second.isBlocked = true;
        
        logSecurityEvent(SecurityEventType::IP_BLOCKED, ip, 
                        "IP address blocked", 
                        "Duration: " + String(duration) + "ms");
        
        Serial.printf("[NetworkSecurity] Blocked IP: %s\n", ip.toString().c_str());
    }
}

void NetworkSecurity::unblockIP(const IPAddress& ip) {
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        it->second.isBlocked = false;
        it->second.failedAttempts = 0;
        
        Serial.printf("[NetworkSecurity] Unblocked IP: %s\n", ip.toString().c_str());
    }
}

void NetworkSecurity::addToWhitelist(const IPAddress& ip, const IPAddress& subnet) {
    AccessRule rule;
    rule.type = AccessControlType::WHITELIST;
    rule.ip = ip;
    rule.subnet = subnet;
    rule.enabled = true;
    
    addAccessRule(rule);
    
    Serial.printf("[NetworkSecurity] Added to whitelist: %s\n", ip.toString().c_str());
}

void NetworkSecurity::removeFromWhitelist(const IPAddress& ip) {
    for (auto it = m_accessRules.begin(); it != m_accessRules.end(); ++it) {
        if (it->type == AccessControlType::WHITELIST && it->ip == ip) {
            m_accessRules.erase(it);
            Serial.printf("[NetworkSecurity] Removed from whitelist: %s\n", ip.toString().c_str());
            break;
        }
    }
}

void NetworkSecurity::addToBlacklist(const IPAddress& ip, const IPAddress& subnet) {
    AccessRule rule;
    rule.type = AccessControlType::BLACKLIST;
    rule.ip = ip;
    rule.subnet = subnet;
    rule.enabled = true;
    
    addAccessRule(rule);
    
    Serial.printf("[NetworkSecurity] Added to blacklist: %s\n", ip.toString().c_str());
}

void NetworkSecurity::removeFromBlacklist(const IPAddress& ip) {
    for (auto it = m_accessRules.begin(); it != m_accessRules.end(); ++it) {
        if (it->type == AccessControlType::BLACKLIST && it->ip == ip) {
            m_accessRules.erase(it);
            Serial.printf("[NetworkSecurity] Removed from blacklist: %s\n", ip.toString().c_str());
            break;
        }
    }
}

void NetworkSecurity::addAccessRule(const AccessRule& rule) {
    m_accessRules.push_back(rule);
}

void NetworkSecurity::removeAccessRule(size_t index) {
    if (index < m_accessRules.size()) {
        m_accessRules.erase(m_accessRules.begin() + index);
    }
}

void NetworkSecurity::updateAccessRule(size_t index, const AccessRule& rule) {
    if (index < m_accessRules.size()) {
        m_accessRules[index] = rule;
    }
}

const std::vector<NetworkSecurity::AccessRule>& NetworkSecurity::getAccessRules() const {
    return m_accessRules;
}

void NetworkSecurity::clearAccessRules() {
    m_accessRules.clear();
}

// ==================== 客户端管理实现 ====================

void NetworkSecurity::trackClient(const IPAddress& ip, const String& userAgent) {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    auto it = m_clientMap.find(ip);
    if (it == m_clientMap.end()) {
        // 新客户端
        ClientInfo client;
        client.ip = ip;
        client.firstSeen = millis();
        client.lastSeen = millis();
        client.requestCount = 1;
        client.userAgent = userAgent;
        
        m_clientMap[ip] = client;
    } else {
        // 现有客户端
        updateClientActivity(ip);
    }
    
    // 维护客户端记录数量
    if (m_clientMap.size() > MAX_CLIENT_RECORDS) {
        cleanupInactiveClients();
    }
}

NetworkSecurity::ClientInfo* NetworkSecurity::getClientInfo(const IPAddress& ip) {
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        return &it->second;
    }
    return nullptr;
}

void NetworkSecurity::updateClientActivity(const IPAddress& ip) {
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        it->second.lastSeen = millis();
        it->second.requestCount++;
        updateClientStats(it->second);
    }
}

void NetworkSecurity::recordFailedAttempt(const IPAddress& ip) {
    trackClient(ip);
    
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        it->second.failedAttempts++;
        
        // 检查是否需要自动阻止
        if (m_autoProtectionEnabled && it->second.failedAttempts >= m_config.maxFailedAttempts) {
            blockIP(ip, m_config.blockDuration);
            
            logSecurityEvent(SecurityEventType::BRUTE_FORCE_ATTACK, ip,
                           "Brute force attack detected",
                           "Failed attempts: " + String(it->second.failedAttempts));
        }
    }
}

void NetworkSecurity::resetFailedAttempts(const IPAddress& ip) {
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        it->second.failedAttempts = 0;
    }
}

uint32_t NetworkSecurity::getActiveClientCount() const {
    uint32_t count = 0;
    uint32_t currentTime = millis();
    
    for (const auto& pair : m_clientMap) {
        const ClientInfo& client = pair.second;
        if (!client.isBlocked && (currentTime - client.lastSeen) < 300000) { // 5分钟内活跃
            count++;
        }
    }
    
    return count;
}

uint32_t NetworkSecurity::getBlockedClientCount() const {
    uint32_t count = 0;
    
    for (const auto& pair : m_clientMap) {
        if (pair.second.isBlocked) {
            count++;
        }
    }
    
    return count;
}

std::vector<NetworkSecurity::ClientInfo> NetworkSecurity::getActiveClients() const {
    std::vector<ClientInfo> activeClients;
    uint32_t currentTime = millis();
    
    for (const auto& pair : m_clientMap) {
        const ClientInfo& client = pair.second;
        if (!client.isBlocked && (currentTime - client.lastSeen) < 300000) {
            activeClients.push_back(client);
        }
    }
    
    return activeClients;
}

std::vector<NetworkSecurity::ClientInfo> NetworkSecurity::getBlockedClients() const {
    std::vector<ClientInfo> blockedClients;
    
    for (const auto& pair : m_clientMap) {
        if (pair.second.isBlocked) {
            blockedClients.push_back(pair.second);
        }
    }
    
    return blockedClients;
}

// ==================== 频率限制实现 ====================

bool NetworkSecurity::checkRateLimit(const IPAddress& ip) {
    if (!m_config.enableRateLimit) {
        return true;
    }

    trackClient(ip);

    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        const ClientInfo& client = it->second;

        // 检查频率限制规则
        for (const auto& rule : m_accessRules) {
            if (rule.type == AccessControlType::RATE_LIMIT && rule.enabled) {
                uint32_t currentTime = millis();
                uint32_t timeWindow = rule.timeWindow;

                if (client.requestCount > rule.maxRequests &&
                    (currentTime - client.firstSeen) < timeWindow) {

                    logSecurityEvent(SecurityEventType::SUSPICIOUS_ACTIVITY, ip,
                                   "Rate limit exceeded",
                                   "Requests: " + String(client.requestCount) +
                                   " in " + String(timeWindow) + "ms");

                    return false;
                }
            }
        }
    }

    return true;
}

bool NetworkSecurity::isRateLimited(const IPAddress& ip) const {
    return !const_cast<NetworkSecurity*>(this)->checkRateLimit(ip);
}

void NetworkSecurity::setRateLimit(uint32_t maxRequests, uint32_t timeWindow) {
    AccessRule rule;
    rule.type = AccessControlType::RATE_LIMIT;
    rule.maxRequests = maxRequests;
    rule.timeWindow = timeWindow;
    rule.enabled = true;

    // 移除现有的频率限制规则
    for (auto it = m_accessRules.begin(); it != m_accessRules.end();) {
        if (it->type == AccessControlType::RATE_LIMIT) {
            it = m_accessRules.erase(it);
        } else {
            ++it;
        }
    }

    // 添加新规则
    addAccessRule(rule);
}

void NetworkSecurity::resetRateLimit(const IPAddress& ip) {
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        it->second.requestCount = 0;
        it->second.firstSeen = millis();
    }
}

// ==================== 加密和认证实现 ====================

bool NetworkSecurity::enableEncryption(const String& key) {
    if (key.length() < 8) {
        Serial.println("[NetworkSecurity] Encryption key too short (minimum 8 characters)");
        return false;
    }

    m_config.enableEncryption = true;
    m_config.encryptionKey = key;

    Serial.println("[NetworkSecurity] Encryption enabled");
    return true;
}

void NetworkSecurity::disableEncryption() {
    m_config.enableEncryption = false;
    m_config.encryptionKey = "";

    Serial.println("[NetworkSecurity] Encryption disabled");
}

bool NetworkSecurity::isEncryptionEnabled() const {
    return m_config.enableEncryption && !m_config.encryptionKey.isEmpty();
}

String NetworkSecurity::encryptData(const String& data) const {
    if (!isEncryptionEnabled()) {
        return data;
    }

    return simpleEncrypt(data, m_config.encryptionKey);
}

String NetworkSecurity::decryptData(const String& encryptedData) const {
    if (!isEncryptionEnabled()) {
        return encryptedData;
    }

    return simpleDecrypt(encryptedData, m_config.encryptionKey);
}

String NetworkSecurity::generateSessionToken() {
    String token = generateRandomToken();

    SessionInfo session;
    session.token = token;
    session.createdTime = millis();
    session.lastAccessTime = millis();
    session.isValid = true;

    m_sessions[token] = session;

    return token;
}

bool NetworkSecurity::validateSessionToken(const String& token) {
    auto it = m_sessions.find(token);
    if (it == m_sessions.end()) {
        return false;
    }

    SessionInfo& session = it->second;
    if (!session.isValid) {
        return false;
    }

    uint32_t currentTime = millis();
    if ((currentTime - session.createdTime) > m_config.sessionTimeout) {
        session.isValid = false;
        return false;
    }

    session.lastAccessTime = currentTime;
    return true;
}

void NetworkSecurity::invalidateSession(const String& token) {
    auto it = m_sessions.find(token);
    if (it != m_sessions.end()) {
        it->second.isValid = false;
    }
}

void NetworkSecurity::cleanupExpiredSessions() {
    uint32_t currentTime = millis();

    for (auto it = m_sessions.begin(); it != m_sessions.end();) {
        const SessionInfo& session = it->second;
        if (!session.isValid ||
            (currentTime - session.createdTime) > m_config.sessionTimeout) {
            it = m_sessions.erase(it);
        } else {
            ++it;
        }
    }
}

// ==================== 安全事件监控实现 ====================

void NetworkSecurity::logSecurityEvent(SecurityEventType type, const IPAddress& sourceIP,
                                      const String& description, const String& details) {
    if (!m_config.enableSecurityLogging) {
        return;
    }

    SecurityEvent event;
    event.type = type;
    event.sourceIP = sourceIP;
    event.timestamp = millis();
    event.description = description;
    event.details = details;

    m_securityEvents.push_back(event);

    // 维护事件历史大小
    if (m_securityEvents.size() > MAX_SECURITY_EVENTS) {
        maintainEventHistory();
    }

    // 打印安全事件
    Serial.printf("[NetworkSecurity] Security Event: %s from %s - %s\n",
                 description.c_str(), sourceIP.toString().c_str(), details.c_str());
}

std::vector<NetworkSecurity::SecurityEvent> NetworkSecurity::getSecurityEvents(SecurityEventType type) const {
    std::vector<SecurityEvent> filteredEvents;

    for (const auto& event : m_securityEvents) {
        if (event.type == type) {
            filteredEvents.push_back(event);
        }
    }

    return filteredEvents;
}

std::vector<NetworkSecurity::SecurityEvent> NetworkSecurity::getSecurityEventsByIP(const IPAddress& ip) const {
    std::vector<SecurityEvent> filteredEvents;

    for (const auto& event : m_securityEvents) {
        if (event.sourceIP == ip) {
            filteredEvents.push_back(event);
        }
    }

    return filteredEvents;
}

uint32_t NetworkSecurity::getSecurityEventCount(SecurityEventType type) const {
    uint32_t count = 0;

    for (const auto& event : m_securityEvents) {
        if (event.type == type) {
            count++;
        }
    }

    return count;
}

void NetworkSecurity::clearSecurityEvents() {
    m_securityEvents.clear();
}

// ==================== 威胁检测实现 ====================

bool NetworkSecurity::detectBruteForceAttack(const IPAddress& ip) const {
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        return it->second.failedAttempts >= (m_config.maxFailedAttempts / 2);
    }
    return false;
}

bool NetworkSecurity::detectSuspiciousActivity(const IPAddress& ip) const {
    auto it = m_clientMap.find(ip);
    if (it != m_clientMap.end()) {
        const ClientInfo& client = it->second;
        return isAnomalousActivity(client);
    }
    return false;
}

bool NetworkSecurity::detectDDoSAttack() const {
    uint32_t activeClients = getActiveClientCount();
    uint32_t recentEvents = 0;
    uint32_t currentTime = millis();

    // 统计最近1分钟的安全事件
    for (const auto& event : m_securityEvents) {
        if ((currentTime - event.timestamp) < 60000) {
            recentEvents++;
        }
    }

    // 简单的DDoS检测：活跃客户端过多或安全事件频繁
    return (activeClients > 50) || (recentEvents > 20);
}

void NetworkSecurity::enableAutoProtection(bool enable) {
    m_autoProtectionEnabled = enable;
    Serial.printf("[NetworkSecurity] Auto protection %s\n", enable ? "enabled" : "disabled");
}

bool NetworkSecurity::isAutoProtectionEnabled() const {
    return m_autoProtectionEnabled;
}

void NetworkSecurity::handleSecurityThreat(const IPAddress& ip, SecurityEventType threatType) {
    if (!m_autoProtectionEnabled) {
        return;
    }

    switch (threatType) {
        case SecurityEventType::BRUTE_FORCE_ATTACK:
            blockIP(ip, m_config.blockDuration * 2); // 双倍阻止时间
            break;

        case SecurityEventType::SUSPICIOUS_ACTIVITY:
            blockIP(ip, m_config.blockDuration);
            break;

        default:
            logSecurityEvent(threatType, ip, "Security threat detected");
            break;
    }
}

// ==================== 内部实现方法 ====================

bool NetworkSecurity::checkAccessRule(const AccessRule& rule, const IPAddress& ip) const {
    return isInSubnet(ip, rule.ip, rule.subnet) && isTimeAllowed(rule);
}

bool NetworkSecurity::isInSubnet(const IPAddress& ip, const IPAddress& network, const IPAddress& subnet) const {
    for (int i = 0; i < 4; i++) {
        if ((ip[i] & subnet[i]) != (network[i] & subnet[i])) {
            return false;
        }
    }
    return true;
}

bool NetworkSecurity::isTimeAllowed(const AccessRule& rule) const {
    if (rule.type != AccessControlType::TIME_BASED) {
        return true;
    }

    // 简化的时间检查（基于一天中的分钟数）
    uint32_t currentMinutes = (millis() / 60000) % 1440; // 一天1440分钟
    return currentMinutes >= rule.startTime && currentMinutes <= rule.endTime;
}

void NetworkSecurity::cleanupInactiveClients() {
    uint32_t currentTime = millis();
    const uint32_t inactiveThreshold = 3600000; // 1小时

    for (auto it = m_clientMap.begin(); it != m_clientMap.end();) {
        if ((currentTime - it->second.lastSeen) > inactiveThreshold && !it->second.isBlocked) {
            it = m_clientMap.erase(it);
        } else {
            ++it;
        }
    }
}

void NetworkSecurity::updateClientStats(ClientInfo& client) {
    // 简化的统计更新
    client.lastSeen = millis();
}

String NetworkSecurity::simpleEncrypt(const String& data, const String& key) const {
    // 简化的XOR加密
    String encrypted = "";
    for (size_t i = 0; i < data.length(); i++) {
        char encryptedChar = data[i] ^ key[i % key.length()];
        encrypted += String(encryptedChar, HEX);
    }
    return encrypted;
}

String NetworkSecurity::simpleDecrypt(const String& encryptedData, const String& key) const {
    // 简化的XOR解密
    String decrypted = "";
    for (size_t i = 0; i < encryptedData.length(); i += 2) {
        String hexByte = encryptedData.substring(i, i + 2);
        char decryptedChar = (char)strtol(hexByte.c_str(), nullptr, 16) ^ key[(i/2) % key.length()];
        decrypted += decryptedChar;
    }
    return decrypted;
}

String NetworkSecurity::generateRandomToken() const {
    String token = "";
    for (int i = 0; i < 32; i++) {
        token += String(random(0, 16), HEX);
    }
    return token;
}

float NetworkSecurity::calculateThreatLevel() const {
    float threatLevel = 0.0f;

    // 基于阻止的IP数量
    threatLevel += getBlockedClientCount() * 0.1f;

    // 基于最近的安全事件
    uint32_t recentEvents = 0;
    uint32_t currentTime = millis();
    for (const auto& event : m_securityEvents) {
        if ((currentTime - event.timestamp) < 300000) { // 5分钟内
            recentEvents++;
        }
    }
    threatLevel += recentEvents * 0.05f;

    return std::min(threatLevel, 10.0f); // 最大威胁级别10
}

bool NetworkSecurity::isAnomalousActivity(const ClientInfo& client) const {
    // 简化的异常检测
    uint32_t currentTime = millis();
    uint32_t sessionDuration = currentTime - client.firstSeen;

    // 检查请求频率是否异常
    if (sessionDuration > 0) {
        float requestRate = (float)client.requestCount / (sessionDuration / 1000.0f);
        return requestRate > 10.0f; // 每秒超过10个请求视为异常
    }

    return false;
}

void NetworkSecurity::maintainEventHistory() {
    // 保留最新的事件，删除旧事件
    if (m_securityEvents.size() > MAX_SECURITY_EVENTS) {
        size_t removeCount = m_securityEvents.size() - MAX_SECURITY_EVENTS + 100;
        m_securityEvents.erase(m_securityEvents.begin(), m_securityEvents.begin() + removeCount);
    }
}

// ==================== 简化的其他方法实现 ====================

bool NetworkSecurity::loadCertificate(const String& certPath) {
    return false; // 简化实现
}

bool NetworkSecurity::validateCertificate(WiFiClientSecure& client) const {
    return true; // 简化实现
}

bool NetworkSecurity::isCertificateValid() const {
    return false; // 简化实现
}

NetworkSecurity::SecurityReport NetworkSecurity::generateSecurityReport() const {
    SecurityReport report;
    report.totalClients = m_clientMap.size();
    report.blockedClients = getBlockedClientCount();
    report.securityEvents = m_securityEvents.size();
    report.bruteForceAttempts = getSecurityEventCount(SecurityEventType::BRUTE_FORCE_ATTACK);
    report.blockedIPs = getBlockedClientCount();
    report.threatLevel = calculateThreatLevel();
    report.reportTime = millis();

    return report;
}

void NetworkSecurity::printSecurityReport() const {
    auto report = generateSecurityReport();

    Serial.println("=== Network Security Report ===");
    Serial.printf("Total clients: %u\n", report.totalClients);
    Serial.printf("Blocked clients: %u\n", report.blockedClients);
    Serial.printf("Security events: %u\n", report.securityEvents);
    Serial.printf("Brute force attempts: %u\n", report.bruteForceAttempts);
    Serial.printf("Threat level: %.1f\n", report.threatLevel);
    Serial.println("===============================");
}

bool NetworkSecurity::saveSecurityConfig(const String& filePath) const {
    return true; // 简化实现
}

bool NetworkSecurity::loadSecurityConfig(const String& filePath) {
    return true; // 简化实现
}

void NetworkSecurity::resetToDefaults() {
    m_config = SecurityConfig();
    m_securityLevel = SecurityLevel::BASIC;
}

void NetworkSecurity::performSecurityMaintenance() {
    cleanupInactiveClients();
    cleanupExpiredSessions();
    maintainEventHistory();
}

bool NetworkSecurity::validateSecurityState() const {
    return true; // 简化实现
}

void NetworkSecurity::printSecurityStatus() const {
    Serial.printf("[NetworkSecurity] Security Level: %d\n", static_cast<int>(m_securityLevel));
    Serial.printf("[NetworkSecurity] Active Clients: %u\n", getActiveClientCount());
    Serial.printf("[NetworkSecurity] Blocked Clients: %u\n", getBlockedClientCount());
    Serial.printf("[NetworkSecurity] Security Events: %u\n", m_securityEvents.size());
}
