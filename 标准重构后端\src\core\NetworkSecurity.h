#pragma once

#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <vector>
#include <map>
#include <mutex>
#include <cstdint>

/**
 * ESP32-S3 红外控制系统 - 网络安全管理器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第六部分：网络安全最佳实践
 * 
 * 网络安全管理器职责：
 * - WiFi连接安全管理
 * - 访问控制和IP白名单
 * - 加密通信和证书管理
 * - 安全事件监控和日志
 */

class NetworkSecurity {
public:
    // 安全级别枚举
    enum class SecurityLevel {
        NONE,           // 无安全措施
        BASIC,          // 基础安全（IP过滤）
        STANDARD,       // 标准安全（加密+认证）
        HIGH,           // 高级安全（证书验证）
        MAXIMUM         // 最高安全（全面防护）
    };
    
    // 访问控制类型
    enum class AccessControlType {
        WHITELIST,      // 白名单模式
        BLACKLIST,      // 黑名单模式
        RATE_LIMIT,     // 频率限制
        TIME_BASED      // 时间段限制
    };
    
    // 安全事件类型
    enum class SecurityEventType {
        UNAUTHORIZED_ACCESS,    // 未授权访问
        BRUTE_FORCE_ATTACK,    // 暴力破解攻击
        IP_BLOCKED,            // IP被阻止
        CERTIFICATE_ERROR,     // 证书错误
        ENCRYPTION_FAILURE,    // 加密失败
        SUSPICIOUS_ACTIVITY    // 可疑活动
    };
    
    // 客户端信息
    struct ClientInfo {
        IPAddress ip;
        String macAddress;
        uint32_t firstSeen;
        uint32_t lastSeen;
        uint32_t requestCount;
        uint32_t failedAttempts;
        bool isBlocked;
        String userAgent;
        
        ClientInfo() : firstSeen(0), lastSeen(0), requestCount(0), 
                      failedAttempts(0), isBlocked(false) {}
    };
    
    // 安全事件记录
    struct SecurityEvent {
        SecurityEventType type;
        IPAddress sourceIP;
        uint32_t timestamp;
        String description;
        String details;
        
        SecurityEvent() : type(SecurityEventType::UNAUTHORIZED_ACCESS), timestamp(0) {}
    };
    
    // 访问控制规则
    struct AccessRule {
        AccessControlType type;
        IPAddress ip;
        IPAddress subnet;
        uint32_t startTime;     // 时间段开始（分钟）
        uint32_t endTime;       // 时间段结束（分钟）
        uint32_t maxRequests;   // 最大请求数
        uint32_t timeWindow;    // 时间窗口（毫秒）
        bool enabled;
        
        AccessRule() : type(AccessControlType::WHITELIST), startTime(0), endTime(1440),
                      maxRequests(100), timeWindow(60000), enabled(true) {}
    };

private:
    static NetworkSecurity* s_instance;
    static std::mutex s_mutex;
    
    SecurityLevel m_securityLevel;
    std::vector<AccessRule> m_accessRules;
    std::map<IPAddress, ClientInfo> m_clientMap;
    std::vector<SecurityEvent> m_securityEvents;
    
    // 安全配置
    struct SecurityConfig {
        bool enableIPFiltering;
        bool enableRateLimit;
        bool enableEncryption;
        bool enableCertificateValidation;
        bool enableSecurityLogging;
        uint32_t maxFailedAttempts;
        uint32_t blockDuration;
        uint32_t sessionTimeout;
        String encryptionKey;
        
        SecurityConfig() : enableIPFiltering(true), enableRateLimit(true), 
                          enableEncryption(false), enableCertificateValidation(false),
                          enableSecurityLogging(true), maxFailedAttempts(5), 
                          blockDuration(300000), sessionTimeout(1800000) {} // 5分钟阻止，30分钟会话
    };
    
    SecurityConfig m_config;
    
    NetworkSecurity();

public:
    ~NetworkSecurity() = default;
    
    // 单例模式
    static NetworkSecurity& getInstance();
    static void destroyInstance();
    
    // ==================== 初始化和配置 ====================
    
    // 初始化安全管理器
    bool initialize();
    void cleanup();
    
    // 设置安全级别
    void setSecurityLevel(SecurityLevel level);
    SecurityLevel getSecurityLevel() const;
    
    // 配置管理
    void setSecurityConfig(const SecurityConfig& config);
    const SecurityConfig& getSecurityConfig() const;
    
    // ==================== 访问控制 ====================
    
    // IP访问控制
    bool isIPAllowed(const IPAddress& ip) const;
    bool isIPBlocked(const IPAddress& ip) const;
    void blockIP(const IPAddress& ip, uint32_t duration = 0);
    void unblockIP(const IPAddress& ip);
    void addToWhitelist(const IPAddress& ip, const IPAddress& subnet = IPAddress(255, 255, 255, 255));
    void removeFromWhitelist(const IPAddress& ip);
    void addToBlacklist(const IPAddress& ip, const IPAddress& subnet = IPAddress(255, 255, 255, 255));
    void removeFromBlacklist(const IPAddress& ip);
    
    // 访问规则管理
    void addAccessRule(const AccessRule& rule);
    void removeAccessRule(size_t index);
    void updateAccessRule(size_t index, const AccessRule& rule);
    const std::vector<AccessRule>& getAccessRules() const;
    void clearAccessRules();
    
    // ==================== 客户端管理 ====================
    
    // 客户端跟踪
    void trackClient(const IPAddress& ip, const String& userAgent = "");
    ClientInfo* getClientInfo(const IPAddress& ip);
    void updateClientActivity(const IPAddress& ip);
    void recordFailedAttempt(const IPAddress& ip);
    void resetFailedAttempts(const IPAddress& ip);
    
    // 客户端统计
    uint32_t getActiveClientCount() const;
    uint32_t getBlockedClientCount() const;
    std::vector<ClientInfo> getActiveClients() const;
    std::vector<ClientInfo> getBlockedClients() const;
    
    // ==================== 频率限制 ====================
    
    // 请求频率检查
    bool checkRateLimit(const IPAddress& ip);
    bool isRateLimited(const IPAddress& ip) const;
    void setRateLimit(uint32_t maxRequests, uint32_t timeWindow);
    void resetRateLimit(const IPAddress& ip);
    
    // ==================== 加密和认证 ====================
    
    // 加密管理
    bool enableEncryption(const String& key);
    void disableEncryption();
    bool isEncryptionEnabled() const;
    String encryptData(const String& data) const;
    String decryptData(const String& encryptedData) const;
    
    // 证书管理
    bool loadCertificate(const String& certPath);
    bool validateCertificate(WiFiClientSecure& client) const;
    bool isCertificateValid() const;
    
    // 会话管理
    String generateSessionToken();
    bool validateSessionToken(const String& token);
    void invalidateSession(const String& token);
    void cleanupExpiredSessions();
    
    // ==================== 安全事件监控 ====================
    
    // 事件记录
    void logSecurityEvent(SecurityEventType type, const IPAddress& sourceIP, 
                         const String& description, const String& details = "");
    
    // 事件查询
    std::vector<SecurityEvent> getSecurityEvents(SecurityEventType type = SecurityEventType::UNAUTHORIZED_ACCESS) const;
    std::vector<SecurityEvent> getSecurityEventsByIP(const IPAddress& ip) const;
    std::vector<SecurityEvent> getSecurityEventsByTimeRange(uint32_t startTime, uint32_t endTime) const;
    
    // 事件统计
    uint32_t getSecurityEventCount(SecurityEventType type = SecurityEventType::UNAUTHORIZED_ACCESS) const;
    void clearSecurityEvents();
    
    // ==================== 威胁检测 ====================
    
    // 攻击检测
    bool detectBruteForceAttack(const IPAddress& ip) const;
    bool detectSuspiciousActivity(const IPAddress& ip) const;
    bool detectDDoSAttack() const;
    
    // 自动防护
    void enableAutoProtection(bool enable = true);
    bool isAutoProtectionEnabled() const;
    void handleSecurityThreat(const IPAddress& ip, SecurityEventType threatType);
    
    // ==================== 安全报告 ====================
    
    // 安全状态报告
    struct SecurityReport {
        uint32_t totalClients;
        uint32_t blockedClients;
        uint32_t securityEvents;
        uint32_t bruteForceAttempts;
        uint32_t blockedIPs;
        float threatLevel;
        String lastThreat;
        uint32_t reportTime;
        
        SecurityReport() : totalClients(0), blockedClients(0), securityEvents(0),
                          bruteForceAttempts(0), blockedIPs(0), threatLevel(0.0f), reportTime(0) {}
    };
    
    SecurityReport generateSecurityReport() const;
    void printSecurityReport() const;
    void exportSecurityLog(const String& filePath) const;
    
    // ==================== 配置和维护 ====================
    
    // 配置导入导出
    bool saveSecurityConfig(const String& filePath) const;
    bool loadSecurityConfig(const String& filePath);
    void resetToDefaults();
    
    // 维护操作
    void performSecurityMaintenance();
    void cleanupOldRecords();
    void optimizeSecurityData();
    
    // ==================== 调试和诊断 ====================
    
    // 安全状态检查
    bool validateSecurityState() const;
    void printSecurityStatus() const;
    void dumpClientMap() const;
    void dumpAccessRules() const;

private:
    // ==================== 内部实现方法 ====================
    
    // 访问控制实现
    bool checkAccessRule(const AccessRule& rule, const IPAddress& ip) const;
    bool isInSubnet(const IPAddress& ip, const IPAddress& network, const IPAddress& subnet) const;
    bool isTimeAllowed(const AccessRule& rule) const;
    
    // 客户端管理实现
    void cleanupInactiveClients();
    void updateClientStats(ClientInfo& client);
    
    // 加密实现
    String simpleEncrypt(const String& data, const String& key) const;
    String simpleDecrypt(const String& encryptedData, const String& key) const;
    
    // 会话管理实现
    struct SessionInfo {
        String token;
        IPAddress clientIP;
        uint32_t createdTime;
        uint32_t lastAccessTime;
        bool isValid;
        
        SessionInfo() : createdTime(0), lastAccessTime(0), isValid(false) {}
    };
    
    std::map<String, SessionInfo> m_sessions;
    String generateRandomToken() const;
    
    // 威胁检测实现
    float calculateThreatLevel() const;
    bool isAnomalousActivity(const ClientInfo& client) const;
    
    // 事件管理
    void maintainEventHistory();
    static constexpr size_t MAX_SECURITY_EVENTS = 1000;
    static constexpr size_t MAX_CLIENT_RECORDS = 500;
    
    // 自动防护
    bool m_autoProtectionEnabled;
    void autoBlockSuspiciousIP(const IPAddress& ip);
    void autoAdjustSecurityLevel();
};
