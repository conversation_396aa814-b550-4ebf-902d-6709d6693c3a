#pragma once

#include <esp_psram.h>
#include <esp_heap_caps.h>
#include <mutex>
#include <vector>
#include <map>

/**
 * ESP32-S3 红外控制系统 - PSRAM管理器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第六部分：内存管理最佳实践
 * 
 * PSRAM管理器职责：
 * - PSRAM初始化和配置管理
 * - PSRAM内存分配和释放
 * - PSRAM使用统计和监控
 * - PSRAM性能优化和缓存管理
 */

class PSRAMManager {
public:
    // PSRAM状态枚举
    enum class PSRAMStatus {
        NOT_AVAILABLE,      // PSRAM不可用
        AVAILABLE,          // PSRAM可用但未初始化
        INITIALIZED,        // PSRAM已初始化
        ERROR,              // PSRAM错误状态
        DISABLED            // PSRAM被禁用
    };
    
    // PSRAM分配策略
    enum class AllocationStrategy {
        FIRST_FIT,          // 首次适应
        BEST_FIT,           // 最佳适应
        WORST_FIT,          // 最差适应
        BUDDY_SYSTEM        // 伙伴系统
    };
    
    // PSRAM块信息
    struct PSRAMBlock {
        void* ptr;
        size_t size;
        uint32_t timestamp;
        bool isAllocated;
        const char* tag;
        
        PSRAMBlock() : ptr(nullptr), size(0), timestamp(0), isAllocated(false), tag(nullptr) {}
    };
    
    // PSRAM统计信息
    struct PSRAMStats {
        size_t totalSize;           // 总PSRAM大小
        size_t usedSize;            // 已使用大小
        size_t freeSize;            // 可用大小
        size_t largestFreeBlock;    // 最大可用块
        uint32_t allocCount;        // 分配次数
        uint32_t freeCount;         // 释放次数
        uint32_t fragmentCount;     // 碎片数量
        float fragmentationRatio;   // 碎片率
        uint32_t peakUsage;         // 峰值使用量
        
        PSRAMStats() : totalSize(0), usedSize(0), freeSize(0), largestFreeBlock(0),
                      allocCount(0), freeCount(0), fragmentCount(0), 
                      fragmentationRatio(0.0f), peakUsage(0) {}
    };
    
    // PSRAM配置
    struct PSRAMConfig {
        bool enablePSRAM;
        AllocationStrategy strategy;
        size_t cacheSize;
        bool enableDefragmentation;
        uint32_t defragThreshold;
        bool enableStatistics;
        bool enableDebugLogging;
        
        PSRAMConfig() : enablePSRAM(true), strategy(AllocationStrategy::FIRST_FIT),
                       cacheSize(64 * 1024), enableDefragmentation(true),
                       defragThreshold(50), enableStatistics(true), enableDebugLogging(false) {}
    };

private:
    static PSRAMManager* s_instance;
    static std::mutex s_mutex;
    
    PSRAMStatus m_status;
    PSRAMConfig m_config;
    PSRAMStats m_stats;
    std::map<void*, PSRAMBlock> m_allocatedBlocks;
    std::vector<PSRAMBlock> m_freeBlocks;
    
    PSRAMManager();

public:
    ~PSRAMManager();
    
    // 单例模式
    static PSRAMManager& getInstance();
    static void destroyInstance();
    
    // ==================== 初始化和配置 ====================
    
    // 初始化PSRAM
    bool initialize();
    void cleanup();
    
    // PSRAM状态检查
    PSRAMStatus getStatus() const;
    bool isPSRAMAvailable() const;
    bool isPSRAMInitialized() const;
    
    // 配置管理
    void setConfig(const PSRAMConfig& config);
    const PSRAMConfig& getConfig() const;
    void resetConfig();
    
    // ==================== 内存分配接口 ====================
    
    // 基础分配
    void* allocate(size_t size, const char* tag = nullptr);
    void* allocateAligned(size_t size, size_t alignment, const char* tag = nullptr);
    void* reallocate(void* ptr, size_t newSize);
    void deallocate(void* ptr);
    
    // 批量分配
    bool allocateBatch(const std::vector<size_t>& sizes, std::vector<void*>& pointers, const char* tag = nullptr);
    void deallocateBatch(const std::vector<void*>& pointers);
    
    // 类型化分配
    template<typename T>
    T* allocateArray(size_t count, const char* tag = nullptr);
    
    template<typename T>
    void deallocateArray(T* ptr);
    
    // 字符串分配
    char* allocateString(size_t length, const char* tag = nullptr);
    char* duplicateString(const char* str, const char* tag = nullptr);
    
    // ==================== 内存池管理 ====================
    
    // PSRAM内存池
    class PSRAMPool {
    private:
        void* m_pool;
        size_t m_blockSize;
        size_t m_blockCount;
        std::vector<bool> m_freeBlocks;
        std::mutex m_poolMutex;
        const char* m_tag;
        
    public:
        PSRAMPool(size_t blockSize, size_t blockCount, const char* tag = nullptr);
        ~PSRAMPool();
        
        void* allocate();
        void deallocate(void* ptr);
        bool isFromPool(void* ptr) const;
        size_t getAvailableBlocks() const;
        size_t getTotalBlocks() const;
        size_t getBlockSize() const;
        const char* getTag() const;
    };
    
    // 创建内存池
    std::unique_ptr<PSRAMPool> createPool(size_t blockSize, size_t blockCount, const char* tag = nullptr);
    
    // ==================== 统计和监控 ====================
    
    // 获取统计信息
    const PSRAMStats& getStats() const;
    void updateStats();
    void resetStats();
    
    // 内存使用信息
    size_t getTotalSize() const;
    size_t getUsedSize() const;
    size_t getFreeSize() const;
    size_t getLargestFreeBlock() const;
    float getUsagePercent() const;
    float getFragmentationRatio() const;
    
    // 分配信息
    uint32_t getAllocatedBlockCount() const;
    std::vector<PSRAMBlock> getAllocatedBlocks() const;
    PSRAMBlock* findBlock(void* ptr);
    
    // ==================== 内存优化 ====================
    
    // 碎片整理
    bool defragment();
    bool needsDefragmentation() const;
    void scheduleDefragmentation();
    
    // 内存压缩
    size_t compact();
    void optimizeLayout();
    
    // 缓存管理
    void flushCache();
    void invalidateCache();
    void preloadCache(void* ptr, size_t size);
    
    // ==================== 调试和诊断 ====================
    
    // 内存验证
    bool validateMemoryIntegrity() const;
    bool checkForCorruption() const;
    bool verifyAllocation(void* ptr) const;
    
    // 调试信息
    void printMemoryMap() const;
    void printStatistics() const;
    void printAllocationDetails() const;
    void dumpMemoryLayout() const;
    
    // 内存分析
    struct MemoryAnalysis {
        uint32_t totalBlocks;
        uint32_t freeBlocks;
        uint32_t allocatedBlocks;
        size_t averageBlockSize;
        size_t largestBlock;
        size_t smallestBlock;
        float efficiency;
        
        MemoryAnalysis() : totalBlocks(0), freeBlocks(0), allocatedBlocks(0),
                          averageBlockSize(0), largestBlock(0), smallestBlock(SIZE_MAX), efficiency(0.0f) {}
    };
    
    MemoryAnalysis analyzeMemory() const;
    
    // ==================== 性能监控 ====================
    
    // 性能指标
    struct PerformanceMetrics {
        uint32_t allocTime;         // 平均分配时间（微秒）
        uint32_t deallocTime;       // 平均释放时间（微秒）
        uint32_t defragTime;        // 碎片整理时间（毫秒）
        float throughput;           // 吞吐量（MB/s）
        uint32_t cacheHitRate;      // 缓存命中率（%）
        
        PerformanceMetrics() : allocTime(0), deallocTime(0), defragTime(0),
                              throughput(0.0f), cacheHitRate(0) {}
    };
    
    PerformanceMetrics getPerformanceMetrics() const;
    void resetPerformanceMetrics();
    
    // 性能测试
    bool performBenchmark();
    void measureAllocationSpeed();
    void measureFragmentationImpact();

private:
    // ==================== 内部实现方法 ====================
    
    // 初始化实现
    bool initializePSRAM();
    bool configurePSRAM();
    void setupMemoryLayout();
    
    // 分配策略实现
    void* allocateFirstFit(size_t size);
    void* allocateBestFit(size_t size);
    void* allocateWorstFit(size_t size);
    void* allocateBuddySystem(size_t size);
    
    // 内存管理实现
    void registerBlock(void* ptr, size_t size, const char* tag);
    void unregisterBlock(void* ptr);
    void updateFreeBlocks();
    void mergeFreeBlocks();
    
    // 统计更新
    void updateAllocationStats(size_t size);
    void updateDeallocationStats(size_t size);
    void calculateFragmentation();
    void updatePeakUsage();
    
    // 内存验证实现
    bool isValidPSRAMPointer(void* ptr) const;
    bool isAligned(void* ptr, size_t alignment) const;
    size_t getBlockSize(void* ptr) const;
    
    // 性能监控实现
    mutable PerformanceMetrics m_performanceMetrics;
    void recordAllocationTime(uint32_t time);
    void recordDeallocationTime(uint32_t time);
    
    // 错误处理
    void handleAllocationFailure(size_t size) const;
    void handleCorruption(void* ptr) const;
    void logError(const String& error) const;
    void logDebug(const String& message) const;
    
    // 常量定义
    static constexpr size_t MIN_BLOCK_SIZE = 16;
    static constexpr size_t MAX_BLOCK_SIZE = 1024 * 1024; // 1MB
    static constexpr size_t ALIGNMENT_SIZE = 4;
    static constexpr uint32_t DEFRAG_INTERVAL = 300000; // 5分钟
};

// ==================== 模板实现 ====================

template<typename T>
T* PSRAMManager::allocateArray(size_t count, const char* tag) {
    size_t totalSize = sizeof(T) * count;
    void* ptr = allocate(totalSize, tag);
    return static_cast<T*>(ptr);
}

template<typename T>
void PSRAMManager::deallocateArray(T* ptr) {
    if (ptr) {
        deallocate(ptr);
    }
}

// ==================== 便利宏定义 ====================

#define PSRAM_MALLOC(size) PSRAMManager::getInstance().allocate(size, __FUNCTION__)
#define PSRAM_FREE(ptr) PSRAMManager::getInstance().deallocate(ptr)
#define PSRAM_REALLOC(ptr, size) PSRAMManager::getInstance().reallocate(ptr, size)

#define PSRAM_NEW_ARRAY(type, count) PSRAMManager::getInstance().allocateArray<type>(count, __FUNCTION__)
#define PSRAM_DELETE_ARRAY(ptr) PSRAMManager::getInstance().deallocateArray(ptr)

#define PSRAM_STRDUP(str) PSRAMManager::getInstance().duplicateString(str, __FUNCTION__)

// 调试版本的宏
#ifdef DEBUG_PSRAM
#define DEBUG_PSRAM_MALLOC(size) PSRAMManager::getInstance().allocate(size, __FILE__ ":" STRINGIFY(__LINE__))
#define DEBUG_PSRAM_FREE(ptr) PSRAMManager::getInstance().deallocate(ptr)
#else
#define DEBUG_PSRAM_MALLOC(size) PSRAM_MALLOC(size)
#define DEBUG_PSRAM_FREE(ptr) PSRAM_FREE(ptr)
#endif
