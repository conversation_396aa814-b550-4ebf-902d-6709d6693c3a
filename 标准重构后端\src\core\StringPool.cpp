#include "StringPool.h"
#include <cstdarg>
#include <algorithm>

/**
 * ESP32-S3 红外控制系统 - 字符串池实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第六部分：内存管理最佳实践
 */

// 静态成员初始化
StringPool* StringPool::s_instance = nullptr;
std::mutex StringPool::s_mutex;

// 常用字符串常量
namespace PooledStrings {
    const String EMPTY = "";
    const String TRUE_STR = "true";
    const String FALSE_STR = "false";
    const String NULL_STR = "null";
    const String OK = "ok";
    const String ERROR = "error";
    const String SUCCESS = "success";
    const String FAILED = "failed";
}

// StringRef实现
StringPool::StringRef::StringRef(const char* str, size_t len, bool permanent) 
    : length(len), refCount(1), isPermanent(permanent), timestamp(millis()) {
    
    if (str && len > 0) {
        data = new char[len + 1];
        memcpy(data, str, len);
        data[len] = '\0';
        
        // 计算哈希值
        hash = 0;
        for (size_t i = 0; i < len; i++) {
            hash = hash * 31 + static_cast<uint8_t>(data[i]);
        }
    } else {
        data = nullptr;
        hash = 0;
    }
}

StringPool::StringRef::~StringRef() {
    if (data) {
        delete[] data;
        data = nullptr;
    }
}

StringPool::StringPool() : m_lastCleanupTime(0) {
}

StringPool::~StringPool() {
    cleanup();
}

// ==================== 单例模式实现 ====================

StringPool& StringPool::getInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (!s_instance) {
        s_instance = new StringPool();
    }
    return *s_instance;
}

void StringPool::destroyInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

// ==================== 初始化和配置实现 ====================

bool StringPool::initialize() {
    Serial.println("[StringPool] Initializing string pool...");
    
    // 重置统计信息
    resetStats();
    
    // 设置最后清理时间
    m_lastCleanupTime = millis();
    
    // 添加常用字符串
    addPermanentString("");
    addPermanentString("true");
    addPermanentString("false");
    addPermanentString("null");
    addPermanentString("ok");
    addPermanentString("error");
    addPermanentString("success");
    addPermanentString("failed");
    
    Serial.printf("[StringPool] String pool initialized with %u permanent strings\n", m_permanentStrings.size());
    return true;
}

void StringPool::cleanup() {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    // 清理所有字符串引用
    m_stringMap.clear();
    m_stringIndex.clear();
    m_permanentStrings.clear();
    
    // 重置统计信息
    resetStats();
    
    Serial.println("[StringPool] String pool cleaned up");
}

void StringPool::setConfig(const PoolConfig& config) {
    m_config = config;
}

const StringPool::PoolConfig& StringPool::getConfig() const {
    return m_config;
}

void StringPool::resetConfig() {
    m_config = PoolConfig();
}

// ==================== 字符串管理接口实现 ====================

String StringPool::getString(const char* str) {
    if (!str) {
        recordMiss();
        return String();
    }
    
    auto ref = getStringRef(str);
    if (ref) {
        recordHit();
        return String(ref->data);
    }
    
    recordMiss();
    return String(str);
}

String StringPool::getString(const String& str) {
    return getString(str.c_str());
}

String StringPool::getStringCopy(const char* str) {
    // 总是返回新的副本，不使用池
    recordMiss();
    return String(str);
}

std::shared_ptr<StringPool::StringRef> StringPool::getStringRef(const char* str) {
    if (!str || !isValidString(str)) {
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(s_mutex);
    
    size_t length = strlen(str);
    uint32_t hash = calculateHash(str, length);
    
    // 查找现有字符串
    auto it = m_stringMap.find(hash);
    if (it != m_stringMap.end()) {
        auto ref = it->second;
        if (ref && ref->data && strcmp(ref->data, str) == 0) {
            ref->refCount++;
            updateAccessTime(ref);
            return ref;
        }
    }
    
    // 创建新的字符串引用
    auto ref = createStringRef(str, length);
    if (ref) {
        m_stringMap[hash] = ref;
        m_stringIndex[String(str)] = ref;
        updateStats();
    }
    
    return ref;
}

std::shared_ptr<StringPool::StringRef> StringPool::getStringRef(const String& str) {
    return getStringRef(str.c_str());
}

void StringPool::releaseStringRef(std::shared_ptr<StringRef> ref) {
    if (!ref) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(s_mutex);
    
    if (ref->refCount > 0) {
        ref->refCount--;
    }
    
    // 如果引用计数为0且不是永久字符串，可以考虑移除
    if (ref->refCount == 0 && !ref->isPermanent) {
        // 在自动清理时移除
    }
}

String StringPool::addPermanentString(const char* str) {
    if (!str) {
        return String();
    }
    
    auto ref = getStringRef(str);
    if (ref) {
        ref->isPermanent = true;
        
        String strObj(str);
        if (std::find(m_permanentStrings.begin(), m_permanentStrings.end(), strObj) == m_permanentStrings.end()) {
            m_permanentStrings.push_back(strObj);
        }
        
        return strObj;
    }
    
    return String(str);
}

String StringPool::addPermanentString(const String& str) {
    return addPermanentString(str.c_str());
}

bool StringPool::isPermanentString(const String& str) const {
    return std::find(m_permanentStrings.begin(), m_permanentStrings.end(), str) != m_permanentStrings.end();
}

void StringPool::removePermanentString(const String& str) {
    auto it = std::find(m_permanentStrings.begin(), m_permanentStrings.end(), str);
    if (it != m_permanentStrings.end()) {
        m_permanentStrings.erase(it);
        
        // 更新字符串引用的永久标志
        auto ref = find(str);
        if (ref) {
            ref->isPermanent = false;
        }
    }
}

// ==================== 字符串操作实现 ====================

bool StringPool::contains(const String& str) const {
    return find(str) != nullptr;
}

bool StringPool::contains(const char* str) const {
    return find(str) != nullptr;
}

std::shared_ptr<StringPool::StringRef> StringPool::find(const String& str) const {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    auto it = m_stringIndex.find(str);
    if (it != m_stringIndex.end()) {
        return it->second;
    }
    
    return nullptr;
}

std::shared_ptr<StringPool::StringRef> StringPool::find(const char* str) const {
    if (!str) {
        return nullptr;
    }
    
    return find(String(str));
}

bool StringPool::equals(const String& str1, const String& str2) const {
    // 如果两个字符串都在池中且指向同一个引用，则相等
    auto ref1 = find(str1);
    auto ref2 = find(str2);
    
    if (ref1 && ref2 && ref1 == ref2) {
        return true;
    }
    
    return str1.equals(str2);
}

int StringPool::compare(const String& str1, const String& str2) const {
    return str1.compareTo(str2);
}

String StringPool::format(const char* format, ...) {
    if (!format) {
        return String();
    }
    
    va_list args;
    va_start(args, format);
    String result = formatV(format, args);
    va_end(args);
    
    return result;
}

String StringPool::formatV(const char* format, va_list args) {
    if (!format) {
        return String();
    }
    
    // 简化实现：使用固定大小的缓冲区
    char buffer[512];
    vsnprintf(buffer, sizeof(buffer), format, args);
    
    return getString(buffer);
}

String StringPool::concat(const String& str1, const String& str2) {
    String result = str1 + str2;
    return getString(result.c_str());
}

String StringPool::concat(const std::vector<String>& strings, const String& separator) {
    if (strings.empty()) {
        return String();
    }
    
    String result = strings[0];
    for (size_t i = 1; i < strings.size(); i++) {
        result += separator + strings[i];
    }
    
    return getString(result.c_str());
}

// ==================== 批量操作实现 ====================

std::vector<String> StringPool::addStrings(const std::vector<const char*>& strings) {
    std::vector<String> result;
    result.reserve(strings.size());
    
    for (const char* str : strings) {
        result.push_back(getString(str));
    }
    
    return result;
}

std::vector<String> StringPool::addStrings(const std::vector<String>& strings) {
    std::vector<String> result;
    result.reserve(strings.size());
    
    for (const String& str : strings) {
        result.push_back(getString(str));
    }
    
    return result;
}

std::vector<std::shared_ptr<StringPool::StringRef>> StringPool::findStrings(const std::vector<String>& strings) const {
    std::vector<std::shared_ptr<StringRef>> result;
    result.reserve(strings.size());
    
    for (const String& str : strings) {
        result.push_back(find(str));
    }
    
    return result;
}

void StringPool::releaseStrings(const std::vector<std::shared_ptr<StringRef>>& refs) {
    for (auto ref : refs) {
        releaseStringRef(ref);
    }
}

// ==================== 内存管理实现 ====================

size_t StringPool::getTotalMemoryUsage() const {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    size_t totalMemory = 0;
    
    for (const auto& pair : m_stringMap) {
        auto ref = pair.second;
        if (ref && ref->data) {
            totalMemory += ref->length + 1; // +1 for null terminator
            totalMemory += sizeof(StringRef);
        }
    }
    
    return totalMemory;
}

size_t StringPool::getStringCount() const {
    return m_stringMap.size();
}

size_t StringPool::getUniqueStringCount() const {
    return m_stringIndex.size();
}

float StringPool::getMemoryEfficiency() const {
    if (m_stats.totalMemory == 0) {
        return 0.0f;
    }
    
    return (float)m_stats.savedMemory / m_stats.totalMemory * 100.0f;
}

void StringPool::compactPool() {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    // 移除未使用的字符串
    removeUnusedStrings();
    
    // 更新统计信息
    updateStats();
    
    logPoolEvent("COMPACT", "Pool compacted");
}

void StringPool::defragmentPool() {
    // 简化实现：字符串池不需要碎片整理
    compactPool();
}

size_t StringPool::removeUnusedStrings() {
    size_t removedCount = 0;
    
    for (auto it = m_stringMap.begin(); it != m_stringMap.end();) {
        auto ref = it->second;
        if (ref && ref->refCount == 0 && !ref->isPermanent) {
            // 从索引中移除
            if (ref->data) {
                m_stringIndex.erase(String(ref->data));
            }
            
            it = m_stringMap.erase(it);
            removedCount++;
        } else {
            ++it;
        }
    }
    
    return removedCount;
}

size_t StringPool::removeExpiredStrings() {
    size_t removedCount = 0;
    uint32_t currentTime = millis();
    
    for (auto it = m_stringMap.begin(); it != m_stringMap.end();) {
        auto ref = it->second;
        if (ref && !ref->isPermanent && 
            (currentTime - ref->timestamp) > m_config.maxIdleTime) {
            
            // 从索引中移除
            if (ref->data) {
                m_stringIndex.erase(String(ref->data));
            }
            
            it = m_stringMap.erase(it);
            removedCount++;
        } else {
            ++it;
        }
    }
    
    return removedCount;
}

void StringPool::performAutoCleanup() {
    if (!m_config.enableAutoCleanup) {
        return;
    }
    
    uint32_t currentTime = millis();
    if ((currentTime - m_lastCleanupTime) >= m_config.cleanupInterval) {
        size_t removedUnused = removeUnusedStrings();
        size_t removedExpired = removeExpiredStrings();
        
        m_lastCleanupTime = currentTime;
        
        if (removedUnused > 0 || removedExpired > 0) {
            logPoolEvent("AUTO_CLEANUP", 
                        "Removed " + String(removedUnused) + " unused, " + 
                        String(removedExpired) + " expired strings");
        }
    }
}

void StringPool::scheduleCleanup() {
    if (needsCleanup()) {
        performAutoCleanup();
    }
}

bool StringPool::needsCleanup() const {
    uint32_t currentTime = millis();
    return (currentTime - m_lastCleanupTime) >= m_config.cleanupInterval;
}

// ==================== 统计和监控实现 ====================

const StringPool::PoolStats& StringPool::getStats() const {
    return m_stats;
}

void StringPool::updateStats() {
    std::lock_guard<std::mutex> lock(s_mutex);

    m_stats.totalStrings = m_stringMap.size();
    m_stats.uniqueStrings = m_stringIndex.size();
    m_stats.totalMemory = getTotalMemoryUsage();

    // 计算重复字符串数量
    m_stats.duplicateCount = 0;
    for (const auto& pair : m_stringMap) {
        auto ref = pair.second;
        if (ref && ref->refCount > 1) {
            m_stats.duplicateCount += (ref->refCount - 1);
        }
    }

    // 计算节省的内存
    m_stats.savedMemory = 0;
    for (const auto& pair : m_stringMap) {
        auto ref = pair.second;
        if (ref && ref->refCount > 1) {
            m_stats.savedMemory += (ref->refCount - 1) * (ref->length + 1);
        }
    }

    // 计算命中率
    uint32_t totalAccess = m_stats.hitCount + m_stats.missCount;
    if (totalAccess > 0) {
        m_stats.hitRatio = (float)m_stats.hitCount / totalAccess * 100.0f;
    } else {
        m_stats.hitRatio = 0.0f;
    }
}

void StringPool::resetStats() {
    std::lock_guard<std::mutex> lock(s_mutex);
    memset(&m_stats, 0, sizeof(m_stats));
}

void StringPool::recordHit() {
    m_stats.hitCount++;
}

void StringPool::recordMiss() {
    m_stats.missCount++;
}

float StringPool::getHitRatio() const {
    uint32_t totalAccess = m_stats.hitCount + m_stats.missCount;
    if (totalAccess > 0) {
        return (float)m_stats.hitCount / totalAccess * 100.0f;
    }
    return 0.0f;
}

StringPool::UsageAnalysis StringPool::analyzeUsage() const {
    std::lock_guard<std::mutex> lock(s_mutex);

    UsageAnalysis analysis;

    if (m_stringMap.empty()) {
        return analysis;
    }

    uint32_t totalRefCount = 0;
    size_t totalLength = 0;

    for (const auto& pair : m_stringMap) {
        auto ref = pair.second;
        if (!ref) continue;

        totalRefCount += ref->refCount;
        totalLength += ref->length;

        // 查找最多使用的字符串
        if (ref->refCount > analysis.mostUsedStringCount) {
            analysis.mostUsedStringCount = ref->refCount;
            analysis.mostUsedString = ref->data ? String(ref->data) : "";
        }

        // 查找最少使用的字符串
        if (ref->refCount < analysis.leastUsedStringCount) {
            analysis.leastUsedStringCount = ref->refCount;
            analysis.leastUsedString = ref->data ? String(ref->data) : "";
        }
    }

    // 计算平均值
    if (m_stringMap.size() > 0) {
        analysis.averageRefCount = (float)totalRefCount / m_stringMap.size();
        analysis.averageStringLength = totalLength / m_stringMap.size();
    }

    return analysis;
}

// ==================== 调试和诊断实现 ====================

bool StringPool::validatePoolIntegrity() const {
    std::lock_guard<std::mutex> lock(s_mutex);

    // 检查映射一致性
    for (const auto& pair : m_stringMap) {
        auto ref = pair.second;
        if (!ref || !ref->data) {
            return false;
        }

        // 检查哈希值是否正确
        uint32_t calculatedHash = calculateHash(ref->data, ref->length);
        if (calculatedHash != pair.first) {
            return false;
        }

        // 检查索引映射是否存在
        String str(ref->data);
        auto indexIt = m_stringIndex.find(str);
        if (indexIt == m_stringIndex.end() || indexIt->second != ref) {
            return false;
        }
    }

    return true;
}

bool StringPool::checkForMemoryLeaks() const {
    // 简化实现：检查是否有引用计数为0但仍在池中的字符串
    for (const auto& pair : m_stringMap) {
        auto ref = pair.second;
        if (ref && ref->refCount == 0 && !ref->isPermanent) {
            return true; // 发现潜在泄漏
        }
    }

    return false;
}

void StringPool::printPoolStatus() const {
    Serial.println("=== String Pool Status ===");
    Serial.printf("Total strings: %u\n", m_stats.totalStrings);
    Serial.printf("Unique strings: %u\n", m_stats.uniqueStrings);
    Serial.printf("Duplicate count: %u\n", m_stats.duplicateCount);
    Serial.printf("Total memory: %u bytes\n", m_stats.totalMemory);
    Serial.printf("Saved memory: %u bytes\n", m_stats.savedMemory);
    Serial.printf("Memory efficiency: %.1f%%\n", getMemoryEfficiency());
    Serial.printf("Permanent strings: %u\n", m_permanentStrings.size());
    Serial.println("==========================");
}

void StringPool::printStatistics() const {
    Serial.println("=== String Pool Statistics ===");
    Serial.printf("Cache hits: %u\n", m_stats.hitCount);
    Serial.printf("Cache misses: %u\n", m_stats.missCount);
    Serial.printf("Hit ratio: %.1f%%\n", m_stats.hitRatio);

    auto analysis = analyzeUsage();
    Serial.printf("Most used string: \"%s\" (%u refs)\n",
                 analysis.mostUsedString.c_str(), analysis.mostUsedStringCount);
    Serial.printf("Least used string: \"%s\" (%u refs)\n",
                 analysis.leastUsedString.c_str(), analysis.leastUsedStringCount);
    Serial.printf("Average ref count: %.1f\n", analysis.averageRefCount);
    Serial.printf("Average string length: %u\n", analysis.averageStringLength);
    Serial.println("===============================");
}

void StringPool::printStringList() const {
    Serial.println("=== String Pool Contents ===");

    for (const auto& pair : m_stringIndex) {
        auto ref = pair.second;
        if (ref) {
            Serial.printf("\"%s\" - refs: %u, len: %u, perm: %s\n",
                         pair.first.c_str(), ref->refCount, ref->length,
                         ref->isPermanent ? "yes" : "no");
        }
    }

    Serial.println("============================");
}

void StringPool::dumpPoolContents() const {
    printPoolStatus();
    printStatistics();
    printStringList();
}

// ==================== 内部实现方法 ====================

uint32_t StringPool::calculateHash(const char* str, size_t length) const {
    if (!str || length == 0) {
        return 0;
    }

    uint32_t hash = HASH_SEED;
    for (size_t i = 0; i < length; i++) {
        hash = hash * 31 + static_cast<uint8_t>(str[i]);
    }

    return hash;
}

uint32_t StringPool::calculateHash(const String& str) const {
    return calculateHash(str.c_str(), str.length());
}

std::shared_ptr<StringPool::StringRef> StringPool::createStringRef(const char* str, size_t length, bool permanent) {
    if (!str || length == 0 || length > m_config.maxStringLength) {
        return nullptr;
    }

    try {
        auto ref = std::make_shared<StringRef>(str, length, permanent);
        return ref;
    } catch (const std::bad_alloc&) {
        handleMemoryError("createStringRef");
        return nullptr;
    }
}

void StringPool::destroyStringRef(std::shared_ptr<StringRef> ref) {
    // shared_ptr会自动管理内存
}

char* StringPool::allocateStringMemory(size_t length) {
    try {
        return new char[length + 1];
    } catch (const std::bad_alloc&) {
        handleMemoryError("allocateStringMemory");
        return nullptr;
    }
}

void StringPool::deallocateStringMemory(char* ptr) {
    if (ptr) {
        delete[] ptr;
    }
}

void StringPool::maintainPool() {
    if (m_config.enableAutoCleanup) {
        performAutoCleanup();
    }

    updateStats();
}

void StringPool::removeExpiredEntries() {
    removeExpiredStrings();
}

void StringPool::updateAccessTime(std::shared_ptr<StringRef> ref) {
    if (ref) {
        ref->timestamp = millis();
    }
}

void StringPool::updateMemoryStats() {
    m_stats.totalMemory = getTotalMemoryUsage();
}

void StringPool::updateUsageStats() {
    // 在updateStats中实现
}

void StringPool::calculateEfficiency() {
    updateStats();
}

bool StringPool::isValidString(const char* str) const {
    if (!str) {
        return false;
    }

    size_t length = strlen(str);
    return length <= m_config.maxStringLength;
}

bool StringPool::isPoolFull() const {
    return getTotalMemoryUsage() >= m_config.maxPoolSize;
}

bool StringPool::shouldCompress(const String& str) const {
    return m_config.enableCompression && str.length() > 64;
}

void StringPool::handlePoolOverflow() {
    logPoolEvent("OVERFLOW", "Pool size limit reached");

    // 尝试清理
    performAutoCleanup();
}

void StringPool::handleMemoryError(const String& operation) {
    logPoolEvent("MEMORY_ERROR", "Memory allocation failed in " + operation);
}

void StringPool::logPoolEvent(const String& event, const String& details) {
    Serial.printf("[StringPool] %s", event.c_str());
    if (!details.isEmpty()) {
        Serial.printf(": %s", details.c_str());
    }
    Serial.println();
}

// ==================== 简化的其他方法实现 ====================

std::vector<String> StringPool::searchStrings(const String& pattern) const {
    std::vector<String> results;

    for (const auto& pair : m_stringIndex) {
        if (pair.first.indexOf(pattern) >= 0) {
            results.push_back(pair.first);
        }
    }

    return results;
}

std::vector<String> StringPool::filterStringsByLength(size_t minLength, size_t maxLength) const {
    std::vector<String> results;

    for (const auto& pair : m_stringIndex) {
        size_t length = pair.first.length();
        if (length >= minLength && length <= maxLength) {
            results.push_back(pair.first);
        }
    }

    return results;
}

std::vector<String> StringPool::filterStringsByRefCount(uint32_t minRefCount) const {
    std::vector<String> results;

    for (const auto& pair : m_stringIndex) {
        auto ref = pair.second;
        if (ref && ref->refCount >= minRefCount) {
            results.push_back(pair.first);
        }
    }

    return results;
}

String StringPool::compressString(const String& str) const {
    // 简化实现：不进行实际压缩
    return str;
}

String StringPool::decompressString(const String& compressedStr) const {
    // 简化实现：不进行实际解压缩
    return compressedStr;
}

bool StringPool::isStringCompressed(const String& str) const {
    return false; // 简化实现
}

String StringPool::encryptString(const String& str, const String& key) const {
    // 简化实现：不进行实际加密
    return str;
}

String StringPool::decryptString(const String& encryptedStr, const String& key) const {
    // 简化实现：不进行实际解密
    return encryptedStr;
}

// StringTemplate实现
StringPool::StringTemplate::StringTemplate(const String& templateStr) : m_template(templateStr) {
}

void StringPool::StringTemplate::setVariable(const String& name, const String& value) {
    m_variables[name] = value;
}

void StringPool::StringTemplate::setVariable(const String& name, int value) {
    m_variables[name] = String(value);
}

void StringPool::StringTemplate::setVariable(const String& name, float value) {
    m_variables[name] = String(value);
}

String StringPool::StringTemplate::render() const {
    String result = m_template;

    for (const auto& pair : m_variables) {
        String placeholder = "${" + pair.first + "}";
        result.replace(placeholder, pair.second);
    }

    return result;
}

void StringPool::StringTemplate::clear() {
    m_variables.clear();
}

std::unique_ptr<StringPool::StringTemplate> StringPool::createTemplate(const String& templateStr) {
    return std::make_unique<StringTemplate>(templateStr);
}

bool StringPool::exportPool(const String& filePath) const {
    return true; // 简化实现
}

bool StringPool::importPool(const String& filePath) {
    return true; // 简化实现
}

bool StringPool::savePoolState(const String& filePath) const {
    return exportPool(filePath);
}

bool StringPool::loadPoolState(const String& filePath) {
    return importPool(filePath);
}

String StringPool::exportToJSON() const {
    return "{}"; // 简化实现
}

bool StringPool::importFromJSON(const String& jsonData) {
    return true; // 简化实现
}
