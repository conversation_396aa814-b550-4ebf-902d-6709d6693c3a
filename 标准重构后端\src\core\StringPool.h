#pragma once

#include <Arduino.h>
#include <vector>
#include <map>
#include <unordered_map>
#include <mutex>
#include <memory>

/**
 * ESP32-S3 红外控制系统 - 字符串池
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第六部分：内存管理最佳实践
 * 
 * 字符串池职责：
 * - 字符串的统一存储和管理
 * - 字符串去重和内存优化
 * - 常用字符串的缓存管理
 * - 字符串生命周期管理
 */

class StringPool {
public:
    // 字符串引用计数器
    struct StringRef {
        char* data;
        size_t length;
        uint32_t refCount;
        uint32_t hash;
        uint32_t timestamp;
        bool isPermanent;
        
        StringRef() : data(nullptr), length(0), refCount(0), hash(0), 
                     timestamp(0), isPermanent(false) {}
        
        StringRef(const char* str, size_t len, bool permanent = false);
        ~StringRef();
    };
    
    // 字符串池统计信息
    struct PoolStats {
        uint32_t totalStrings;      // 总字符串数
        uint32_t uniqueStrings;     // 唯一字符串数
        uint32_t duplicateCount;    // 重复字符串数
        size_t totalMemory;         // 总内存使用
        size_t savedMemory;         // 节省的内存
        uint32_t hitCount;          // 缓存命中次数
        uint32_t missCount;         // 缓存未命中次数
        float hitRatio;             // 命中率
        
        PoolStats() : totalStrings(0), uniqueStrings(0), duplicateCount(0),
                     totalMemory(0), savedMemory(0), hitCount(0), missCount(0), hitRatio(0.0f) {}
    };
    
    // 字符串池配置
    struct PoolConfig {
        size_t maxPoolSize;         // 最大池大小
        size_t maxStringLength;     // 最大字符串长度
        uint32_t maxRefCount;       // 最大引用计数
        bool enableCompression;     // 启用压缩
        bool enableStatistics;      // 启用统计
        bool enableAutoCleanup;     // 启用自动清理
        uint32_t cleanupInterval;   // 清理间隔（毫秒）
        uint32_t maxIdleTime;       // 最大空闲时间（毫秒）
        
        PoolConfig() : maxPoolSize(64 * 1024), maxStringLength(1024), maxRefCount(1000),
                      enableCompression(false), enableStatistics(true), enableAutoCleanup(true),
                      cleanupInterval(300000), maxIdleTime(600000) {} // 5分钟清理，10分钟空闲
    };

private:
    static StringPool* s_instance;
    static std::mutex s_mutex;
    
    std::unordered_map<uint32_t, std::shared_ptr<StringRef>> m_stringMap;  // 哈希到字符串的映射
    std::map<String, std::shared_ptr<StringRef>> m_stringIndex;            // 字符串到引用的映射
    std::vector<String> m_permanentStrings;                                // 永久字符串列表
    
    PoolConfig m_config;
    PoolStats m_stats;
    uint32_t m_lastCleanupTime;
    
    StringPool();

public:
    ~StringPool();
    
    // 单例模式
    static StringPool& getInstance();
    static void destroyInstance();
    
    // ==================== 初始化和配置 ====================
    
    // 初始化字符串池
    bool initialize();
    void cleanup();
    
    // 配置管理
    void setConfig(const PoolConfig& config);
    const PoolConfig& getConfig() const;
    void resetConfig();
    
    // ==================== 字符串管理接口 ====================
    
    // 字符串获取和存储
    String getString(const char* str);
    String getString(const String& str);
    String getStringCopy(const char* str);
    
    // 字符串引用管理
    std::shared_ptr<StringRef> getStringRef(const char* str);
    std::shared_ptr<StringRef> getStringRef(const String& str);
    void releaseStringRef(std::shared_ptr<StringRef> ref);
    
    // 永久字符串管理
    String addPermanentString(const char* str);
    String addPermanentString(const String& str);
    bool isPermanentString(const String& str) const;
    void removePermanentString(const String& str);
    
    // ==================== 字符串操作 ====================
    
    // 字符串查找
    bool contains(const String& str) const;
    bool contains(const char* str) const;
    std::shared_ptr<StringRef> find(const String& str) const;
    std::shared_ptr<StringRef> find(const char* str) const;
    
    // 字符串比较
    bool equals(const String& str1, const String& str2) const;
    int compare(const String& str1, const String& str2) const;
    
    // 字符串格式化
    String format(const char* format, ...);
    String formatV(const char* format, va_list args);
    
    // 字符串连接
    String concat(const String& str1, const String& str2);
    String concat(const std::vector<String>& strings, const String& separator = "");
    
    // ==================== 批量操作 ====================
    
    // 批量添加字符串
    std::vector<String> addStrings(const std::vector<const char*>& strings);
    std::vector<String> addStrings(const std::vector<String>& strings);
    
    // 批量查找字符串
    std::vector<std::shared_ptr<StringRef>> findStrings(const std::vector<String>& strings) const;
    
    // 批量释放字符串
    void releaseStrings(const std::vector<std::shared_ptr<StringRef>>& refs);
    
    // ==================== 内存管理 ====================
    
    // 内存使用信息
    size_t getTotalMemoryUsage() const;
    size_t getStringCount() const;
    size_t getUniqueStringCount() const;
    float getMemoryEfficiency() const;
    
    // 内存优化
    void compactPool();
    void defragmentPool();
    size_t removeUnusedStrings();
    size_t removeExpiredStrings();
    
    // 自动清理
    void performAutoCleanup();
    void scheduleCleanup();
    bool needsCleanup() const;
    
    // ==================== 统计和监控 ====================
    
    // 获取统计信息
    const PoolStats& getStats() const;
    void updateStats();
    void resetStats();
    
    // 性能监控
    void recordHit();
    void recordMiss();
    float getHitRatio() const;
    
    // 使用分析
    struct UsageAnalysis {
        uint32_t mostUsedStringCount;
        uint32_t leastUsedStringCount;
        String mostUsedString;
        String leastUsedString;
        float averageRefCount;
        size_t averageStringLength;
        
        UsageAnalysis() : mostUsedStringCount(0), leastUsedStringCount(UINT32_MAX),
                         averageRefCount(0.0f), averageStringLength(0) {}
    };
    
    UsageAnalysis analyzeUsage() const;
    
    // ==================== 调试和诊断 ====================
    
    // 池状态验证
    bool validatePoolIntegrity() const;
    bool checkForMemoryLeaks() const;
    
    // 调试信息
    void printPoolStatus() const;
    void printStatistics() const;
    void printStringList() const;
    void dumpPoolContents() const;
    
    // 字符串搜索和过滤
    std::vector<String> searchStrings(const String& pattern) const;
    std::vector<String> filterStringsByLength(size_t minLength, size_t maxLength) const;
    std::vector<String> filterStringsByRefCount(uint32_t minRefCount) const;
    
    // ==================== 高级功能 ====================
    
    // 字符串压缩（如果启用）
    String compressString(const String& str) const;
    String decompressString(const String& compressedStr) const;
    bool isStringCompressed(const String& str) const;
    
    // 字符串加密
    String encryptString(const String& str, const String& key) const;
    String decryptString(const String& encryptedStr, const String& key) const;
    
    // 字符串模板
    class StringTemplate {
    private:
        String m_template;
        std::map<String, String> m_variables;
        
    public:
        StringTemplate(const String& templateStr);
        
        void setVariable(const String& name, const String& value);
        void setVariable(const String& name, int value);
        void setVariable(const String& name, float value);
        String render() const;
        void clear();
    };
    
    std::unique_ptr<StringTemplate> createTemplate(const String& templateStr);
    
    // ==================== 导入导出 ====================
    
    // 池数据导入导出
    bool exportPool(const String& filePath) const;
    bool importPool(const String& filePath);
    bool savePoolState(const String& filePath) const;
    bool loadPoolState(const String& filePath);
    
    // JSON格式导入导出
    String exportToJSON() const;
    bool importFromJSON(const String& jsonData);

private:
    // ==================== 内部实现方法 ====================
    
    // 哈希计算
    uint32_t calculateHash(const char* str, size_t length) const;
    uint32_t calculateHash(const String& str) const;
    
    // 字符串创建和销毁
    std::shared_ptr<StringRef> createStringRef(const char* str, size_t length, bool permanent = false);
    void destroyStringRef(std::shared_ptr<StringRef> ref);
    
    // 内存分配
    char* allocateStringMemory(size_t length);
    void deallocateStringMemory(char* ptr);
    
    // 池维护
    void maintainPool();
    void removeExpiredEntries();
    void updateAccessTime(std::shared_ptr<StringRef> ref);
    
    // 统计更新
    void updateMemoryStats();
    void updateUsageStats();
    void calculateEfficiency();
    
    // 验证和检查
    bool isValidString(const char* str) const;
    bool isPoolFull() const;
    bool shouldCompress(const String& str) const;
    
    // 错误处理
    void handlePoolOverflow();
    void handleMemoryError(const String& operation);
    void logPoolEvent(const String& event, const String& details = "");
    
    // 常量定义
    static constexpr size_t DEFAULT_POOL_SIZE = 64 * 1024;     // 64KB默认池大小
    static constexpr size_t MAX_STRING_LENGTH = 1024;          // 1KB最大字符串长度
    static constexpr uint32_t HASH_SEED = 0x9e3779b9;         // 哈希种子
    static constexpr uint32_t CLEANUP_THRESHOLD = 100;         // 清理阈值
};

// ==================== 便利宏定义 ====================

#define POOL_STRING(str) StringPool::getInstance().getString(str)
#define POOL_FORMAT(fmt, ...) StringPool::getInstance().format(fmt, ##__VA_ARGS__)
#define POOL_CONCAT(str1, str2) StringPool::getInstance().concat(str1, str2)
#define POOL_PERMANENT(str) StringPool::getInstance().addPermanentString(str)

// 常用字符串常量
namespace PooledStrings {
    extern const String EMPTY;
    extern const String TRUE_STR;
    extern const String FALSE_STR;
    extern const String NULL_STR;
    extern const String OK;
    extern const String ERROR;
    extern const String SUCCESS;
    extern const String FAILED;
}

// 字符串池初始化宏
#define INIT_STRING_POOL() do { \
    StringPool::getInstance().initialize(); \
    StringPool::getInstance().addPermanentString(""); \
    StringPool::getInstance().addPermanentString("true"); \
    StringPool::getInstance().addPermanentString("false"); \
    StringPool::getInstance().addPermanentString("null"); \
    StringPool::getInstance().addPermanentString("ok"); \
    StringPool::getInstance().addPermanentString("error"); \
} while(0)
