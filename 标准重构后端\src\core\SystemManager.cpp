#include "SystemManager.h"
#include <esp_system.h>
#include <esp_task_wdt.h>
#include <esp_heap_caps.h>
#include <algorithm>

/**
 * ESP32-S3 红外控制系统 - 系统管理器实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第六部分：系统管理最佳实践
 */

// 静态成员初始化
SystemManager* SystemManager::s_instance = nullptr;
std::mutex SystemManager::s_mutex;

SystemManager::SystemManager() 
    : m_currentState(SystemState::UNINITIALIZED), m_startupTime(0),
      m_lastHealthCheck(0), m_lastResourceUpdate(0), m_watchdogEnabled(false), m_lastWatchdogFeed(0) {
}

SystemManager::~SystemManager() {
    cleanup();
}

// ==================== 单例模式实现 ====================

SystemManager& SystemManager::getInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (!s_instance) {
        s_instance = new SystemManager();
    }
    return *s_instance;
}

void SystemManager::destroyInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

// ==================== 系统生命周期管理实现 ====================

bool SystemManager::initialize() {
    Serial.println("[SystemManager] Initializing system manager...");
    
    setSystemState(SystemState::INITIALIZING);
    
    // 初始化核心组件
    if (!initializeCore()) {
        Serial.println("[SystemManager] Failed to initialize core components");
        setSystemState(SystemState::ERROR);
        return false;
    }
    
    // 设置事件系统
    setupEventSystem();
    
    // 设置看门狗
    if (m_config.enableWatchdog) {
        setupWatchdog();
    }
    
    // 记录启动时间
    m_startupTime = millis();
    
    // 发布系统启动事件
    publishEvent(SystemEventType::SYSTEM_STARTUP, "System manager initialized", "", 0);
    
    Serial.println("[SystemManager] System manager initialized successfully");
    return true;
}

void SystemManager::cleanup() {
    Serial.println("[SystemManager] Cleaning up system manager...");
    
    setSystemState(SystemState::SHUTTING_DOWN);
    
    // 停止所有服务
    for (auto& serviceInfo : m_services) {
        if (serviceInfo.isInitialized) {
            stopServiceInternal(serviceInfo);
        }
    }
    
    // 禁用看门狗
    if (m_watchdogEnabled) {
        disableWatchdog();
    }
    
    // 清理资源
    m_services.clear();
    m_eventHistory.clear();
    m_eventCallbacks.clear();
    
    setSystemState(SystemState::SHUTDOWN);
    
    Serial.println("[SystemManager] System manager cleaned up");
}

bool SystemManager::startup() {
    if (m_currentState != SystemState::UNINITIALIZED && m_currentState != SystemState::SHUTDOWN) {
        Serial.println("[SystemManager] System already started or in invalid state");
        return false;
    }
    
    Serial.println("[SystemManager] Starting system...");
    
    // 初始化系统管理器
    if (!initialize()) {
        return false;
    }
    
    // 初始化所有注册的服务
    if (!initializeServices()) {
        Serial.println("[SystemManager] Failed to initialize services");
        setSystemState(SystemState::ERROR);
        return false;
    }
    
    // 更新系统资源信息
    updateSystemResources();
    
    // 执行初始健康检查
    performHealthCheck();
    
    setSystemState(SystemState::RUNNING);
    
    publishEvent(SystemEventType::SYSTEM_STARTUP, "System startup completed", "", 0);
    
    Serial.println("[SystemManager] System started successfully");
    return true;
}

void SystemManager::shutdown() {
    Serial.println("[SystemManager] Shutting down system...");
    
    publishEvent(SystemEventType::SYSTEM_SHUTDOWN, "System shutdown initiated", "", 1);
    
    cleanup();
    
    Serial.println("[SystemManager] System shutdown completed");
}

void SystemManager::restart() {
    Serial.println("[SystemManager] Restarting system...");
    
    publishEvent(SystemEventType::SYSTEM_SHUTDOWN, "System restart initiated", "", 1);
    
    // 延迟以确保日志输出
    delay(1000);
    
    // 重启ESP32
    ESP.restart();
}

void SystemManager::emergencyShutdown() {
    Serial.println("[SystemManager] EMERGENCY SHUTDOWN!");
    
    publishEvent(SystemEventType::SYSTEM_SHUTDOWN, "Emergency shutdown", "", 3);
    
    // 立即停止所有服务
    for (auto& serviceInfo : m_services) {
        if (serviceInfo.service) {
            try {
                serviceInfo.service->cleanup();
            } catch (...) {
                // 忽略异常，继续关闭
            }
        }
    }
    
    setSystemState(SystemState::SHUTDOWN);
    
    delay(500);
    ESP.restart();
}

// ==================== 系统状态管理实现 ====================

SystemManager::SystemState SystemManager::getSystemState() const {
    return m_currentState;
}

void SystemManager::setSystemState(SystemState state) {
    if (m_currentState != state) {
        SystemState oldState = m_currentState;
        m_currentState = state;
        
        Serial.printf("[SystemManager] State changed: %s -> %s\n",
                     getSystemStateString().c_str(), getSystemStateString().c_str());
    }
}

bool SystemManager::isSystemHealthy() const {
    if (m_currentState != SystemState::RUNNING) {
        return false;
    }
    
    // 检查是否有不健康的关键服务
    for (const auto& serviceInfo : m_services) {
        if (serviceInfo.isInitialized && !serviceInfo.isHealthy && serviceInfo.priority < 50) {
            return false; // 高优先级服务不健康
        }
    }
    
    // 检查系统资源
    if (getHeapUsagePercent() > MEMORY_CRITICAL_THRESHOLD) {
        return false;
    }
    
    if (getTemperature() > TEMPERATURE_CRITICAL_THRESHOLD) {
        return false;
    }
    
    return true;
}

String SystemManager::getSystemStateString() const {
    switch (m_currentState) {
        case SystemState::UNINITIALIZED: return "UNINITIALIZED";
        case SystemState::INITIALIZING: return "INITIALIZING";
        case SystemState::RUNNING: return "RUNNING";
        case SystemState::DEGRADED: return "DEGRADED";
        case SystemState::ERROR: return "ERROR";
        case SystemState::SHUTTING_DOWN: return "SHUTTING_DOWN";
        case SystemState::SHUTDOWN: return "SHUTDOWN";
        default: return "UNKNOWN";
    }
}

// ==================== 服务管理实现 ====================

bool SystemManager::registerService(std::shared_ptr<ISystemService> service) {
    if (!service) {
        Serial.println("[SystemManager] Cannot register null service");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(s_mutex);
    
    // 检查服务是否已注册
    if (findService(service)) {
        Serial.printf("[SystemManager] Service %s already registered\n", service->getServiceName());
        return false;
    }
    
    // 创建服务信息
    ServiceInfo serviceInfo;
    serviceInfo.service = service;
    serviceInfo.name = service->getServiceName();
    serviceInfo.priority = service->getServicePriority();
    serviceInfo.isInitialized = false;
    serviceInfo.isHealthy = false;
    serviceInfo.initTime = 0;
    serviceInfo.lastHealthCheck = 0;
    serviceInfo.failureCount = 0;
    
    m_services.push_back(serviceInfo);
    
    // 按优先级排序
    sortServicesByPriority();
    
    Serial.printf("[SystemManager] Service %s registered with priority %u\n", 
                 serviceInfo.name.c_str(), serviceInfo.priority);
    
    return true;
}

bool SystemManager::unregisterService(const String& serviceName) {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    auto it = std::find_if(m_services.begin(), m_services.end(),
        [&serviceName](const ServiceInfo& info) {
            return info.name == serviceName;
        });
    
    if (it != m_services.end()) {
        // 停止服务
        if (it->isInitialized) {
            stopServiceInternal(*it);
        }
        
        m_services.erase(it);
        
        Serial.printf("[SystemManager] Service %s unregistered\n", serviceName.c_str());
        return true;
    }
    
    return false;
}

bool SystemManager::unregisterService(std::shared_ptr<ISystemService> service) {
    if (!service) {
        return false;
    }
    
    return unregisterService(service->getServiceName());
}

bool SystemManager::startService(const String& serviceName) {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    ServiceInfo* serviceInfo = findService(serviceName);
    if (!serviceInfo) {
        Serial.printf("[SystemManager] Service %s not found\n", serviceName.c_str());
        return false;
    }
    
    return startServiceInternal(*serviceInfo);
}

bool SystemManager::stopService(const String& serviceName) {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    ServiceInfo* serviceInfo = findService(serviceName);
    if (!serviceInfo) {
        Serial.printf("[SystemManager] Service %s not found\n", serviceName.c_str());
        return false;
    }
    
    stopServiceInternal(*serviceInfo);
    return true;
}

bool SystemManager::restartService(const String& serviceName) {
    if (stopService(serviceName)) {
        delay(100); // 短暂延迟
        return startService(serviceName);
    }
    return false;
}

std::shared_ptr<SystemManager::ISystemService> SystemManager::getService(const String& serviceName) {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    ServiceInfo* serviceInfo = findService(serviceName);
    if (serviceInfo) {
        return serviceInfo->service;
    }
    
    return nullptr;
}

std::vector<SystemManager::ServiceInfo> SystemManager::getAllServices() const {
    std::lock_guard<std::mutex> lock(s_mutex);
    return m_services;
}

std::vector<SystemManager::ServiceInfo> SystemManager::getHealthyServices() const {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    std::vector<ServiceInfo> healthyServices;
    for (const auto& serviceInfo : m_services) {
        if (serviceInfo.isHealthy) {
            healthyServices.push_back(serviceInfo);
        }
    }
    
    return healthyServices;
}

std::vector<SystemManager::ServiceInfo> SystemManager::getUnhealthyServices() const {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    std::vector<ServiceInfo> unhealthyServices;
    for (const auto& serviceInfo : m_services) {
        if (serviceInfo.isInitialized && !serviceInfo.isHealthy) {
            unhealthyServices.push_back(serviceInfo);
        }
    }
    
    return unhealthyServices;
}

// ==================== 健康检查实现 ====================

void SystemManager::performHealthCheck() {
    uint32_t currentTime = millis();
    
    if ((currentTime - m_lastHealthCheck) < m_config.healthCheckInterval) {
        return; // 还未到检查时间
    }
    
    std::lock_guard<std::mutex> lock(s_mutex);
    
    for (auto& serviceInfo : m_services) {
        if (serviceInfo.isInitialized) {
            checkServiceHealth(serviceInfo);
        }
    }
    
    m_lastHealthCheck = currentTime;
    
    // 检查系统整体健康状态
    checkSystemHealth();
}

void SystemManager::performHealthCheck(const String& serviceName) {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    ServiceInfo* serviceInfo = findService(serviceName);
    if (serviceInfo && serviceInfo->isInitialized) {
        checkServiceHealth(*serviceInfo);
    }
}

bool SystemManager::isServiceHealthy(const String& serviceName) const {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    const ServiceInfo* serviceInfo = const_cast<SystemManager*>(this)->findService(serviceName);
    if (serviceInfo) {
        return serviceInfo->isHealthy;
    }
    
    return false;
}

// ==================== 资源监控实现 ====================

const SystemManager::SystemResources& SystemManager::getSystemResources() const {
    return m_resources;
}

void SystemManager::updateSystemResources() {
    uint32_t currentTime = millis();

    if ((currentTime - m_lastResourceUpdate) < m_config.resourceUpdateInterval) {
        return; // 还未到更新时间
    }

    updateMemoryInfo();
    updateCPUInfo();
    updateTemperatureInfo();
    updateStackInfo();

    m_resources.uptime = currentTime - m_startupTime;
    m_lastResourceUpdate = currentTime;

    // 检查资源阈值
    checkResourceThresholds();
}

size_t SystemManager::getFreeHeap() const {
    return ESP.getFreeHeap();
}

size_t SystemManager::getMinFreeHeap() const {
    return ESP.getMinFreeHeap();
}

size_t SystemManager::getFreePSRAM() const {
    return ESP.getFreePsram();
}

float SystemManager::getHeapUsagePercent() const {
    if (m_resources.totalHeap == 0) {
        return 0.0f;
    }
    return (float)(m_resources.totalHeap - m_resources.freeHeap) / m_resources.totalHeap * 100.0f;
}

float SystemManager::getPSRAMUsagePercent() const {
    if (m_resources.totalPSRAM == 0) {
        return 0.0f;
    }
    return (float)(m_resources.totalPSRAM - m_resources.freePSRAM) / m_resources.totalPSRAM * 100.0f;
}

uint32_t SystemManager::getCPUFrequency() const {
    return ESP.getCpuFreqMHz();
}

float SystemManager::getCPUUsage() const {
    return m_resources.cpuUsage;
}

uint32_t SystemManager::getUptime() const {
    return millis() - m_startupTime;
}

float SystemManager::getTemperature() const {
    return m_resources.temperature;
}

bool SystemManager::isTemperatureNormal() const {
    return m_resources.temperature < TEMPERATURE_WARNING_THRESHOLD;
}

uint32_t SystemManager::getFreeStackSize() const {
    return m_resources.freeStackSize;
}

bool SystemManager::isStackHealthy() const {
    return m_resources.freeStackSize > 1024; // 至少1KB空闲栈空间
}

// ==================== 事件管理实现 ====================

void SystemManager::publishEvent(SystemEventType type, const String& description, const String& details, uint32_t severity) {
    SystemEvent event;
    event.type = type;
    event.description = description;
    event.details = details;
    event.timestamp = millis();
    event.severity = severity;

    publishEvent(event);
}

void SystemManager::publishEvent(const SystemEvent& event) {
    std::lock_guard<std::mutex> lock(s_mutex);

    // 添加到事件历史
    m_eventHistory.push_back(event);

    // 维护事件历史大小
    maintainEventHistory();

    // 处理事件
    processEvent(event);

    // 通知订阅者
    notifyEventSubscribers(event);

    // 打印重要事件
    if (event.severity >= 2) {
        Serial.printf("[SystemManager] Event: %s (severity: %u)\n",
                     event.description.c_str(), event.severity);
    }
}

void SystemManager::subscribeToEvent(SystemEventType type, EventCallback callback) {
    std::lock_guard<std::mutex> lock(s_mutex);
    m_eventCallbacks[type].push_back(callback);
}

void SystemManager::unsubscribeFromEvent(SystemEventType type) {
    std::lock_guard<std::mutex> lock(s_mutex);
    m_eventCallbacks[type].clear();
}

std::vector<SystemManager::SystemEvent> SystemManager::getEventHistory() const {
    std::lock_guard<std::mutex> lock(s_mutex);
    return m_eventHistory;
}

std::vector<SystemManager::SystemEvent> SystemManager::getEventHistory(SystemEventType type) const {
    std::lock_guard<std::mutex> lock(s_mutex);

    std::vector<SystemEvent> filteredEvents;
    for (const auto& event : m_eventHistory) {
        if (event.type == type) {
            filteredEvents.push_back(event);
        }
    }

    return filteredEvents;
}

void SystemManager::clearEventHistory() {
    std::lock_guard<std::mutex> lock(s_mutex);
    m_eventHistory.clear();
}

// ==================== 内部实现方法 ====================

bool SystemManager::initializeCore() {
    // 初始化资源监控
    updateSystemResources();

    // 设置默认配置
    m_config = SystemConfig();

    return true;
}

bool SystemManager::initializeServices() {
    Serial.println("[SystemManager] Initializing services...");

    // 按优先级排序服务
    sortServicesByPriority();

    // 初始化所有服务
    for (auto& serviceInfo : m_services) {
        if (!startServiceInternal(serviceInfo)) {
            Serial.printf("[SystemManager] Failed to start service: %s\n", serviceInfo.name.c_str());

            if (serviceInfo.priority < 50) { // 高优先级服务失败
                return false;
            }
        }
    }

    Serial.printf("[SystemManager] Initialized %u services\n", m_services.size());
    return true;
}

void SystemManager::setupWatchdog() {
    enableWatchdog(m_config.watchdogTimeout);
}

void SystemManager::setupEventSystem() {
    // 清理事件历史
    m_eventHistory.clear();
    m_eventCallbacks.clear();
}

SystemManager::ServiceInfo* SystemManager::findService(const String& serviceName) {
    auto it = std::find_if(m_services.begin(), m_services.end(),
        [&serviceName](const ServiceInfo& info) {
            return info.name == serviceName;
        });

    return (it != m_services.end()) ? &(*it) : nullptr;
}

SystemManager::ServiceInfo* SystemManager::findService(std::shared_ptr<ISystemService> service) {
    auto it = std::find_if(m_services.begin(), m_services.end(),
        [&service](const ServiceInfo& info) {
            return info.service == service;
        });

    return (it != m_services.end()) ? &(*it) : nullptr;
}

void SystemManager::sortServicesByPriority() {
    std::sort(m_services.begin(), m_services.end(),
        [](const ServiceInfo& a, const ServiceInfo& b) {
            return a.priority < b.priority; // 低数字 = 高优先级
        });
}
