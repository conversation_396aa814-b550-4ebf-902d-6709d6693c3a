#pragma once

#include <Arduino.h>
#include <vector>
#include <map>
#include <functional>
#include <mutex>
#include "DataStructures.h"

/**
 * ESP32-S3 红外控制系统 - 系统管理器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第六部分：系统管理最佳实践
 * 
 * 系统管理器职责：
 * - 系统初始化和启动管理
 * - 系统状态监控和管理
 * - 系统服务生命周期管理
 * - 系统资源监控和优化
 */

class SystemManager {
public:
    // 系统状态枚举
    enum class SystemState {
        UNINITIALIZED,      // 未初始化
        INITIALIZING,       // 初始化中
        RUNNING,            // 正常运行
        DEGRADED,           // 降级运行
        ERROR,              // 错误状态
        SHUTTING_DOWN,      // 关闭中
        SHUTDOWN            // 已关闭
    };
    
    // 系统服务接口
    class ISystemService {
    public:
        virtual ~ISystemService() = default;
        virtual bool initialize() = 0;
        virtual void cleanup() = 0;
        virtual bool isHealthy() const = 0;
        virtual const char* getServiceName() const = 0;
        virtual uint32_t getServicePriority() const { return 100; }
    };
    
    // 服务信息
    struct ServiceInfo {
        std::shared_ptr<ISystemService> service;
        String name;
        uint32_t priority;
        bool isInitialized;
        bool isHealthy;
        uint32_t initTime;
        uint32_t lastHealthCheck;
        uint32_t failureCount;
        
        ServiceInfo() : priority(100), isInitialized(false), isHealthy(false),
                       initTime(0), lastHealthCheck(0), failureCount(0) {}
    };
    
    // 系统资源信息
    struct SystemResources {
        size_t totalHeap;
        size_t freeHeap;
        size_t minFreeHeap;
        size_t totalPSRAM;
        size_t freePSRAM;
        uint32_t cpuFrequency;
        float cpuUsage;
        float temperature;
        uint32_t uptime;
        uint32_t freeStackSize;
        
        SystemResources() : totalHeap(0), freeHeap(0), minFreeHeap(0),
                           totalPSRAM(0), freePSRAM(0), cpuFrequency(0),
                           cpuUsage(0.0f), temperature(0.0f), uptime(0), freeStackSize(0) {}
    };
    
    // 系统事件类型
    enum class SystemEventType {
        SYSTEM_STARTUP,
        SYSTEM_SHUTDOWN,
        SERVICE_STARTED,
        SERVICE_STOPPED,
        SERVICE_FAILED,
        RESOURCE_WARNING,
        RESOURCE_CRITICAL,
        WATCHDOG_RESET,
        MEMORY_LOW,
        TEMPERATURE_HIGH
    };
    
    // 系统事件
    struct SystemEvent {
        SystemEventType type;
        String description;
        String details;
        uint32_t timestamp;
        uint32_t severity;  // 0-低, 1-中, 2-高, 3-严重
        
        SystemEvent() : type(SystemEventType::SYSTEM_STARTUP), timestamp(0), severity(0) {}
    };
    
    // 事件回调函数类型
    using EventCallback = std::function<void(const SystemEvent&)>;

private:
    static SystemManager* s_instance;
    static std::mutex s_mutex;
    
    SystemState m_currentState;
    std::vector<ServiceInfo> m_services;
    std::vector<SystemEvent> m_eventHistory;
    std::map<SystemEventType, std::vector<EventCallback>> m_eventCallbacks;
    
    SystemResources m_resources;
    uint32_t m_startupTime;
    uint32_t m_lastHealthCheck;
    uint32_t m_lastResourceUpdate;
    
    // 系统配置
    struct SystemConfig {
        uint32_t healthCheckInterval;   // 健康检查间隔（毫秒）
        uint32_t resourceUpdateInterval; // 资源更新间隔（毫秒）
        uint32_t maxEventHistory;       // 最大事件历史数量
        bool enableWatchdog;            // 启用看门狗
        uint32_t watchdogTimeout;       // 看门狗超时（毫秒）
        bool enableAutoRestart;         // 启用自动重启
        uint32_t maxServiceFailures;    // 最大服务失败次数
        
        SystemConfig() : healthCheckInterval(30000), resourceUpdateInterval(5000),
                        maxEventHistory(1000), enableWatchdog(true), watchdogTimeout(60000),
                        enableAutoRestart(true), maxServiceFailures(3) {}
    };
    
    SystemConfig m_config;
    
    SystemManager();

public:
    ~SystemManager();
    
    // 单例模式
    static SystemManager& getInstance();
    static void destroyInstance();
    
    // ==================== 系统生命周期管理 ====================
    
    // 系统初始化
    bool initialize();
    void cleanup();
    
    // 系统启动和关闭
    bool startup();
    void shutdown();
    void restart();
    void emergencyShutdown();
    
    // 系统状态管理
    SystemState getSystemState() const;
    void setSystemState(SystemState state);
    bool isSystemHealthy() const;
    String getSystemStateString() const;
    
    // ==================== 服务管理 ====================
    
    // 服务注册和注销
    bool registerService(std::shared_ptr<ISystemService> service);
    bool unregisterService(const String& serviceName);
    bool unregisterService(std::shared_ptr<ISystemService> service);
    
    // 服务控制
    bool startService(const String& serviceName);
    bool stopService(const String& serviceName);
    bool restartService(const String& serviceName);
    
    // 服务查询
    std::shared_ptr<ISystemService> getService(const String& serviceName);
    std::vector<ServiceInfo> getAllServices() const;
    std::vector<ServiceInfo> getHealthyServices() const;
    std::vector<ServiceInfo> getUnhealthyServices() const;
    
    // 服务健康检查
    void performHealthCheck();
    void performHealthCheck(const String& serviceName);
    bool isServiceHealthy(const String& serviceName) const;
    
    // ==================== 资源监控 ====================
    
    // 资源信息获取
    const SystemResources& getSystemResources() const;
    void updateSystemResources();
    
    // 内存监控
    size_t getFreeHeap() const;
    size_t getMinFreeHeap() const;
    size_t getFreePSRAM() const;
    float getHeapUsagePercent() const;
    float getPSRAMUsagePercent() const;
    
    // CPU监控
    uint32_t getCPUFrequency() const;
    float getCPUUsage() const;
    uint32_t getUptime() const;
    
    // 温度监控
    float getTemperature() const;
    bool isTemperatureNormal() const;
    
    // 栈监控
    uint32_t getFreeStackSize() const;
    bool isStackHealthy() const;
    
    // ==================== 事件管理 ====================
    
    // 事件发布
    void publishEvent(SystemEventType type, const String& description, const String& details = "", uint32_t severity = 0);
    void publishEvent(const SystemEvent& event);
    
    // 事件订阅
    void subscribeToEvent(SystemEventType type, EventCallback callback);
    void unsubscribeFromEvent(SystemEventType type);
    
    // 事件历史
    std::vector<SystemEvent> getEventHistory() const;
    std::vector<SystemEvent> getEventHistory(SystemEventType type) const;
    std::vector<SystemEvent> getEventHistory(uint32_t fromTime, uint32_t toTime) const;
    void clearEventHistory();
    
    // ==================== 看门狗管理 ====================
    
    // 看门狗控制
    void enableWatchdog(uint32_t timeoutMs = 60000);
    void disableWatchdog();
    void feedWatchdog();
    bool isWatchdogEnabled() const;
    
    // 看门狗回调
    void setWatchdogCallback(std::function<void()> callback);
    
    // ==================== 系统配置 ====================
    
    // 配置管理
    void setSystemConfig(const SystemConfig& config);
    const SystemConfig& getSystemConfig() const;
    void resetSystemConfig();
    
    // 配置项设置
    void setHealthCheckInterval(uint32_t intervalMs);
    void setResourceUpdateInterval(uint32_t intervalMs);
    void setMaxEventHistory(uint32_t maxEvents);
    
    // ==================== 系统诊断 ====================
    
    // 系统诊断
    struct SystemDiagnostics {
        bool systemHealthy;
        uint32_t healthyServices;
        uint32_t unhealthyServices;
        uint32_t criticalEvents;
        float memoryPressure;
        float cpuLoad;
        bool temperatureOK;
        String recommendations;
        
        SystemDiagnostics() : systemHealthy(false), healthyServices(0), unhealthyServices(0),
                             criticalEvents(0), memoryPressure(0.0f), cpuLoad(0.0f), temperatureOK(true) {}
    };
    
    SystemDiagnostics performSystemDiagnostics() const;
    void printSystemDiagnostics() const;
    
    // 系统报告
    void printSystemStatus() const;
    void printServiceStatus() const;
    void printResourceStatus() const;
    void printEventSummary() const;
    
    // ==================== 系统优化 ====================
    
    // 性能优化
    void optimizeSystem();
    void optimizeMemory();
    void optimizeCPU();
    
    // 资源清理
    void performMaintenance();
    void cleanupResources();
    void garbageCollect();
    
    // ==================== 错误处理和恢复 ====================
    
    // 错误处理
    void handleSystemError(const String& error, uint32_t severity = 2);
    void handleServiceFailure(const String& serviceName);
    void handleResourceCritical(const String& resource);
    
    // 系统恢复
    bool attemptSystemRecovery();
    bool recoverService(const String& serviceName);
    void resetSystem();

private:
    // ==================== 内部实现方法 ====================
    
    // 初始化实现
    bool initializeCore();
    bool initializeServices();
    void setupWatchdog();
    void setupEventSystem();
    
    // 服务管理实现
    ServiceInfo* findService(const String& serviceName);
    ServiceInfo* findService(std::shared_ptr<ISystemService> service);
    void sortServicesByPriority();
    bool startServiceInternal(ServiceInfo& serviceInfo);
    void stopServiceInternal(ServiceInfo& serviceInfo);
    
    // 资源监控实现
    void updateMemoryInfo();
    void updateCPUInfo();
    void updateTemperatureInfo();
    void updateStackInfo();
    void checkResourceThresholds();
    
    // 事件处理实现
    void processEvent(const SystemEvent& event);
    void notifyEventSubscribers(const SystemEvent& event);
    void maintainEventHistory();
    
    // 健康检查实现
    void checkServiceHealth(ServiceInfo& serviceInfo);
    void handleUnhealthyService(ServiceInfo& serviceInfo);
    
    // 看门狗实现
    std::function<void()> m_watchdogCallback;
    bool m_watchdogEnabled;
    uint32_t m_lastWatchdogFeed;
    
    // 系统维护
    void performPeriodicMaintenance();
    void checkSystemHealth();
    void updateSystemMetrics();
    
    // 错误处理实现
    void logSystemError(const String& error);
    void escalateError(uint32_t severity);
    
    // 常量定义
    static constexpr uint32_t DEFAULT_HEALTH_CHECK_INTERVAL = 30000;    // 30秒
    static constexpr uint32_t DEFAULT_RESOURCE_UPDATE_INTERVAL = 5000;  // 5秒
    static constexpr uint32_t MAX_SERVICE_FAILURES = 3;
    static constexpr uint32_t WATCHDOG_DEFAULT_TIMEOUT = 60000;         // 60秒
    static constexpr float MEMORY_WARNING_THRESHOLD = 80.0f;            // 80%
    static constexpr float MEMORY_CRITICAL_THRESHOLD = 95.0f;           // 95%
    static constexpr float TEMPERATURE_WARNING_THRESHOLD = 70.0f;       // 70°C
    static constexpr float TEMPERATURE_CRITICAL_THRESHOLD = 85.0f;      // 85°C
};
