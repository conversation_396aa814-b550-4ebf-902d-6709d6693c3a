#include "DataManager.h"

DataManager::DataManager() : m_initialized(false), m_lastSave(0), m_lastBackup(0) {}

DataManager::~DataManager() {
    cleanup();
}

bool DataManager::initialize(const DataManagerConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) return true;
    
    m_config = config;
    
    if (!createDataDirectory()) {
        Serial.println("❌ DataManager: Failed to create data directory");
        return false;
    }
    
    if (!initializeRepositories()) {
        Serial.println("❌ DataManager: Failed to initialize repositories");
        return false;
    }
    
    if (m_config.enablePersistence) {
        loadAllData();
    }
    
    m_initialized = true;
    Serial.println("✅ DataManager: Initialized successfully");
    return true;
}

void DataManager::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        if (m_config.enablePersistence) {
            saveAllData();
        }
        
        m_signalRepo.cleanup();
        m_taskRepo.cleanup();
        m_timerRepo.cleanup();
        m_configRepo.cleanup();
        
        m_initialized = false;
        Serial.println("✅ DataManager: Cleanup completed");
    }
}

bool DataManager::saveAllData() {
    if (!m_initialized) return false;
    
    bool success = true;
    success &= saveRepository("/signals.json", m_signalRepo.getAll());
    success &= saveRepository("/tasks.json", m_taskRepo.getAll());
    success &= saveRepository("/timers.json", m_timerRepo.getAll());
    success &= saveRepository("/configs.json", m_configRepo.getAll());
    
    if (success) {
        m_lastSave = millis();
        Serial.println("✅ DataManager: All data saved");
    } else {
        Serial.println("❌ DataManager: Failed to save some data");
    }
    
    return success;
}

bool DataManager::loadAllData() {
    if (!m_initialized) return false;
    
    std::vector<SignalData> signals;
    std::vector<TaskData> tasks;
    std::vector<TimerData> timers;
    std::vector<ConfigData> configs;
    
    loadRepository("/signals.json", signals);
    loadRepository("/tasks.json", tasks);
    loadRepository("/timers.json", timers);
    loadRepository("/configs.json", configs);
    
    // 加载到仓库
    for (const auto& signal : signals) {
        m_signalRepo.create(signal);
    }
    for (const auto& task : tasks) {
        m_taskRepo.create(task);
    }
    for (const auto& timer : timers) {
        m_timerRepo.create(timer);
    }
    for (const auto& config : configs) {
        m_configRepo.create(config);
    }
    
    Serial.printf("✅ DataManager: Loaded %d signals, %d tasks, %d timers, %d configs\n",
                  signals.size(), tasks.size(), timers.size(), configs.size());
    return true;
}

bool DataManager::createBackup() {
    if (!m_config.enableBackup) return false;
    
    String backupFile = generateBackupFilename();
    bool success = exportToJson(backupFile);
    
    if (success) {
        m_lastBackup = millis();
        Serial.println("✅ DataManager: Backup created: " + backupFile);
    }
    
    return success;
}

bool DataManager::beginTransaction() {
    m_signalRepo.beginTransaction();
    m_taskRepo.beginTransaction();
    m_timerRepo.beginTransaction();
    m_configRepo.beginTransaction();
    return true;
}

bool DataManager::commitTransaction() {
    m_signalRepo.commitTransaction();
    m_taskRepo.commitTransaction();
    m_timerRepo.commitTransaction();
    m_configRepo.commitTransaction();
    return true;
}

bool DataManager::rollbackTransaction() {
    m_signalRepo.rollbackTransaction();
    m_taskRepo.rollbackTransaction();
    m_timerRepo.rollbackTransaction();
    m_configRepo.rollbackTransaction();
    return true;
}

bool DataManager::exportToJson(const String& filename) {
    DynamicJsonDocument doc(8192);
    
    JsonObject root = doc.createNestedObject("data_export");
    root["version"] = 1;
    root["timestamp"] = millis();
    
    // 导出信号
    JsonArray signalsArray = JSONConverter::signalsToJsonArray(m_signalRepo.getAll(), doc);
    root["signals"] = signalsArray;
    
    // 导出任务
    JsonArray tasksArray = JSONConverter::tasksToJsonArray(m_taskRepo.getAll(), doc);
    root["tasks"] = tasksArray;
    
    // 导出定时器
    JsonArray timersArray = JSONConverter::timersToJsonArray(m_timerRepo.getAll(), doc);
    root["timers"] = timersArray;
    
    String jsonString;
    serializeJson(doc, jsonString);
    
    File file = LittleFS.open(filename, "w");
    if (!file) return false;
    
    size_t written = file.print(jsonString);
    file.close();
    
    return written > 0;
}

bool DataManager::importFromJson(const String& filename) {
    if (!LittleFS.exists(filename)) return false;
    
    File file = LittleFS.open(filename, "r");
    if (!file) return false;
    
    String jsonString = file.readString();
    file.close();
    
    DynamicJsonDocument doc(8192);
    if (deserializeJson(doc, jsonString) != DeserializationError::Ok) {
        return false;
    }
    
    JsonObject root = doc["data_export"];
    if (root.isNull()) return false;
    
    // 开始事务
    beginTransaction();
    
    try {
        // 清空现有数据
        m_signalRepo.clear();
        m_taskRepo.clear();
        m_timerRepo.clear();
        m_configRepo.clear();
        
        // 导入信号
        JsonArray signals = root["signals"];
        for (JsonVariant signalVar : signals) {
            auto result = JSONConverter::signalFromJson(signalVar.as<JsonObject>());
            if (result.isSuccess()) {
                m_signalRepo.create(result.getValue());
            }
        }
        
        // 导入任务
        JsonArray tasks = root["tasks"];
        for (JsonVariant taskVar : tasks) {
            auto result = JSONConverter::taskFromJson(taskVar.as<JsonObject>());
            if (result.isSuccess()) {
                m_taskRepo.create(result.getValue());
            }
        }
        
        // 导入定时器
        JsonArray timers = root["timers"];
        for (JsonVariant timerVar : timers) {
            auto result = JSONConverter::timerFromJson(timerVar.as<JsonObject>());
            if (result.isSuccess()) {
                m_timerRepo.create(result.getValue());
            }
        }
        
        commitTransaction();
        Serial.println("✅ DataManager: Data imported successfully");
        return true;
        
    } catch (...) {
        rollbackTransaction();
        Serial.println("❌ DataManager: Failed to import data");
        return false;
    }
}

JsonObject DataManager::getStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    JsonObject stats = doc.createNestedObject("data_manager_statistics");
    stats["initialized"] = m_initialized;
    stats["last_save"] = m_lastSave;
    stats["last_backup"] = m_lastBackup;
    
    // 仓库统计
    JsonObject repoStats = stats.createNestedObject("repositories");
    repoStats["signals_count"] = m_signalRepo.count();
    repoStats["tasks_count"] = m_taskRepo.count();
    repoStats["timers_count"] = m_timerRepo.count();
    repoStats["configs_count"] = m_configRepo.count();
    
    // 配置信息
    JsonObject config = stats.createNestedObject("config");
    config["enable_persistence"] = m_config.enablePersistence;
    config["data_directory"] = m_config.dataDirectory;
    config["auto_save_interval"] = m_config.autoSaveInterval;
    config["enable_backup"] = m_config.enableBackup;
    config["max_backup_files"] = m_config.maxBackupFiles;
    
    return stats;
}

bool DataManager::validateAllData() {
    bool valid = true;
    
    // 验证信号数据
    auto signals = m_signalRepo.getAll();
    for (const auto& signal : signals) {
        if (!signal.isValid()) {
            Serial.printf("❌ Invalid signal: ID=%d\n", signal.id);
            valid = false;
        }
    }
    
    // 验证任务数据
    auto tasks = m_taskRepo.getAll();
    for (const auto& task : tasks) {
        if (!task.isValid()) {
            Serial.printf("❌ Invalid task: ID=%d\n", task.id);
            valid = false;
        }
    }
    
    // 验证定时器数据
    auto timers = m_timerRepo.getAll();
    for (const auto& timer : timers) {
        if (!timer.isValid()) {
            Serial.printf("❌ Invalid timer: ID=%d\n", timer.id);
            valid = false;
        }
    }
    
    return valid;
}

bool DataManager::clearAllData() {
    beginTransaction();
    
    bool success = true;
    success &= m_signalRepo.clear();
    success &= m_taskRepo.clear();
    success &= m_timerRepo.clear();
    success &= m_configRepo.clear();
    
    if (success) {
        commitTransaction();
        Serial.println("✅ DataManager: All data cleared");
    } else {
        rollbackTransaction();
        Serial.println("❌ DataManager: Failed to clear data");
    }
    
    return success;
}

// ==================== 私有方法实现 ====================

bool DataManager::initializeRepositories() {
    bool success = true;
    success &= m_signalRepo.initialize();
    success &= m_taskRepo.initialize();
    success &= m_timerRepo.initialize();
    success &= m_configRepo.initialize();
    return success;
}

bool DataManager::createDataDirectory() {
    if (!LittleFS.exists(m_config.dataDirectory)) {
        return LittleFS.mkdir(m_config.dataDirectory);
    }
    return true;
}

bool DataManager::saveRepository(const String& filename, const std::vector<SignalData>& data) {
    DynamicJsonDocument doc(4096);
    JsonArray array = JSONConverter::signalsToJsonArray(data, doc);

    String jsonString;
    serializeJson(array, jsonString);

    File file = LittleFS.open(m_config.dataDirectory + filename, "w");
    if (!file) return false;

    size_t written = file.print(jsonString);
    file.close();
    return written > 0;
}

bool DataManager::saveRepository(const String& filename, const std::vector<TaskData>& data) {
    DynamicJsonDocument doc(4096);
    JsonArray array = JSONConverter::tasksToJsonArray(data, doc);

    String jsonString;
    serializeJson(array, jsonString);

    File file = LittleFS.open(m_config.dataDirectory + filename, "w");
    if (!file) return false;

    size_t written = file.print(jsonString);
    file.close();
    return written > 0;
}

bool DataManager::saveRepository(const String& filename, const std::vector<TimerData>& data) {
    DynamicJsonDocument doc(4096);
    JsonArray array = JSONConverter::timersToJsonArray(data, doc);

    String jsonString;
    serializeJson(array, jsonString);

    File file = LittleFS.open(m_config.dataDirectory + filename, "w");
    if (!file) return false;

    size_t written = file.print(jsonString);
    file.close();
    return written > 0;
}

bool DataManager::saveRepository(const String& filename, const std::vector<ConfigData>& data) {
    DynamicJsonDocument doc(4096);
    JsonArray array = doc.createNestedArray();

    for (const auto& config : data) {
        JsonObject configObj = JSONConverter::configToJson(config, doc);
        array.add(configObj);
    }

    String jsonString;
    serializeJson(array, jsonString);

    File file = LittleFS.open(m_config.dataDirectory + filename, "w");
    if (!file) return false;

    size_t written = file.print(jsonString);
    file.close();
    return written > 0;
}

bool DataManager::loadRepository(const String& filename, std::vector<SignalData>& data) {
    String fullPath = m_config.dataDirectory + filename;
    if (!LittleFS.exists(fullPath)) return false;

    File file = LittleFS.open(fullPath, "r");
    if (!file) return false;

    String jsonString = file.readString();
    file.close();

    DynamicJsonDocument doc(4096);
    if (deserializeJson(doc, jsonString) != DeserializationError::Ok) {
        return false;
    }

    JsonArray array = doc.as<JsonArray>();
    for (JsonVariant item : array) {
        auto result = JSONConverter::signalFromJson(item.as<JsonObject>());
        if (result.isSuccess()) {
            data.push_back(result.getValue());
        }
    }

    return true;
}

bool DataManager::loadRepository(const String& filename, std::vector<TaskData>& data) {
    String fullPath = m_config.dataDirectory + filename;
    if (!LittleFS.exists(fullPath)) return false;

    File file = LittleFS.open(fullPath, "r");
    if (!file) return false;

    String jsonString = file.readString();
    file.close();

    DynamicJsonDocument doc(4096);
    if (deserializeJson(doc, jsonString) != DeserializationError::Ok) {
        return false;
    }

    JsonArray array = doc.as<JsonArray>();
    for (JsonVariant item : array) {
        auto result = JSONConverter::taskFromJson(item.as<JsonObject>());
        if (result.isSuccess()) {
            data.push_back(result.getValue());
        }
    }

    return true;
}

bool DataManager::loadRepository(const String& filename, std::vector<TimerData>& data) {
    String fullPath = m_config.dataDirectory + filename;
    if (!LittleFS.exists(fullPath)) return false;

    File file = LittleFS.open(fullPath, "r");
    if (!file) return false;

    String jsonString = file.readString();
    file.close();

    DynamicJsonDocument doc(4096);
    if (deserializeJson(doc, jsonString) != DeserializationError::Ok) {
        return false;
    }

    JsonArray array = doc.as<JsonArray>();
    for (JsonVariant item : array) {
        auto result = JSONConverter::timerFromJson(item.as<JsonObject>());
        if (result.isSuccess()) {
            data.push_back(result.getValue());
        }
    }

    return true;
}

bool DataManager::loadRepository(const String& filename, std::vector<ConfigData>& data) {
    String fullPath = m_config.dataDirectory + filename;
    if (!LittleFS.exists(fullPath)) return false;

    File file = LittleFS.open(fullPath, "r");
    if (!file) return false;

    String jsonString = file.readString();
    file.close();

    DynamicJsonDocument doc(4096);
    if (deserializeJson(doc, jsonString) != DeserializationError::Ok) {
        return false;
    }

    JsonArray array = doc.as<JsonArray>();
    for (JsonVariant item : array) {
        auto result = JSONConverter::configFromJson(item.as<JsonObject>());
        if (result.isSuccess()) {
            data.push_back(result.getValue());
        }
    }

    return true;
}

String DataManager::generateBackupFilename() const {
    return m_config.dataDirectory + "/backup_" + String(millis()) + ".json";
}

bool DataManager::shouldAutoSave() const {
    return m_config.enablePersistence &&
           (millis() - m_lastSave) > m_config.autoSaveInterval;
}

bool DataManager::shouldCreateBackup() const {
    return m_config.enableBackup &&
           (millis() - m_lastBackup) > (m_config.autoSaveInterval * 10);
}
