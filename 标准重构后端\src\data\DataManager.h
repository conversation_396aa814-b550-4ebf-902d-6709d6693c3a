#pragma once

#include "../core/DataStructures.h"
#include "../core/JSONConverter.h"
#include "Repository.h"
#include <LittleFS.h>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - 数据管理器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：数据访问层
 */

class SignalRepository : public Repository<SignalData, SignalID> {
protected:
    SignalID getEntityId(const SignalData& entity) const override { return entity.id; }
    bool validateEntity(const SignalData& entity) const override { return entity.isValid(); }
};

class TaskRepository : public Repository<TaskData, TaskID> {
protected:
    TaskID getEntityId(const TaskData& entity) const override { return entity.id; }
    bool validateEntity(const TaskData& entity) const override { return entity.isValid(); }
};

class TimerRepository : public Repository<TimerData, TimerID> {
protected:
    TimerID getEntityId(const TimerData& entity) const override { return entity.id; }
    bool validateEntity(const TimerData& entity) const override { return entity.isValid(); }
};

class ConfigRepository : public Repository<ConfigData, ConfigID> {
protected:
    ConfigID getEntityId(const ConfigData& entity) const override { return entity.id; }
    bool validateEntity(const ConfigData& entity) const override { return entity.isValid(); }
};

class DataManager {
public:
    struct DataManagerConfig {
        bool enablePersistence;
        String dataDirectory;
        uint32_t autoSaveInterval;
        bool enableBackup;
        uint32_t maxBackupFiles;
        
        DataManagerConfig() : enablePersistence(true), dataDirectory("/data"),
                             autoSaveInterval(30000), enableBackup(true), maxBackupFiles(5) {}
    };

private:
    // 仓库实例
    SignalRepository m_signalRepo;
    TaskRepository m_taskRepo;
    TimerRepository m_timerRepo;
    ConfigRepository m_configRepo;
    
    // 配置
    DataManagerConfig m_config;
    
    // 状态
    bool m_initialized;
    Timestamp m_lastSave;
    Timestamp m_lastBackup;
    
    // 线程安全
    mutable std::mutex m_mutex;

public:
    DataManager();
    ~DataManager();
    
    bool initialize(const DataManagerConfig& config = DataManagerConfig());
    void cleanup();
    bool isInitialized() const { return m_initialized; }
    
    // 仓库访问
    SignalRepository& getSignalRepository() { return m_signalRepo; }
    TaskRepository& getTaskRepository() { return m_taskRepo; }
    TimerRepository& getTimerRepository() { return m_timerRepo; }
    ConfigRepository& getConfigRepository() { return m_configRepo; }
    
    // 持久化操作
    bool saveAllData();
    bool loadAllData();
    bool createBackup();
    bool restoreFromBackup(const String& backupFile);
    
    // 批量操作
    bool beginTransaction();
    bool commitTransaction();
    bool rollbackTransaction();
    
    // 数据导入导出
    bool exportToJson(const String& filename);
    bool importFromJson(const String& filename);
    
    // 统计信息
    JsonObject getStatistics(JsonDocument& doc) const;
    
    // 维护操作
    bool validateAllData();
    bool compactData();
    bool clearAllData();

private:
    bool initializeRepositories();
    bool createDataDirectory();
    bool saveRepository(const String& filename, const std::vector<SignalData>& data);
    bool saveRepository(const String& filename, const std::vector<TaskData>& data);
    bool saveRepository(const String& filename, const std::vector<TimerData>& data);
    bool saveRepository(const String& filename, const std::vector<ConfigData>& data);
    bool loadRepository(const String& filename, std::vector<SignalData>& data);
    bool loadRepository(const String& filename, std::vector<TaskData>& data);
    bool loadRepository(const String& filename, std::vector<TimerData>& data);
    bool loadRepository(const String& filename, std::vector<ConfigData>& data);
    String generateBackupFilename() const;
    bool shouldAutoSave() const;
    bool shouldCreateBackup() const;
};
