#pragma once

#include "../core/DataStructures.h"
#include <vector>
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - 基础仓库模板
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：数据访问层
 */

template<typename T, typename ID>
class Repository {
public:
    using FilterFunction = std::function<bool(const T&)>;
    
    struct RepositoryStats {
        uint32_t totalEntities;
        uint32_t createOperations;
        uint32_t readOperations;
        uint32_t updateOperations;
        uint32_t deleteOperations;
        uint32_t queryOperations;
        Timestamp lastOperation;
        
        RepositoryStats() : totalEntities(0), createOperations(0), readOperations(0),
                           updateOperations(0), deleteOperations(0), queryOperations(0),
                           lastOperation(0) {}
    };

protected:
    std::vector<T> m_entities;
    RepositoryStats m_stats;
    bool m_inTransaction;
    std::vector<T> m_transactionBackup;
    mutable std::mutex m_mutex;
    bool m_initialized;

public:
    Repository() : m_inTransaction(false), m_initialized(false) {}
    virtual ~Repository() { cleanup(); }
    
    virtual bool initialize() {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_initialized) return true;
        m_entities.reserve(100);
        m_initialized = true;
        return true;
    }
    
    virtual void cleanup() {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_initialized) {
            if (m_inTransaction) rollbackTransaction();
            m_entities.clear();
            m_initialized = false;
        }
    }
    
    bool isInitialized() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_initialized;
    }
    
    virtual Result<T> create(const T& entity) {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_initialized) {
            return Result<T>::Error(ErrorCode::SYSTEM_NOT_READY, "Repository not initialized");
        }
        if (!validateEntity(entity)) {
            return Result<T>::Error(ErrorCode::INVALID_PARAMETER, "Invalid entity data");
        }
        if (existsById(getEntityId(entity))) {
            return Result<T>::Error(ErrorCode::DATA_ALREADY_EXISTS, "Entity already exists");
        }
        m_entities.push_back(entity);
        m_stats.totalEntities++;
        m_stats.createOperations++;
        m_stats.lastOperation = millis();
        return Result<T>::Success(entity);
    }
    
    virtual Result<T> getById(ID id) {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_initialized) {
            return Result<T>::Error(ErrorCode::SYSTEM_NOT_READY, "Repository not initialized");
        }
        auto it = findEntityById(id);
        if (it == m_entities.end()) {
            return Result<T>::Error(ErrorCode::DATA_NOT_FOUND, "Entity not found");
        }
        m_stats.readOperations++;
        m_stats.lastOperation = millis();
        return Result<T>::Success(*it);
    }
    
    virtual Result<T> update(const T& entity) {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_initialized) {
            return Result<T>::Error(ErrorCode::SYSTEM_NOT_READY, "Repository not initialized");
        }
        if (!validateEntity(entity)) {
            return Result<T>::Error(ErrorCode::INVALID_PARAMETER, "Invalid entity data");
        }
        auto it = findEntityById(getEntityId(entity));
        if (it == m_entities.end()) {
            return Result<T>::Error(ErrorCode::DATA_NOT_FOUND, "Entity not found");
        }
        *it = entity;
        m_stats.updateOperations++;
        m_stats.lastOperation = millis();
        return Result<T>::Success(entity);
    }
    
    virtual bool deleteById(ID id) {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_initialized) return false;
        auto it = findEntityById(id);
        if (it == m_entities.end()) return false;
        m_entities.erase(it);
        m_stats.totalEntities--;
        m_stats.deleteOperations++;
        m_stats.lastOperation = millis();
        return true;
    }
    
    virtual std::vector<T> getAll() {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_initialized) return {};
        m_stats.readOperations++;
        m_stats.lastOperation = millis();
        return m_entities;
    }
    
    virtual size_t count() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_entities.size();
    }
    
    virtual bool existsById(ID id) const {
        std::lock_guard<std::mutex> lock(m_mutex);
        return findEntityById(id) != m_entities.end();
    }
    
    virtual bool clear() {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_initialized) return false;
        m_entities.clear();
        m_stats.totalEntities = 0;
        m_stats.lastOperation = millis();
        return true;
    }
    
    virtual std::vector<T> findWhere(FilterFunction filter) {
        std::lock_guard<std::mutex> lock(m_mutex);
        std::vector<T> result;
        if (!m_initialized) return result;
        for (const auto& entity : m_entities) {
            if (filter(entity)) result.push_back(entity);
        }
        m_stats.queryOperations++;
        m_stats.lastOperation = millis();
        return result;
    }
    
    virtual Result<T> findFirst(FilterFunction filter) {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_initialized) {
            return Result<T>::Error(ErrorCode::SYSTEM_NOT_READY, "Repository not initialized");
        }
        for (const auto& entity : m_entities) {
            if (filter(entity)) {
                m_stats.queryOperations++;
                m_stats.lastOperation = millis();
                return Result<T>::Success(entity);
            }
        }
        return Result<T>::Error(ErrorCode::DATA_NOT_FOUND, "No matching entity found");
    }
    
    // 事务支持
    virtual void beginTransaction() {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_inTransaction) {
            m_transactionBackup = m_entities;
            m_inTransaction = true;
        }
    }
    
    virtual void commitTransaction() {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_inTransaction) {
            m_transactionBackup.clear();
            m_inTransaction = false;
        }
    }
    
    virtual void rollbackTransaction() {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_inTransaction) {
            m_entities = m_transactionBackup;
            m_transactionBackup.clear();
            m_inTransaction = false;
        }
    }
    
    // 统计信息
    virtual JsonObject getDetailedStatistics(JsonDocument& doc) const {
        std::lock_guard<std::mutex> lock(m_mutex);
        JsonObject stats = doc.createNestedObject("repository_statistics");
        stats["initialized"] = m_initialized;
        stats["total_entities"] = m_stats.totalEntities;
        stats["create_operations"] = m_stats.createOperations;
        stats["read_operations"] = m_stats.readOperations;
        stats["update_operations"] = m_stats.updateOperations;
        stats["delete_operations"] = m_stats.deleteOperations;
        stats["query_operations"] = m_stats.queryOperations;
        stats["last_operation"] = m_stats.lastOperation;
        stats["in_transaction"] = m_inTransaction;
        return stats;
    }

protected:
    // 纯虚函数，由子类实现
    virtual ID getEntityId(const T& entity) const = 0;
    virtual bool validateEntity(const T& entity) const = 0;
    
    // 查找实体
    typename std::vector<T>::iterator findEntityById(ID id) {
        return std::find_if(m_entities.begin(), m_entities.end(),
                           [this, id](const T& entity) { return getEntityId(entity) == id; });
    }
    
    typename std::vector<T>::const_iterator findEntityById(ID id) const {
        return std::find_if(m_entities.begin(), m_entities.end(),
                           [this, id](const T& entity) { return getEntityId(entity) == id; });
    }
};
