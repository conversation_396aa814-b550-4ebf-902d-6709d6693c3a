#pragma once

#include "Repository.h"
#include "../core/DataStructures.h"

/**
 * ESP32-S3 红外控制系统 - 信号数据仓库
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第二阶段：数据访问层
 * 
 * 信号数据仓库职责：
 * - 信号数据的持久化存储
 * - 信号数据的查询和检索
 * - 信号数据的缓存管理
 * - 信号数据的索引优化
 */

class SignalRepository : public Repository<SignalData, SignalID> {
private:
    // 信号索引缓存
    struct SignalIndex {
        std::map<uint32_t, size_t> idIndex;        // ID到位置的映射
        std::map<String, std::vector<uint32_t>> nameIndex;    // 名称索引
        std::map<String, std::vector<uint32_t>> protocolIndex; // 协议索引
        std::map<String, std::vector<uint32_t>> categoryIndex; // 分类索引
        bool isDirty;
        
        SignalIndex() : isDirty(true) {}
    };
    
    SignalIndex m_index;
    
    // 信号统计信息
    struct SignalStats {
        uint32_t totalSignals;
        uint32_t favoriteSignals;
        std::map<String, uint32_t> protocolCounts;
        std::map<String, uint32_t> categoryCounts;
        Timestamp lastUpdated;
        
        SignalStats() : totalSignals(0), favoriteSignals(0), lastUpdated(0) {}
    };
    
    SignalStats m_stats;
    
    // 信号查询缓存
    struct QueryCache {
        std::map<String, std::vector<IRSignal>> cache;
        std::map<String, Timestamp> cacheTime;
        uint32_t maxCacheSize;
        uint32_t cacheTimeout;
        
        QueryCache() : maxCacheSize(50), cacheTimeout(300000) {} // 5分钟缓存
    };
    
    QueryCache m_queryCache;

public:
    SignalRepository();
    ~SignalRepository() override = default;
    
    // 基类接口实现
    bool initialize() override;
    void cleanup() override;
    
    // 基础CRUD操作
    Result<IRSignal> create(const IRSignal& signal) override;
    Result<IRSignal> getById(uint32_t id) override;
    Result<IRSignal> update(const IRSignal& signal) override;
    bool deleteById(uint32_t id) override;
    std::vector<IRSignal> getAll() override;
    
    // 信号特定查询方法
    std::vector<IRSignal> getByName(const String& name);
    std::vector<IRSignal> getByProtocol(const String& protocol);
    std::vector<IRSignal> getByCategory(const String& category);
    std::vector<IRSignal> getFavoriteSignals();
    std::vector<IRSignal> getRecentSignals(uint32_t count = 10);
    std::vector<IRSignal> getFrequentSignals(uint32_t count = 10);
    
    // 高级查询方法
    std::vector<IRSignal> searchSignals(const String& keyword);
    std::vector<IRSignal> getSignalsByDateRange(Timestamp startTime, Timestamp endTime);
    std::vector<IRSignal> getSignalsByUsageCount(uint32_t minUsage);
    std::vector<IRSignal> getSimilarSignals(const IRSignal& signal, float threshold = 0.8f);
    
    // 分页查询
    struct PageResult {
        std::vector<IRSignal> signals;
        uint32_t totalCount;
        uint32_t pageNumber;
        uint32_t pageSize;
        uint32_t totalPages;
        
        PageResult() : totalCount(0), pageNumber(0), pageSize(0), totalPages(0) {}
    };
    
    PageResult getSignalsPage(uint32_t page, uint32_t pageSize);
    PageResult searchSignalsPage(const String& keyword, uint32_t page, uint32_t pageSize);
    
    // 排序和过滤
    enum class SortField {
        NAME,
        PROTOCOL,
        CATEGORY,
        CREATED_AT,
        UPDATED_AT,
        USAGE_COUNT,
        LAST_USED
    };
    
    enum class SortOrder {
        ASCENDING,
        DESCENDING
    };
    
    std::vector<IRSignal> getSortedSignals(SortField field, SortOrder order = SortOrder::ASCENDING);
    
    struct SignalFilter {
        String namePattern;
        String protocol;
        String category;
        bool onlyFavorites;
        Timestamp createdAfter;
        Timestamp createdBefore;
        uint32_t minUsageCount;
        uint32_t maxUsageCount;
        
        SignalFilter() : onlyFavorites(false), createdAfter(0), createdBefore(0),
                        minUsageCount(0), maxUsageCount(UINT32_MAX) {}
    };
    
    std::vector<IRSignal> getFilteredSignals(const SignalFilter& filter);
    
    // 批量操作
    bool createBatch(const std::vector<IRSignal>& signals);
    bool updateBatch(const std::vector<IRSignal>& signals);
    bool deleteBatch(const std::vector<uint32_t>& ids);
    
    // 统计信息
    const SignalStats& getStatistics();
    void updateStatistics();
    
    uint32_t getSignalCount() const;
    uint32_t getFavoriteCount() const;
    std::map<String, uint32_t> getProtocolCounts() const;
    std::map<String, uint32_t> getCategoryCounts() const;
    
    // 数据完整性
    bool validateSignal(const IRSignal& signal) const;
    bool checkDataIntegrity();
    bool repairDataIntegrity();
    
    // 备份和恢复
    bool exportSignals(const String& filePath);
    bool importSignals(const String& filePath);
    bool createBackup(const String& backupPath);
    bool restoreBackup(const String& backupPath);
    
    // 性能优化
    void rebuildIndex();
    void optimizeStorage();
    void clearCache();
    void preloadFrequentSignals();
    
    // 使用统计
    void recordSignalUsage(uint32_t signalId);
    std::vector<IRSignal> getMostUsedSignals(uint32_t count = 10);
    std::vector<IRSignal> getLeastUsedSignals(uint32_t count = 10);
    
    // 信号分析
    struct SignalAnalysis {
        float averageDataLength;
        std::map<String, float> protocolDistribution;
        std::map<String, float> categoryDistribution;
        uint32_t duplicateCount;
        std::vector<uint32_t> duplicateIds;
        
        SignalAnalysis() : averageDataLength(0.0f), duplicateCount(0) {}
    };
    
    SignalAnalysis analyzeSignals();
    std::vector<uint32_t> findDuplicateSignals();
    bool removeDuplicates();

protected:
    // 基类抽象方法实现
    String getDataFilePath() const override;
    String getBackupFilePath() const override;
    
    // 索引管理
    void buildIndex();
    void updateIndex(const IRSignal& signal, bool isNew = true);
    void removeFromIndex(uint32_t signalId);
    
    // 缓存管理
    void addToCache(const String& key, const std::vector<IRSignal>& signals);
    std::vector<IRSignal> getFromCache(const String& key);
    void clearExpiredCache();
    String generateCacheKey(const String& operation, const String& params = "");
    
    // 数据验证
    bool isValidSignalData(const IRSignal& signal) const;
    bool isValidProtocol(const String& protocol) const;
    bool isValidDataArray(const std::vector<uint16_t>& data) const;
    
    // 查询优化
    std::vector<IRSignal> executeIndexedQuery(const std::vector<uint32_t>& ids);
    std::vector<uint32_t> intersectIdSets(const std::vector<std::vector<uint32_t>>& idSets);
    std::vector<uint32_t> unionIdSets(const std::vector<std::vector<uint32_t>>& idSets);
    
    // 排序实现
    void sortSignals(std::vector<IRSignal>& signals, SortField field, SortOrder order);
    
    // 相似度计算
    float calculateSignalSimilarity(const IRSignal& signal1, const IRSignal& signal2);
    float calculateDataSimilarity(const std::vector<uint16_t>& data1, const std::vector<uint16_t>& data2);
    
    // 数据压缩和优化
    bool compressSignalData(IRSignal& signal);
    bool decompressSignalData(IRSignal& signal);
    
    // 错误处理
    void handleRepositoryError(const String& operation, const String& error);
    bool recoverFromCorruption();
};
