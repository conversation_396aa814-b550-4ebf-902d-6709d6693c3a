#pragma once

#include "Repository.h"
#include "../core/DataStructures.h"

/**
 * ESP32-S3 红外控制系统 - 任务数据仓库
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第二阶段：数据访问层
 * 
 * 任务数据仓库职责：
 * - 任务数据的持久化存储
 * - 任务数据的查询和检索
 * - 任务调度信息管理
 * - 任务执行历史记录
 */

class TaskRepository : public Repository<Task> {
private:
    // 任务索引缓存
    struct TaskIndex {
        std::map<uint32_t, size_t> idIndex;                    // ID到位置的映射
        std::map<String, std::vector<uint32_t>> nameIndex;     // 名称索引
        std::map<TaskStatus, std::vector<uint32_t>> statusIndex; // 状态索引
        std::map<TaskType, std::vector<uint32_t>> typeIndex;   // 类型索引
        std::map<TaskPriority, std::vector<uint32_t>> priorityIndex; // 优先级索引
        std::multimap<Timestamp, uint32_t> scheduleIndex;      // 调度时间索引
        bool isDirty;
        
        TaskIndex() : isDirty(true) {}
    };
    
    TaskIndex m_index;
    
    // 任务统计信息
    struct TaskStats {
        uint32_t totalTasks;
        uint32_t pendingTasks;
        uint32_t runningTasks;
        uint32_t completedTasks;
        uint32_t failedTasks;
        uint32_t cancelledTasks;
        std::map<TaskType, uint32_t> typeCounts;
        std::map<TaskPriority, uint32_t> priorityCounts;
        Timestamp lastUpdated;
        
        TaskStats() : totalTasks(0), pendingTasks(0), runningTasks(0),
                     completedTasks(0), failedTasks(0), cancelledTasks(0), lastUpdated(0) {}
    };
    
    TaskStats m_stats;
    
    // 任务执行历史
    std::vector<TaskExecution> m_executionHistory;
    uint32_t m_maxHistorySize;
    
    // 任务依赖关系缓存
    std::map<uint32_t, std::vector<uint32_t>> m_dependencyMap;    // 任务ID -> 依赖任务ID列表
    std::map<uint32_t, std::vector<uint32_t>> m_dependentMap;     // 任务ID -> 依赖此任务的任务ID列表

public:
    TaskRepository();
    ~TaskRepository() override = default;
    
    // 基类接口实现
    bool initialize() override;
    void cleanup() override;
    
    // 基础CRUD操作
    Result<Task> create(const Task& task) override;
    Result<Task> getById(uint32_t id) override;
    Result<Task> update(const Task& task) override;
    bool deleteById(uint32_t id) override;
    std::vector<Task> getAll() override;
    
    // 任务特定查询方法
    std::vector<Task> getByName(const String& name);
    std::vector<Task> getByStatus(TaskStatus status);
    std::vector<Task> getByType(TaskType type);
    std::vector<Task> getByPriority(TaskPriority priority);
    std::vector<Task> getScheduledTasks();
    std::vector<Task> getPendingTasks();
    std::vector<Task> getRunningTasks();
    std::vector<Task> getCompletedTasks();
    std::vector<Task> getFailedTasks();
    
    // 调度相关查询
    std::vector<Task> getTasksScheduledBefore(Timestamp time);
    std::vector<Task> getTasksScheduledAfter(Timestamp time);
    std::vector<Task> getTasksScheduledBetween(Timestamp startTime, Timestamp endTime);
    std::vector<Task> getOverdueTasks();
    std::vector<Task> getUpcomingTasks(uint32_t count = 10);
    
    // 依赖关系管理
    bool addTaskDependency(uint32_t taskId, uint32_t dependencyId);
    bool removeTaskDependency(uint32_t taskId, uint32_t dependencyId);
    std::vector<uint32_t> getTaskDependencies(uint32_t taskId);
    std::vector<uint32_t> getTaskDependents(uint32_t taskId);
    bool hasCircularDependency(uint32_t taskId, uint32_t dependencyId);
    std::vector<Task> getExecutableTasksInOrder();
    
    // 高级查询方法
    std::vector<Task> searchTasks(const String& keyword);
    std::vector<Task> getTasksByDateRange(Timestamp startTime, Timestamp endTime);
    std::vector<Task> getTasksByExecutionTime(uint32_t minDuration, uint32_t maxDuration);
    std::vector<Task> getRepeatingTasks();
    std::vector<Task> getOneTimeTasks();
    
    // 分页查询
    struct TaskPageResult {
        std::vector<Task> tasks;
        uint32_t totalCount;
        uint32_t pageNumber;
        uint32_t pageSize;
        uint32_t totalPages;
        
        TaskPageResult() : totalCount(0), pageNumber(0), pageSize(0), totalPages(0) {}
    };
    
    TaskPageResult getTasksPage(uint32_t page, uint32_t pageSize);
    TaskPageResult searchTasksPage(const String& keyword, uint32_t page, uint32_t pageSize);
    TaskPageResult getTasksByStatusPage(TaskStatus status, uint32_t page, uint32_t pageSize);
    
    // 排序和过滤
    enum class TaskSortField {
        NAME,
        STATUS,
        TYPE,
        PRIORITY,
        CREATED_AT,
        UPDATED_AT,
        SCHEDULED_TIME,
        EXECUTION_TIME
    };
    
    enum class TaskSortOrder {
        ASCENDING,
        DESCENDING
    };
    
    std::vector<Task> getSortedTasks(TaskSortField field, TaskSortOrder order = TaskSortOrder::ASCENDING);
    
    struct TaskFilter {
        String namePattern;
        TaskStatus status;
        TaskType type;
        TaskPriority priority;
        bool onlyScheduled;
        bool onlyRepeating;
        Timestamp createdAfter;
        Timestamp createdBefore;
        Timestamp scheduledAfter;
        Timestamp scheduledBefore;
        uint32_t minExecutionTime;
        uint32_t maxExecutionTime;
        
        TaskFilter() : status(TaskStatus::PENDING), type(TaskType::SIGNAL_SEND), 
                      priority(TaskPriority::NORMAL), onlyScheduled(false), onlyRepeating(false),
                      createdAfter(0), createdBefore(0), scheduledAfter(0), scheduledBefore(0),
                      minExecutionTime(0), maxExecutionTime(UINT32_MAX) {}
    };
    
    std::vector<Task> getFilteredTasks(const TaskFilter& filter);
    
    // 批量操作
    bool createBatch(const std::vector<Task>& tasks);
    bool updateBatch(const std::vector<Task>& tasks);
    bool deleteBatch(const std::vector<uint32_t>& ids);
    bool updateTaskStatuses(const std::vector<uint32_t>& ids, TaskStatus newStatus);
    
    // 任务执行历史管理
    bool addExecutionRecord(const TaskExecution& execution);
    std::vector<TaskExecution> getExecutionHistory(uint32_t taskId);
    std::vector<TaskExecution> getExecutionHistoryByDateRange(Timestamp startTime, Timestamp endTime);
    std::vector<TaskExecution> getFailedExecutions();
    void clearExecutionHistory();
    void clearExecutionHistoryBefore(Timestamp time);
    
    // 统计信息
    const TaskStats& getStatistics();
    void updateStatistics();
    
    uint32_t getTaskCount() const;
    uint32_t getTaskCountByStatus(TaskStatus status) const;
    uint32_t getTaskCountByType(TaskType type) const;
    uint32_t getTaskCountByPriority(TaskPriority priority) const;
    std::map<TaskType, uint32_t> getTypeCounts() const;
    std::map<TaskPriority, uint32_t> getPriorityCounts() const;
    
    // 任务性能分析
    struct TaskPerformanceStats {
        float averageExecutionTime;
        float successRate;
        uint32_t totalExecutions;
        uint32_t successfulExecutions;
        uint32_t failedExecutions;
        Timestamp lastExecution;
        
        TaskPerformanceStats() : averageExecutionTime(0.0f), successRate(0.0f),
                               totalExecutions(0), successfulExecutions(0), 
                               failedExecutions(0), lastExecution(0) {}
    };
    
    TaskPerformanceStats getTaskPerformanceStats(uint32_t taskId);
    std::map<uint32_t, TaskPerformanceStats> getAllTaskPerformanceStats();
    
    // 数据完整性
    bool validateTask(const Task& task) const;
    bool checkDataIntegrity();
    bool repairDataIntegrity();
    bool validateDependencies();
    bool repairDependencies();
    
    // 备份和恢复
    bool exportTasks(const String& filePath);
    bool importTasks(const String& filePath);
    bool exportExecutionHistory(const String& filePath);
    bool importExecutionHistory(const String& filePath);
    
    // 性能优化
    void rebuildIndex();
    void rebuildDependencyMaps();
    void optimizeStorage();
    void clearCache();
    void compactExecutionHistory();
    
    // 任务调度优化
    std::vector<Task> getOptimalExecutionOrder();
    bool canTaskExecuteNow(uint32_t taskId);
    std::vector<uint32_t> getBlockedTasks();
    std::vector<uint32_t> getReadyTasks();

protected:
    // 基类抽象方法实现
    String getDataFilePath() const override;
    String getBackupFilePath() const override;
    
    // 索引管理
    void buildIndex();
    void updateIndex(const Task& task, bool isNew = true);
    void removeFromIndex(uint32_t taskId);
    
    // 依赖关系管理
    void buildDependencyMaps();
    void updateDependencyMaps(const Task& task);
    void removeDependencyMaps(uint32_t taskId);
    
    // 数据验证
    bool isValidTaskData(const Task& task) const;
    bool isValidTaskType(TaskType type) const;
    bool isValidTaskStatus(TaskStatus status) const;
    bool isValidTaskPriority(TaskPriority priority) const;
    
    // 查询优化
    std::vector<Task> executeIndexedQuery(const std::vector<uint32_t>& ids);
    std::vector<uint32_t> intersectTaskIdSets(const std::vector<std::vector<uint32_t>>& idSets);
    
    // 排序实现
    void sortTasks(std::vector<Task>& tasks, TaskSortField field, TaskSortOrder order);
    
    // 执行历史管理
    void maintainExecutionHistorySize();
    bool saveExecutionHistory();
    bool loadExecutionHistory();
    
    // 错误处理
    void handleRepositoryError(const String& operation, const String& error);
    bool recoverFromCorruption();
};
