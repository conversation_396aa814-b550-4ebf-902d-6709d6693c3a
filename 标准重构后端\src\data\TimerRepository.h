#pragma once

#include "Repository.h"
#include "../core/DataStructures.h"

/**
 * ESP32-S3 红外控制系统 - 定时器数据仓库
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第二阶段：数据访问层
 * 
 * 定时器数据仓库职责：
 * - 定时器数据的持久化存储
 * - 定时器数据的查询和检索
 * - 定时器触发时间管理
 * - 定时器执行历史记录
 */

class TimerRepository : public Repository<Timer> {
private:
    // 定时器索引缓存
    struct TimerIndex {
        std::map<uint32_t, size_t> idIndex;                    // ID到位置的映射
        std::map<String, std::vector<uint32_t>> nameIndex;     // 名称索引
        std::map<TimerType, std::vector<uint32_t>> typeIndex;  // 类型索引
        std::map<bool, std::vector<uint32_t>> enabledIndex;    // 启用状态索引
        std::multimap<Timestamp, uint32_t> triggerTimeIndex;   // 触发时间索引
        std::map<String, std::vector<uint32_t>> cronIndex;     // Cron表达式索引
        bool isDirty;
        
        TimerIndex() : isDirty(true) {}
    };
    
    TimerIndex m_index;
    
    // 定时器统计信息
    struct TimerStats {
        uint32_t totalTimers;
        uint32_t enabledTimers;
        uint32_t disabledTimers;
        uint32_t activeTimers;
        std::map<TimerType, uint32_t> typeCounts;
        uint32_t totalTriggers;
        uint32_t successfulTriggers;
        uint32_t failedTriggers;
        Timestamp lastUpdated;
        
        TimerStats() : totalTimers(0), enabledTimers(0), disabledTimers(0), activeTimers(0),
                      totalTriggers(0), successfulTriggers(0), failedTriggers(0), lastUpdated(0) {}
    };
    
    TimerStats m_stats;
    
    // 定时器执行历史
    std::vector<TimerExecution> m_executionHistory;
    uint32_t m_maxHistorySize;
    
    // 定时器触发队列
    struct TriggerQueue {
        std::priority_queue<std::pair<Timestamp, uint32_t>, 
                           std::vector<std::pair<Timestamp, uint32_t>>,
                           std::greater<std::pair<Timestamp, uint32_t>>> queue;
        std::mutex queueMutex;
        bool isDirty;
        
        TriggerQueue() : isDirty(true) {}
    };
    
    TriggerQueue m_triggerQueue;

public:
    TimerRepository();
    ~TimerRepository() override = default;
    
    // 基类接口实现
    bool initialize() override;
    void cleanup() override;
    
    // 基础CRUD操作
    Result<Timer> create(const Timer& timer) override;
    Result<Timer> getById(uint32_t id) override;
    Result<Timer> update(const Timer& timer) override;
    bool deleteById(uint32_t id) override;
    std::vector<Timer> getAll() override;
    
    // 定时器特定查询方法
    std::vector<Timer> getByName(const String& name);
    std::vector<Timer> getByType(TimerType type);
    std::vector<Timer> getEnabledTimers();
    std::vector<Timer> getDisabledTimers();
    std::vector<Timer> getActiveTimers();
    std::vector<Timer> getInactiveTimers();
    std::vector<Timer> getExpiredTimers();
    
    // 触发时间相关查询
    std::vector<Timer> getTimersToTrigger(Timestamp currentTime);
    std::vector<Timer> getTimersToTriggerBefore(Timestamp time);
    std::vector<Timer> getTimersToTriggerAfter(Timestamp time);
    std::vector<Timer> getTimersToTriggerBetween(Timestamp startTime, Timestamp endTime);
    std::vector<Timer> getUpcomingTimers(uint32_t count = 10);
    std::vector<Timer> getOverdueTimers();
    
    // Cron定时器查询
    std::vector<Timer> getCronTimers();
    std::vector<Timer> getTimersByCronPattern(const String& pattern);
    std::vector<Timer> getTimersWithInvalidCron();
    
    // 高级查询方法
    std::vector<Timer> searchTimers(const String& keyword);
    std::vector<Timer> getTimersByDateRange(Timestamp startTime, Timestamp endTime);
    std::vector<Timer> getTimersByTaskId(uint32_t taskId);
    std::vector<Timer> getRepeatingTimers();
    std::vector<Timer> getOneTimeTimers();
    std::vector<Timer> getTimersByFrequency(uint32_t minInterval, uint32_t maxInterval);
    
    // 分页查询
    struct TimerPageResult {
        std::vector<Timer> timers;
        uint32_t totalCount;
        uint32_t pageNumber;
        uint32_t pageSize;
        uint32_t totalPages;
        
        TimerPageResult() : totalCount(0), pageNumber(0), pageSize(0), totalPages(0) {}
    };
    
    TimerPageResult getTimersPage(uint32_t page, uint32_t pageSize);
    TimerPageResult searchTimersPage(const String& keyword, uint32_t page, uint32_t pageSize);
    TimerPageResult getTimersByTypePage(TimerType type, uint32_t page, uint32_t pageSize);
    
    // 排序和过滤
    enum class TimerSortField {
        NAME,
        TYPE,
        ENABLED,
        NEXT_TRIGGER_TIME,
        CREATED_AT,
        UPDATED_AT,
        TRIGGER_COUNT,
        LAST_TRIGGER_TIME
    };
    
    enum class TimerSortOrder {
        ASCENDING,
        DESCENDING
    };
    
    std::vector<Timer> getSortedTimers(TimerSortField field, TimerSortOrder order = TimerSortOrder::ASCENDING);
    
    struct TimerFilter {
        String namePattern;
        TimerType type;
        bool onlyEnabled;
        bool onlyActive;
        bool onlyRepeating;
        Timestamp createdAfter;
        Timestamp createdBefore;
        Timestamp nextTriggerAfter;
        Timestamp nextTriggerBefore;
        uint32_t minTriggerCount;
        uint32_t maxTriggerCount;
        String cronPattern;
        
        TimerFilter() : type(TimerType::ONCE), onlyEnabled(false), onlyActive(false), onlyRepeating(false),
                       createdAfter(0), createdBefore(0), nextTriggerAfter(0), nextTriggerBefore(0),
                       minTriggerCount(0), maxTriggerCount(UINT32_MAX) {}
    };
    
    std::vector<Timer> getFilteredTimers(const TimerFilter& filter);
    
    // 批量操作
    bool createBatch(const std::vector<Timer>& timers);
    bool updateBatch(const std::vector<Timer>& timers);
    bool deleteBatch(const std::vector<uint32_t>& ids);
    bool enableTimers(const std::vector<uint32_t>& ids);
    bool disableTimers(const std::vector<uint32_t>& ids);
    bool updateNextTriggerTimes(const std::vector<uint32_t>& ids);
    
    // 定时器触发管理
    bool triggerTimer(uint32_t timerId);
    bool updateNextTriggerTime(uint32_t timerId);
    Timestamp calculateNextTriggerTime(const Timer& timer);
    bool isTimerDue(uint32_t timerId, Timestamp currentTime);
    std::vector<uint32_t> getDueTimerIds(Timestamp currentTime);
    
    // 触发队列管理
    void rebuildTriggerQueue();
    void addToTriggerQueue(uint32_t timerId, Timestamp triggerTime);
    void removeFromTriggerQueue(uint32_t timerId);
    std::vector<uint32_t> getNextTimersToTrigger(uint32_t count = 10);
    
    // 定时器执行历史管理
    bool addExecutionRecord(const TimerExecution& execution);
    std::vector<TimerExecution> getExecutionHistory(uint32_t timerId);
    std::vector<TimerExecution> getExecutionHistoryByDateRange(Timestamp startTime, Timestamp endTime);
    std::vector<TimerExecution> getFailedExecutions();
    void clearExecutionHistory();
    void clearExecutionHistoryBefore(Timestamp time);
    
    // 统计信息
    const TimerStats& getStatistics();
    void updateStatistics();
    
    uint32_t getTimerCount() const;
    uint32_t getEnabledTimerCount() const;
    uint32_t getActiveTimerCount() const;
    uint32_t getTimerCountByType(TimerType type) const;
    std::map<TimerType, uint32_t> getTypeCounts() const;
    
    // 定时器性能分析
    struct TimerPerformanceStats {
        float averageExecutionTime;
        float successRate;
        uint32_t totalTriggers;
        uint32_t successfulTriggers;
        uint32_t failedTriggers;
        uint32_t missedTriggers;
        Timestamp lastTrigger;
        float averageInterval;
        
        TimerPerformanceStats() : averageExecutionTime(0.0f), successRate(0.0f),
                                 totalTriggers(0), successfulTriggers(0), failedTriggers(0),
                                 missedTriggers(0), lastTrigger(0), averageInterval(0.0f) {}
    };
    
    TimerPerformanceStats getTimerPerformanceStats(uint32_t timerId);
    std::map<uint32_t, TimerPerformanceStats> getAllTimerPerformanceStats();
    
    // Cron表达式处理
    bool validateCronExpression(const String& cronExpr);
    Timestamp getNextCronTriggerTime(const String& cronExpr, Timestamp fromTime);
    std::vector<Timestamp> getCronTriggerTimes(const String& cronExpr, Timestamp fromTime, uint32_t count);
    
    // 数据完整性
    bool validateTimer(const Timer& timer) const;
    bool checkDataIntegrity();
    bool repairDataIntegrity();
    bool validateTriggerTimes();
    bool repairTriggerTimes();
    
    // 备份和恢复
    bool exportTimers(const String& filePath);
    bool importTimers(const String& filePath);
    bool exportExecutionHistory(const String& filePath);
    bool importExecutionHistory(const String& filePath);
    
    // 性能优化
    void rebuildIndex();
    void optimizeStorage();
    void clearCache();
    void compactExecutionHistory();
    void cleanupExpiredTimers();

protected:
    // 基类抽象方法实现
    String getDataFilePath() const override;
    String getBackupFilePath() const override;
    
    // 索引管理
    void buildIndex();
    void updateIndex(const Timer& timer, bool isNew = true);
    void removeFromIndex(uint32_t timerId);
    
    // 数据验证
    bool isValidTimerData(const Timer& timer) const;
    bool isValidTimerType(TimerType type) const;
    bool isValidTriggerTime(Timestamp triggerTime) const;
    
    // 查询优化
    std::vector<Timer> executeIndexedQuery(const std::vector<uint32_t>& ids);
    std::vector<uint32_t> intersectTimerIdSets(const std::vector<std::vector<uint32_t>>& idSets);
    
    // 排序实现
    void sortTimers(std::vector<Timer>& timers, TimerSortField field, TimerSortOrder order);
    
    // 执行历史管理
    void maintainExecutionHistorySize();
    bool saveExecutionHistory();
    bool loadExecutionHistory();
    
    // Cron解析器
    struct CronFields {
        std::vector<int> seconds;
        std::vector<int> minutes;
        std::vector<int> hours;
        std::vector<int> days;
        std::vector<int> months;
        std::vector<int> weekdays;
    };
    
    CronFields parseCronExpression(const String& cronExpr);
    bool matchesCronFields(const CronFields& fields, Timestamp time);
    
    // 错误处理
    void handleRepositoryError(const String& operation, const String& error);
    bool recoverFromCorruption();
};
