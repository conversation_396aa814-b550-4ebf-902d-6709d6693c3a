#pragma once

#include "../core/DataStructures.h"
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - 红外控制器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：硬件抽象层
 */

class IRController {
public:
    struct IRConfig {
        uint8_t sendPin;
        uint8_t recvPin;
        uint16_t bufferSize;
        uint8_t timeout;
        bool enableReceiver;
        uint32_t frequency;
        
        IRConfig() : sendPin(4), recvPin(14), bufferSize(1024), timeout(15),
                     enableReceiver(true), frequency(38000) {}
    };
    
    struct IRStats {
        uint32_t sendCount;
        uint32_t receiveCount;
        uint32_t sendErrors;
        uint32_t receiveErrors;
        Timestamp lastSend;
        Timestamp lastReceive;
        
        IRStats() : sendCount(0), receiveCount(0), sendErrors(0), receiveErrors(0),
                    lastSend(0), lastReceive(0) {}
    };

private:
    IRsend* m_irSend;
    IRrecv* m_irRecv;
    IRConfig m_config;
    bool m_initialized;
    bool m_learning;
    SignalData m_learnedSignal;
    IRStats m_stats;
    mutable std::mutex m_mutex;

public:
    IRController();
    ~IRController();
    
    bool initialize(const IRConfig& config = IRConfig());
    void cleanup();
    bool isInitialized() const { return m_initialized; }
    
    bool sendSignal(const SignalData& signal);
    bool sendRawSignal(const std::vector<uint16_t>& rawData, uint32_t frequency = 38000);
    bool sendProtocolSignal(IRProtocol protocol, uint64_t code, uint8_t bits, uint32_t frequency = 38000);
    
    bool startLearning();
    bool stopLearning();
    bool isLearning() const { return m_learning; }
    Result<SignalData> getLearnedSignal();
    bool hasLearnedSignal() const;
    
    bool isSending() const;
    bool isReceiving() const;
    
    bool updateConfig(const IRConfig& config);
    const IRConfig& getConfig() const { return m_config; }
    
    const IRStats& getStatistics() const { return m_stats; }
    JsonObject getDetailedStatistics(JsonDocument& doc) const;
    
    bool testSender();
    bool testReceiver();
    bool performSelfTest();

private:
    bool initializeSender();
    bool initializeReceiver();
    bool validateSignal(const SignalData& signal) const;
    IRProtocol detectProtocol(const decode_results& results) const;
    DeviceType guessDeviceType(IRProtocol protocol, uint64_t code) const;
    bool convertToRawData(const decode_results& results, std::vector<uint16_t>& rawData) const;
    void updateSendStats(bool success);
    void updateReceiveStats(bool success);
    String protocolToLibraryName(IRProtocol protocol) const;
    bool sendByProtocol(IRProtocol protocol, uint64_t code, uint8_t bits, uint32_t frequency);
};
