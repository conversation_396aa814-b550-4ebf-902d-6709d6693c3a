#include <Arduino.h>
#include <WiFi.h>
#include <LittleFS.h>

#include "core/DataStructures.h"
#include "core/IDGenerator.h"
#include "data/DataManager.h"
#include "hardware/IRController.h"
#include "network/WSManager.h"
#include "services/SignalService.h"
#include "services/TaskService.h"
#include "services/TimerService.h"
#include "services/SystemService.h"
#include "WebServerManager.h"

/**
 * ESP32-S3 红外控制系统 - 主程序
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 * 
 * 系统架构：
 * 1. 核心层：数据结构、JSON转换、ID生成
 * 2. 数据访问层：仓库模式、数据管理
 * 3. 硬件抽象层：红外控制器
 * 4. 网络通信层：WebSocket管理
 * 5. 业务逻辑层：信号、任务、定时器、系统服务
 * 6. Web服务器管理器：API路由、请求处理
 */

// ==================== 全局组件实例 ====================

// 核心组件
DataManager* g_dataManager = nullptr;
IRController* g_irController = nullptr;
WSManager* g_wsManager = nullptr;

// 业务服务
SignalService* g_signalService = nullptr;
TaskService* g_taskService = nullptr;
TimerService* g_timerService = nullptr;
SystemService* g_systemService = nullptr;

// Web服务器
WebServerManager* g_webServerManager = nullptr;

// ==================== 系统配置 ====================

struct SystemConfig {
    // WiFi配置
    String wifiSSID = "ESP32_IR_System";
    String wifiPassword = "";
    bool enableAP = true;
    
    // 服务器配置
    uint16_t webServerPort = 80;
    bool enableWebSocket = true;
    
    // 硬件配置
    uint8_t irSendPin = 4;
    uint8_t irRecvPin = 14;
    
    // 系统配置
    bool enableLogging = true;
    bool enableMonitoring = true;
    uint32_t monitoringInterval = 5000;
} g_systemConfig;

// ==================== 系统状态 ====================

bool g_systemInitialized = false;
Timestamp g_systemStartTime = 0;
Timestamp g_lastMonitoringCheck = 0;

// ==================== 函数声明 ====================

bool initializeFileSystem();
bool initializeWiFi();
bool initializeCoreComponents();
bool initializeServices();
bool initializeWebServer();
void performSystemMonitoring();
void handleSystemLoop();
void cleanupSystem();

// ==================== Arduino主函数 ====================

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("========================================");
    Serial.println("ESP32-S3 红外控制系统启动中...");
    Serial.println("========================================");
    
    g_systemStartTime = millis();
    
    // 1. 初始化文件系统
    if (!initializeFileSystem()) {
        Serial.println("❌ 文件系统初始化失败");
        return;
    }
    
    // 2. 初始化WiFi
    if (!initializeWiFi()) {
        Serial.println("❌ WiFi初始化失败");
        return;
    }
    
    // 3. 初始化核心组件
    if (!initializeCoreComponents()) {
        Serial.println("❌ 核心组件初始化失败");
        cleanupSystem();
        return;
    }
    
    // 4. 初始化业务服务
    if (!initializeServices()) {
        Serial.println("❌ 业务服务初始化失败");
        cleanupSystem();
        return;
    }
    
    // 5. 初始化Web服务器
    if (!initializeWebServer()) {
        Serial.println("❌ Web服务器初始化失败");
        cleanupSystem();
        return;
    }
    
    g_systemInitialized = true;
    g_lastMonitoringCheck = millis();
    
    Serial.println("========================================");
    Serial.println("✅ 系统初始化完成！");
    Serial.printf("✅ 系统运行在: http://%s:%d\n", WiFi.localIP().toString().c_str(), g_systemConfig.webServerPort);
    Serial.println("========================================");
    
    if (g_systemService) {
        g_systemService->setSystemState(SystemState::RUNNING);
        g_systemService->addLogEntry(SystemService::LogLevel::INFO, "Main", "System startup completed successfully");
    }
}

void loop() {
    if (!g_systemInitialized) {
        delay(1000);
        return;
    }
    
    handleSystemLoop();
    delay(100); // 避免过度占用CPU
}

// ==================== 初始化函数实现 ====================

bool initializeFileSystem() {
    Serial.println("🔧 初始化文件系统...");
    
    if (!LittleFS.begin(true)) {
        Serial.println("❌ LittleFS初始化失败");
        return false;
    }
    
    Serial.printf("✅ LittleFS初始化成功 - 总空间: %d bytes, 已用: %d bytes\n", 
                  LittleFS.totalBytes(), LittleFS.usedBytes());
    return true;
}

bool initializeWiFi() {
    Serial.println("🔧 初始化WiFi...");
    
    if (g_systemConfig.enableAP) {
        // 启动AP模式
        WiFi.mode(WIFI_AP);
        bool success = WiFi.softAP(g_systemConfig.wifiSSID.c_str(), g_systemConfig.wifiPassword.c_str());
        
        if (success) {
            Serial.printf("✅ AP模式启动成功 - SSID: %s, IP: %s\n", 
                         g_systemConfig.wifiSSID.c_str(), WiFi.softAPIP().toString().c_str());
            return true;
        } else {
            Serial.println("❌ AP模式启动失败");
            return false;
        }
    }
    
    return true;
}

bool initializeCoreComponents() {
    Serial.println("🔧 初始化核心组件...");
    
    // 1. 初始化ID生成器
    IDGenerator* idGenerator = getGlobalIDGenerator();
    if (!idGenerator || !idGenerator->isInitialized()) {
        Serial.println("❌ ID生成器初始化失败");
        return false;
    }
    
    // 2. 初始化数据管理器
    g_dataManager = new DataManager();
    if (!g_dataManager || !g_dataManager->initialize()) {
        Serial.println("❌ 数据管理器初始化失败");
        return false;
    }
    
    // 3. 初始化红外控制器
    g_irController = new IRController();
    IRController::IRConfig irConfig;
    irConfig.sendPin = g_systemConfig.irSendPin;
    irConfig.recvPin = g_systemConfig.irRecvPin;
    
    if (!g_irController || !g_irController->initialize(irConfig)) {
        Serial.println("❌ 红外控制器初始化失败");
        return false;
    }
    
    // 4. 初始化WebSocket管理器
    g_wsManager = new WSManager();
    // WebSocket将在Web服务器初始化时设置
    
    Serial.println("✅ 核心组件初始化完成");
    return true;
}

bool initializeServices() {
    Serial.println("🔧 初始化业务服务...");
    
    // 1. 初始化系统服务
    g_systemService = new SystemService(g_dataManager);
    SystemService::ServiceConfig systemConfig;
    systemConfig.enableLogging = g_systemConfig.enableLogging;
    systemConfig.enableMonitoring = g_systemConfig.enableMonitoring;
    systemConfig.monitoringInterval = g_systemConfig.monitoringInterval;
    
    if (!g_systemService || !g_systemService->initialize(systemConfig)) {
        Serial.println("❌ 系统服务初始化失败");
        return false;
    }
    
    // 2. 初始化信号服务
    g_signalService = new SignalService(g_dataManager, g_irController);
    if (!g_signalService || !g_signalService->initialize()) {
        Serial.println("❌ 信号服务初始化失败");
        return false;
    }
    
    // 3. 初始化任务服务
    g_taskService = new TaskService(g_dataManager, g_signalService);
    if (!g_taskService || !g_taskService->initialize()) {
        Serial.println("❌ 任务服务初始化失败");
        return false;
    }
    
    // 4. 初始化定时器服务
    g_timerService = new TimerService(g_dataManager, g_taskService);
    if (!g_timerService || !g_timerService->initialize()) {
        Serial.println("❌ 定时器服务初始化失败");
        return false;
    }
    
    Serial.println("✅ 业务服务初始化完成");
    return true;
}

bool initializeWebServer() {
    Serial.println("🔧 初始化Web服务器...");

    // 1. 创建Web服务器管理器
    g_webServerManager = new WebServerManager();
    if (!g_webServerManager) {
        Serial.println("❌ Web服务器管理器创建失败");
        return false;
    }

    // 2. 注入依赖
    g_webServerManager->setDataManager(g_dataManager);
    g_webServerManager->setIRController(g_irController);
    g_webServerManager->setWSManager(g_wsManager);
    g_webServerManager->setSignalService(g_signalService);
    g_webServerManager->setTaskService(g_taskService);
    g_webServerManager->setTimerService(g_timerService);
    g_webServerManager->setSystemService(g_systemService);

    // 3. 配置Web服务器
    WebServerManager::ServerConfig serverConfig;
    serverConfig.port = g_systemConfig.webServerPort;
    serverConfig.enableCORS = true;
    serverConfig.enableAuthentication = false;
    serverConfig.enableStaticFiles = true;

    // 4. 初始化Web服务器
    if (!g_webServerManager->initialize(serverConfig)) {
        Serial.println("❌ Web服务器初始化失败");
        return false;
    }

    // 5. 启动Web服务器
    if (!g_webServerManager->startServer()) {
        Serial.println("❌ Web服务器启动失败");
        return false;
    }

    Serial.println("✅ Web服务器初始化完成");
    return true;
}

// ==================== 系统运行循环 ====================

void handleSystemLoop() {
    Timestamp currentTime = millis();

    // 定期系统监控
    if (g_systemConfig.enableMonitoring &&
        (currentTime - g_lastMonitoringCheck) >= g_systemConfig.monitoringInterval) {
        performSystemMonitoring();
        g_lastMonitoringCheck = currentTime;
    }

    // 处理定时器检查
    if (g_timerService) {
        g_timerService->checkTimers();
    }

    // 处理任务队列
    if (g_taskService) {
        g_taskService->processScheduledTasks();
        g_taskService->processRepeatingTasks();
        g_taskService->processExecutionQueue();
    }

    // 处理系统监控
    if (g_systemService) {
        g_systemService->processSystemMonitoring();
    }
}

void performSystemMonitoring() {
    if (!g_systemService) return;

    // 执行系统检查
    g_systemService->performSystemCheck();

    // 更新系统状态
    g_systemService->updateSystemStatus();

    // 检查内存使用情况
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 10000) { // 少于10KB可用内存
        g_systemService->addLogEntry(SystemService::LogLevel::WARNING, "Main",
                                    "Low memory warning: " + String(freeHeap) + " bytes free");
    }

    // 输出系统状态到串口（调试用）
    static uint32_t lastStatusOutput = 0;
    if (millis() - lastStatusOutput > 30000) { // 每30秒输出一次
        lastStatusOutput = millis();

        Serial.println("========== 系统状态 ==========");
        Serial.printf("运行时间: %lu ms\n", millis() - g_systemStartTime);
        Serial.printf("可用内存: %d bytes\n", freeHeap);
        Serial.printf("信号数量: %d\n", g_signalService ? g_signalService->getSignalCount() : 0);
        Serial.printf("任务数量: %d\n", g_taskService ? g_taskService->getTaskCount() : 0);
        Serial.printf("定时器数量: %d\n", g_timerService ? g_timerService->getTimerCount() : 0);
        Serial.println("============================");
    }
}

// ==================== 系统清理 ====================

void cleanupSystem() {
    Serial.println("🔧 清理系统资源...");

    if (g_webServerManager) {
        g_webServerManager->cleanup();
        delete g_webServerManager;
        g_webServerManager = nullptr;
    }

    if (g_timerService) {
        g_timerService->cleanup();
        delete g_timerService;
        g_timerService = nullptr;
    }

    if (g_taskService) {
        g_taskService->cleanup();
        delete g_taskService;
        g_taskService = nullptr;
    }

    if (g_signalService) {
        g_signalService->cleanup();
        delete g_signalService;
        g_signalService = nullptr;
    }

    if (g_systemService) {
        g_systemService->cleanup();
        delete g_systemService;
        g_systemService = nullptr;
    }

    if (g_wsManager) {
        g_wsManager->cleanup();
        delete g_wsManager;
        g_wsManager = nullptr;
    }

    if (g_irController) {
        g_irController->cleanup();
        delete g_irController;
        g_irController = nullptr;
    }

    if (g_dataManager) {
        g_dataManager->cleanup();
        delete g_dataManager;
        g_dataManager = nullptr;
    }

    Serial.println("✅ 系统资源清理完成");
}
