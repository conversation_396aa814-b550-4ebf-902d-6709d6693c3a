#include "WSManager.h"
#include "../core/JSONConverter.h"

/**
 * ESP32-S3 红外控制系统 - WebSocket管理器实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：网络通信层
 */

WSManager::WSManager() : m_webSocket(nullptr), m_initialized(false) {}

WSManager::~WSManager() {
    cleanup();
}

bool WSManager::initialize(AsyncWebServer* server, const WSConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized || !server) return false;
    
    m_config = config;
    
    m_webSocket = new AsyncWebSocket("/ws");
    m_webSocket->onEvent([this](AsyncWebSocket* server, AsyncWebSocketClient* client, 
                               AwsEventType type, void* arg, uint8_t* data, size_t len) {
        this->onEvent(server, client, type, arg, data, len);
    });
    
    server->addHandler(m_webSocket);
    
    m_clients.reserve(m_config.maxClients);
    m_initialized = true;
    
    Serial.println("✅ WSManager: Initialized successfully");
    return true;
}

void WSManager::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        if (m_webSocket) {
            m_webSocket->closeAll();
            delete m_webSocket;
            m_webSocket = nullptr;
        }
        
        m_clients.clear();
        m_initialized = false;
        
        Serial.println("✅ WSManager: Cleanup completed");
    }
}

void WSManager::setMessageHandler(MessageHandler handler) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_messageHandler = handler;
}

void WSManager::setConnectionHandler(ConnectionHandler handler) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_connectionHandler = handler;
}

bool WSManager::sendMessage(uint32_t clientId, const String& message) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_webSocket) return false;
    
    AsyncWebSocketClient* client = m_webSocket->client(clientId);
    if (!client || !client->canSend()) return false;
    
    client->text(message);
    m_stats.messagesSent++;
    m_stats.lastActivity = millis();
    
    return true;
}

bool WSManager::broadcastMessage(const String& message) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_webSocket) return false;
    
    m_webSocket->textAll(message);
    m_stats.messagesSent += m_stats.currentConnections;
    m_stats.lastActivity = millis();
    
    return true;
}

bool WSManager::sendToAuthenticated(const String& message) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_webSocket) return false;
    
    uint32_t sentCount = 0;
    for (const auto& clientInfo : m_clients) {
        if (clientInfo.authenticated) {
            AsyncWebSocketClient* client = m_webSocket->client(clientInfo.id);
            if (client && client->canSend()) {
                client->text(message);
                sentCount++;
            }
        }
    }
    
    m_stats.messagesSent += sentCount;
    m_stats.lastActivity = millis();
    
    return sentCount > 0;
}

std::vector<WSManager::ClientInfo> WSManager::getConnectedClients() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_clients;
}

bool WSManager::disconnectClient(uint32_t clientId) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_webSocket) return false;
    
    AsyncWebSocketClient* client = m_webSocket->client(clientId);
    if (!client) return false;
    
    client->close();
    removeClient(clientId);
    
    return true;
}

bool WSManager::authenticateClient(uint32_t clientId, const String& token) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_config.enableAuthentication) return true;
    
    ClientInfo* client = findClient(clientId);
    if (!client) return false;
    
    if (token == m_config.authToken) {
        client->authenticated = true;
        return true;
    } else {
        m_stats.authFailures++;
        return false;
    }
}

JsonObject WSManager::getDetailedStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    JsonObject stats = doc.createNestedObject("websocket_statistics");
    stats["initialized"] = m_initialized;
    stats["total_connections"] = m_stats.totalConnections;
    stats["current_connections"] = m_stats.currentConnections;
    stats["messages_sent"] = m_stats.messagesSent;
    stats["messages_received"] = m_stats.messagesReceived;
    stats["auth_failures"] = m_stats.authFailures;
    stats["last_activity"] = m_stats.lastActivity;
    
    JsonObject config = stats.createNestedObject("config");
    config["enable_heartbeat"] = m_config.enableHeartbeat;
    config["heartbeat_interval"] = m_config.heartbeatInterval;
    config["max_clients"] = m_config.maxClients;
    config["enable_authentication"] = m_config.enableAuthentication;
    
    JsonArray clients = stats.createNestedArray("clients");
    for (const auto& clientInfo : m_clients) {
        JsonObject client = clients.createNestedObject();
        client["id"] = clientInfo.id;
        client["remote_ip"] = clientInfo.remoteIP;
        client["connect_time"] = clientInfo.connectTime;
        client["last_ping"] = clientInfo.lastPing;
        client["authenticated"] = clientInfo.authenticated;
    }
    
    return stats;
}

bool WSManager::updateConfig(const WSConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_config = config;
    return true;
}

// ==================== 私有方法实现 ====================

void WSManager::onEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, AwsEventType type,
                       void* arg, uint8_t* data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT:
            handleConnect(client);
            break;
        case WS_EVT_DISCONNECT:
            handleDisconnect(client);
            break;
        case WS_EVT_DATA:
            handleMessage(client, data, len);
            break;
        case WS_EVT_ERROR:
            handleError(client, (AwsFrameInfo*)arg);
            break;
        default:
            break;
    }
}

void WSManager::handleConnect(AsyncWebSocketClient* client) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_clients.size() >= m_config.maxClients) {
        client->close();
        return;
    }
    
    ClientInfo clientInfo;
    clientInfo.id = client->id();
    clientInfo.remoteIP = client->remoteIP().toString();
    clientInfo.connectTime = millis();
    clientInfo.lastPing = clientInfo.connectTime;
    clientInfo.authenticated = !m_config.enableAuthentication;
    
    m_clients.push_back(clientInfo);
    m_stats.totalConnections++;
    m_stats.currentConnections++;
    m_stats.lastActivity = millis();
    
    if (m_connectionHandler) {
        m_connectionHandler(client->id(), true);
    }
    
    Serial.printf("✅ WSManager: Client %d connected from %s\n", 
                  client->id(), clientInfo.remoteIP.c_str());
}

void WSManager::handleDisconnect(AsyncWebSocketClient* client) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    removeClient(client->id());
    m_stats.currentConnections--;
    m_stats.lastActivity = millis();
    
    if (m_connectionHandler) {
        m_connectionHandler(client->id(), false);
    }
    
    Serial.printf("✅ WSManager: Client %d disconnected\n", client->id());
}

void WSManager::handleMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    String message = String((char*)data, len);
    
    if (!validateMessage(message)) {
        Serial.printf("❌ WSManager: Invalid message from client %d\n", client->id());
        return;
    }
    
    updateClientActivity(client->id());
    m_stats.messagesReceived++;
    m_stats.lastActivity = millis();
    
    if (m_messageHandler) {
        m_messageHandler(client->id(), message);
    }
}

void WSManager::handleError(AsyncWebSocketClient* client, AwsFrameInfo* info) {
    Serial.printf("❌ WSManager: Error from client %d\n", client->id());
}

WSManager::ClientInfo* WSManager::findClient(uint32_t clientId) {
    for (auto& client : m_clients) {
        if (client.id == clientId) {
            return &client;
        }
    }
    return nullptr;
}

void WSManager::removeClient(uint32_t clientId) {
    m_clients.erase(std::remove_if(m_clients.begin(), m_clients.end(),
                                  [clientId](const ClientInfo& client) {
                                      return client.id == clientId;
                                  }), m_clients.end());
}

void WSManager::updateClientActivity(uint32_t clientId) {
    ClientInfo* client = findClient(clientId);
    if (client) {
        client->lastPing = millis();
    }
}

bool WSManager::validateMessage(const String& message) const {
    return !message.isEmpty() && message.length() <= 1024;
}

void WSManager::performHeartbeat() {
    if (!m_config.enableHeartbeat) return;
    
    String heartbeat = "{\"type\":\"heartbeat\",\"timestamp\":" + String(millis()) + "}";
    broadcastMessage(heartbeat);
}
