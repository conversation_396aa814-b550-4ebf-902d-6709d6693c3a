#pragma once

#include "../core/DataStructures.h"
#include <ESPAsyncWebServer.h>
#include <vector>
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - WebSocket管理器
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：网络通信层
 */

class WSManager {
public:
    struct WSConfig {
        bool enableHeartbeat;
        uint32_t heartbeatInterval;
        uint32_t maxClients;
        bool enableAuthentication;
        String authToken;
        
        WSConfig() : enableHeartbeat(true), heartbeatInterval(30000), maxClients(10),
                     enableAuthentication(false), authToken("") {}
    };
    
    struct WSStats {
        uint32_t totalConnections;
        uint32_t currentConnections;
        uint32_t messagesSent;
        uint32_t messagesReceived;
        uint32_t authFailures;
        Timestamp lastActivity;
        
        WSStats() : totalConnections(0), currentConnections(0), messagesSent(0),
                    messagesReceived(0), authFailures(0), lastActivity(0) {}
    };
    
    struct ClientInfo {
        uint32_t id;
        String remoteIP;
        Timestamp connectTime;
        Timestamp lastPing;
        bool authenticated;
        
        ClientInfo() : id(0), connectTime(0), lastPing(0), authenticated(false) {}
    };
    
    using MessageHandler = std::function<void(uint32_t clientId, const String& message)>;
    using ConnectionHandler = std::function<void(uint32_t clientId, bool connected)>;

private:
    AsyncWebSocket* m_webSocket;
    WSConfig m_config;
    bool m_initialized;
    WSStats m_stats;
    std::vector<ClientInfo> m_clients;
    MessageHandler m_messageHandler;
    ConnectionHandler m_connectionHandler;
    mutable std::mutex m_mutex;

public:
    WSManager();
    ~WSManager();
    
    bool initialize(AsyncWebServer* server, const WSConfig& config = WSConfig());
    void cleanup();
    bool isInitialized() const { return m_initialized; }
    
    // 消息处理
    void setMessageHandler(MessageHandler handler);
    void setConnectionHandler(ConnectionHandler handler);
    
    // 消息发送
    bool sendMessage(uint32_t clientId, const String& message);
    bool broadcastMessage(const String& message);
    bool sendToAuthenticated(const String& message);
    
    // 客户端管理
    std::vector<ClientInfo> getConnectedClients() const;
    bool disconnectClient(uint32_t clientId);
    bool authenticateClient(uint32_t clientId, const String& token);
    
    // 统计信息
    const WSStats& getStatistics() const { return m_stats; }
    JsonObject getDetailedStatistics(JsonDocument& doc) const;
    
    // 配置管理
    bool updateConfig(const WSConfig& config);
    const WSConfig& getConfig() const { return m_config; }

private:
    void onEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, AwsEventType type,
                 void* arg, uint8_t* data, size_t len);
    void handleConnect(AsyncWebSocketClient* client);
    void handleDisconnect(AsyncWebSocketClient* client);
    void handleMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len);
    void handleError(AsyncWebSocketClient* client, AwsFrameInfo* info);
    
    ClientInfo* findClient(uint32_t clientId);
    void removeClient(uint32_t clientId);
    void updateClientActivity(uint32_t clientId);
    bool validateMessage(const String& message) const;
    void performHeartbeat();
};
