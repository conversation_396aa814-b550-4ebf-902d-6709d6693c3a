#include "SignalService.h"
#include "../core/IDGenerator.h"

/**
 * ESP32-S3 红外控制系统 - 信号业务服务实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：业务逻辑层
 */

SignalService::SignalService(DataManager* dataManager, IRController* irController)
    : m_dataManager(dataManager), m_irController(irController), m_initialized(false) {}

SignalService::~SignalService() {
    cleanup();
}

bool SignalService::initialize(const ServiceConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) return true;
    
    if (!m_dataManager || !m_irController) {
        Serial.println("❌ SignalService: Missing dependencies");
        return false;
    }
    
    if (!m_dataManager->isInitialized() || !m_irController->isInitialized()) {
        Serial.println("❌ SignalService: Dependencies not initialized");
        return false;
    }
    
    m_config = config;
    m_initialized = true;
    
    Serial.println("✅ SignalService: Initialized successfully");
    return true;
}

void SignalService::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        if (shouldAutoSave()) {
            performAutoSave();
        }
        
        m_initialized = false;
        Serial.println("✅ SignalService: Cleanup completed");
    }
}

Result<SignalData> SignalService::createSignal(const SignalData& signal) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<SignalData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    if (!validateSignalData(signal)) {
        return Result<SignalData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid signal data");
    }
    
    if (m_config.enableDuplicateCheck && checkDuplicateSignal(signal)) {
        return Result<SignalData>::Error(ErrorCode::DATA_ALREADY_EXISTS, "Duplicate signal detected");
    }
    
    SignalData newSignal = signal;
    if (newSignal.id == INVALID_ID) {
        newSignal.id = generateUniqueId();
    }
    
    newSignal.createdTime = millis();
    newSignal.modifiedTime = newSignal.createdTime;
    
    auto result = m_dataManager->getSignalRepository().create(newSignal);
    if (result.isSuccess()) {
        m_stats.signalsCreated++;
        m_stats.lastOperation = millis();
        
        triggerEvent(newSignal, "created");
        
        if (shouldAutoSave()) {
            performAutoSave();
        }
    }
    
    return result;
}

Result<SignalData> SignalService::getSignal(SignalID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<SignalData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    return m_dataManager->getSignalRepository().getById(id);
}

Result<SignalData> SignalService::updateSignal(const SignalData& signal) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<SignalData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    if (!validateSignalData(signal)) {
        return Result<SignalData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid signal data");
    }
    
    SignalData updatedSignal = signal;
    updatedSignal.modifiedTime = millis();
    
    auto result = m_dataManager->getSignalRepository().update(updatedSignal);
    if (result.isSuccess()) {
        m_stats.lastOperation = millis();
        triggerEvent(updatedSignal, "updated");
        
        if (shouldAutoSave()) {
            performAutoSave();
        }
    }
    
    return result;
}

bool SignalService::deleteSignal(SignalID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto signalResult = m_dataManager->getSignalRepository().getById(id);
    if (!signalResult.isSuccess()) return false;
    
    bool success = m_dataManager->getSignalRepository().deleteById(id);
    if (success) {
        m_stats.signalsDeleted++;
        m_stats.lastOperation = millis();
        
        triggerEvent(signalResult.getValue(), "deleted");
        
        if (shouldAutoSave()) {
            performAutoSave();
        }
    }
    
    return success;
}

std::vector<SignalData> SignalService::getAllSignals() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getSignalRepository().getAll();
}

bool SignalService::sendSignal(SignalID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getSignalRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    SignalData signal = result.getValue();
    bool success = m_irController->sendSignal(signal);
    
    if (success) {
        updateSignalUsage(signal);
        m_dataManager->getSignalRepository().update(signal);
        
        m_stats.signalsSent++;
        m_stats.lastOperation = millis();
        
        triggerEvent(signal, "sent");
    }
    
    return success;
}

bool SignalService::sendSignalData(const SignalData& signal) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !validateSignalData(signal)) return false;
    
    bool success = m_irController->sendSignal(signal);
    
    if (success) {
        m_stats.signalsSent++;
        m_stats.lastOperation = millis();
        triggerEvent(signal, "sent");
    }
    
    return success;
}

bool SignalService::testSignal(SignalID id) {
    return sendSignal(id); // 测试就是发送信号
}

bool SignalService::startLearning() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    bool success = m_irController->startLearning();
    if (success) {
        m_stats.learningAttempts++;
        m_stats.lastOperation = millis();
    }
    
    return success;
}

bool SignalService::stopLearning() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    return m_irController->stopLearning();
}

bool SignalService::isLearning() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    return m_irController->isLearning();
}

Result<SignalData> SignalService::getLearnedSignal() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<SignalData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    auto result = m_irController->getLearnedSignal();
    if (result.isSuccess()) {
        m_stats.learningSuccesses++;
        m_stats.lastOperation = millis();
    }
    
    return result;
}

Result<SignalData> SignalService::saveLearnedSignal(const String& name, const String& description) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<SignalData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    auto learnedResult = m_irController->getLearnedSignal();
    if (!learnedResult.isSuccess()) {
        return learnedResult;
    }
    
    SignalData signal = learnedResult.getValue();
    signal.name = name;
    signal.description = description;
    signal.id = generateUniqueId();
    
    return createSignal(signal);
}

std::vector<SignalData> SignalService::findSignalsByDevice(DeviceType deviceType) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getSignalRepository().findWhere(
        [deviceType](const SignalData& signal) {
            return signal.deviceType == deviceType;
        }
    );
}

std::vector<SignalData> SignalService::findSignalsByProtocol(IRProtocol protocol) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getSignalRepository().findWhere(
        [protocol](const SignalData& signal) {
            return signal.protocol == protocol;
        }
    );
}

std::vector<SignalData> SignalService::findSignalsByName(const String& namePattern) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getSignalRepository().findWhere(
        [namePattern](const SignalData& signal) {
            return signal.name.indexOf(namePattern) >= 0;
        }
    );
}

std::vector<SignalData> SignalService::getRecentSignals(uint32_t count) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    auto signals = m_dataManager->getSignalRepository().getAll();
    
    // 按创建时间排序
    std::sort(signals.begin(), signals.end(),
              [](const SignalData& a, const SignalData& b) {
                  return a.createdTime > b.createdTime;
              });
    
    if (signals.size() > count) {
        signals.resize(count);
    }
    
    return signals;
}

std::vector<SignalData> SignalService::getMostUsedSignals(uint32_t count) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    auto signals = m_dataManager->getSignalRepository().getAll();
    
    // 按使用次数排序
    std::sort(signals.begin(), signals.end(),
              [](const SignalData& a, const SignalData& b) {
                  return a.usageCount > b.usageCount;
              });
    
    if (signals.size() > count) {
        signals.resize(count);
    }
    
    return signals;
}

bool SignalService::importSignals(const std::vector<SignalData>& signals) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    m_dataManager->beginTransaction();

    try {
        for (const auto& signal : signals) {
            SignalData newSignal = signal;
            newSignal.id = generateUniqueId();

            auto result = m_dataManager->getSignalRepository().create(newSignal);
            if (!result.isSuccess()) {
                m_dataManager->rollbackTransaction();
                return false;
            }
        }

        m_dataManager->commitTransaction();
        m_stats.signalsCreated += signals.size();
        m_stats.lastOperation = millis();

        return true;

    } catch (...) {
        m_dataManager->rollbackTransaction();
        return false;
    }
}

std::vector<SignalData> SignalService::exportSignals() {
    return getAllSignals();
}

bool SignalService::clearAllSignals() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    bool success = m_dataManager->getSignalRepository().clear();
    if (success) {
        m_stats.lastOperation = millis();
        performAutoSave();
    }

    return success;
}

bool SignalService::duplicateSignal(SignalID sourceId, const String& newName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    auto result = m_dataManager->getSignalRepository().getById(sourceId);
    if (!result.isSuccess()) return false;

    SignalData newSignal = result.getValue();
    newSignal.id = generateUniqueId();
    newSignal.name = newName;
    newSignal.createdTime = millis();
    newSignal.modifiedTime = newSignal.createdTime;
    newSignal.usageCount = 0;
    newSignal.lastUsed = 0;

    auto createResult = m_dataManager->getSignalRepository().create(newSignal);
    return createResult.isSuccess();
}

bool SignalService::validateSignal(const SignalData& signal) {
    return validateSignalData(signal);
}

bool SignalService::validateAllSignals() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    auto signals = m_dataManager->getSignalRepository().getAll();

    for (const auto& signal : signals) {
        if (!validateSignalData(signal)) {
            Serial.printf("❌ Invalid signal found: ID=%d, Name=%s\n", signal.id, signal.name.c_str());
            return false;
        }
    }

    return true;
}

std::vector<SignalID> SignalService::findDuplicateSignals() {
    std::lock_guard<std::mutex> lock(m_mutex);

    std::vector<SignalID> duplicates;
    if (!m_initialized) return duplicates;

    auto signals = m_dataManager->getSignalRepository().getAll();

    for (size_t i = 0; i < signals.size(); i++) {
        for (size_t j = i + 1; j < signals.size(); j++) {
            if (signals[i].protocol == signals[j].protocol &&
                signals[i].code == signals[j].code &&
                signals[i].bits == signals[j].bits) {
                duplicates.push_back(signals[j].id);
            }
        }
    }

    return duplicates;
}

bool SignalService::removeDuplicates() {
    auto duplicates = findDuplicateSignals();

    for (SignalID id : duplicates) {
        deleteSignal(id);
    }

    return true;
}

JsonObject SignalService::getDetailedStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("signal_service_statistics");
    stats["initialized"] = m_initialized;
    stats["signals_created"] = m_stats.signalsCreated;
    stats["signals_deleted"] = m_stats.signalsDeleted;
    stats["signals_sent"] = m_stats.signalsSent;
    stats["learning_attempts"] = m_stats.learningAttempts;
    stats["learning_successes"] = m_stats.learningSuccesses;
    stats["last_operation"] = m_stats.lastOperation;

    stats["total_signals"] = getSignalCount();

    JsonObject config = stats.createNestedObject("config");
    config["enable_auto_save"] = m_config.enableAutoSave;
    config["auto_save_interval"] = m_config.autoSaveInterval;
    config["enable_validation"] = m_config.enableValidation;
    config["enable_duplicate_check"] = m_config.enableDuplicateCheck;
    config["max_signals_per_device"] = m_config.maxSignalsPerDevice;

    return stats;
}

uint32_t SignalService::getSignalCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return 0;

    return m_dataManager->getSignalRepository().count();
}

uint32_t SignalService::getSignalCountByDevice(DeviceType deviceType) const {
    auto signals = findSignalsByDevice(deviceType);
    return signals.size();
}

void SignalService::setEventHandler(SignalEventHandler handler) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_eventHandler = handler;
}

bool SignalService::updateConfig(const ServiceConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_config = config;
    return true;
}

// ==================== 私有方法实现 ====================

bool SignalService::validateSignalData(const SignalData& signal) const {
    if (!m_config.enableValidation) return true;

    return signal.isValid();
}

bool SignalService::checkDuplicateSignal(const SignalData& signal) const {
    auto signals = m_dataManager->getSignalRepository().getAll();

    for (const auto& existing : signals) {
        if (existing.protocol == signal.protocol &&
            existing.code == signal.code &&
            existing.bits == signal.bits) {
            return true;
        }
    }

    return false;
}

SignalID SignalService::generateUniqueId() {
    return GENERATE_SIGNAL_ID();
}

void SignalService::updateSignalUsage(SignalData& signal) {
    signal.updateUsage();
}

void SignalService::triggerEvent(const SignalData& signal, const String& event) {
    if (m_eventHandler) {
        m_eventHandler(signal, event);
    }
}

bool SignalService::shouldAutoSave() const {
    return m_config.enableAutoSave &&
           (millis() - m_stats.lastOperation) > m_config.autoSaveInterval;
}

void SignalService::performAutoSave() {
    if (m_dataManager) {
        m_dataManager->saveAllData();
    }
}
