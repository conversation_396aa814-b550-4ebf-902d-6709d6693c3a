#pragma once

#include "../core/DataStructures.h"
#include "../data/DataManager.h"
#include "../hardware/IRController.h"
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - 信号业务服务
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：业务逻辑层
 */

class SignalService {
public:
    struct ServiceConfig {
        bool enableAutoSave;
        uint32_t autoSaveInterval;
        bool enableValidation;
        bool enableDuplicateCheck;
        uint32_t maxSignalsPerDevice;
        
        ServiceConfig() : enableAutoSave(true), autoSaveInterval(30000), enableValidation(true),
                         enableDuplicateCheck(true), maxSignalsPerDevice(100) {}
    };
    
    struct ServiceStats {
        uint32_t signalsCreated;
        uint32_t signalsDeleted;
        uint32_t signalsSent;
        uint32_t learningAttempts;
        uint32_t learningSuccesses;
        Timestamp lastOperation;
        
        ServiceStats() : signalsCreated(0), signalsDeleted(0), signalsSent(0),
                        learningAttempts(0), learningSuccesses(0), lastOperation(0) {}
    };
    
    using SignalEventHandler = std::function<void(const SignalData& signal, const String& event)>;

private:
    DataManager* m_dataManager;
    IRController* m_irController;
    ServiceConfig m_config;
    bool m_initialized;
    ServiceStats m_stats;
    SignalEventHandler m_eventHandler;
    mutable std::mutex m_mutex;

public:
    SignalService(DataManager* dataManager, IRController* irController);
    ~SignalService();
    
    bool initialize(const ServiceConfig& config = ServiceConfig());
    void cleanup();
    bool isInitialized() const { return m_initialized; }
    
    // 信号管理
    Result<SignalData> createSignal(const SignalData& signal);
    Result<SignalData> getSignal(SignalID id);
    Result<SignalData> updateSignal(const SignalData& signal);
    bool deleteSignal(SignalID id);
    std::vector<SignalData> getAllSignals();
    
    // 信号发送
    bool sendSignal(SignalID id);
    bool sendSignalData(const SignalData& signal);
    bool testSignal(SignalID id);
    
    // 信号学习
    bool startLearning();
    bool stopLearning();
    bool isLearning() const;
    Result<SignalData> getLearnedSignal();
    Result<SignalData> saveLearnedSignal(const String& name, const String& description = "");
    
    // 查询和过滤
    std::vector<SignalData> findSignalsByDevice(DeviceType deviceType);
    std::vector<SignalData> findSignalsByProtocol(IRProtocol protocol);
    std::vector<SignalData> findSignalsByName(const String& namePattern);
    std::vector<SignalData> getRecentSignals(uint32_t count = 10);
    std::vector<SignalData> getMostUsedSignals(uint32_t count = 10);
    
    // 批量操作
    bool importSignals(const std::vector<SignalData>& signals);
    std::vector<SignalData> exportSignals();
    bool clearAllSignals();
    bool duplicateSignal(SignalID sourceId, const String& newName);
    
    // 验证和维护
    bool validateSignal(const SignalData& signal);
    bool validateAllSignals();
    std::vector<SignalID> findDuplicateSignals();
    bool removeDuplicates();
    
    // 统计和监控
    const ServiceStats& getStatistics() const { return m_stats; }
    JsonObject getDetailedStatistics(JsonDocument& doc) const;
    uint32_t getSignalCount() const;
    uint32_t getSignalCountByDevice(DeviceType deviceType) const;
    
    // 事件处理
    void setEventHandler(SignalEventHandler handler);
    
    // 配置管理
    bool updateConfig(const ServiceConfig& config);
    const ServiceConfig& getConfig() const { return m_config; }

private:
    bool validateSignalData(const SignalData& signal) const;
    bool checkDuplicateSignal(const SignalData& signal) const;
    SignalID generateUniqueId();
    void updateSignalUsage(SignalData& signal);
    void triggerEvent(const SignalData& signal, const String& event);
    bool shouldAutoSave() const;
    void performAutoSave();
};
