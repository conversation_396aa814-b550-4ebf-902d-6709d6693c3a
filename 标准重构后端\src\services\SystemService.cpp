#include "SystemService.h"
#include <ESP.h>
#include <LittleFS.h>

/**
 * ESP32-S3 红外控制系统 - 系统业务服务实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：业务逻辑层
 */

SystemService::SystemService(DataManager* dataManager)
    : m_dataManager(dataManager), m_initialized(false), m_startTime(0), m_lastMonitoringCheck(0) {}

SystemService::~SystemService() {
    cleanup();
}

bool SystemService::initialize(const ServiceConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) return true;
    
    m_config = config;
    m_startTime = millis();
    m_lastMonitoringCheck = m_startTime;
    
    initializeSystemStatus();
    
    if (m_config.enableLogging) {
        m_logEntries.reserve(m_config.maxLogEntries);
    }
    
    m_initialized = true;
    
    addLogEntry(LogLevel::INFO, "SystemService", "System service initialized successfully");
    Serial.println("✅ SystemService: Initialized successfully");
    return true;
}

void SystemService::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        addLogEntry(LogLevel::INFO, "SystemService", "System service shutting down");
        
        if (m_config.enableLogging) {
            exportLogs("/system_shutdown.log");
        }
        
        m_logEntries.clear();
        m_initialized = false;
        
        Serial.println("✅ SystemService: Cleanup completed");
    }
}

SystemService::SystemStatus SystemService::getSystemStatus() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_systemStatus;
}

bool SystemService::updateSystemStatus() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    m_systemStatus.uptime = millis() - m_startTime;
    updateMemoryInfo();
    updateCPUInfo();
    updateNetworkInfo();
    updateStorageInfo();
    
    return true;
}

bool SystemService::setSystemState(SystemState state) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    SystemState oldState = m_systemStatus.state;
    m_systemStatus.state = state;
    
    String stateMsg = "System state changed from " + String((int)oldState) + " to " + String((int)state);
    addLogEntry(LogLevel::INFO, "SystemService", stateMsg);
    triggerEvent("state_changed", stateMsg);
    
    return true;
}

SystemService::SystemState SystemService::getSystemState() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_systemStatus.state;
}

void SystemService::addLogEntry(LogLevel level, const String& component, const String& message) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_config.enableLogging) return;
    
    if (level < m_config.minLogLevel) return;
    
    LogEntry entry(level, component, message);
    m_logEntries.push_back(entry);
    
    m_stats.logEntriesCreated++;
    
    if (level == LogLevel::ERROR || level == LogLevel::CRITICAL) {
        m_stats.errorCount++;
        m_stats.lastError = millis();
        
        if (level == LogLevel::CRITICAL) {
            handleCriticalError(message);
        }
    } else if (level == LogLevel::WARNING) {
        m_stats.warningCount++;
    }
    
    // 输出到串口
    Serial.printf("[%s] %s: %s\n", logLevelToString(level).c_str(), 
                  component.c_str(), message.c_str());
    
    trimLogEntries();
}

std::vector<SystemService::LogEntry> SystemService::getLogEntries(LogLevel minLevel) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    std::vector<LogEntry> filteredLogs;
    
    for (const auto& entry : m_logEntries) {
        if (entry.level >= minLevel) {
            filteredLogs.push_back(entry);
        }
    }
    
    return filteredLogs;
}

std::vector<SystemService::LogEntry> SystemService::getRecentLogs(uint32_t count) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    std::vector<LogEntry> recentLogs;
    
    if (m_logEntries.size() <= count) {
        return m_logEntries;
    }
    
    auto startIt = m_logEntries.end() - count;
    recentLogs.assign(startIt, m_logEntries.end());
    
    return recentLogs;
}

bool SystemService::clearLogs() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    m_logEntries.clear();
    addLogEntry(LogLevel::INFO, "SystemService", "Log entries cleared");
    
    return true;
}

bool SystemService::exportLogs(const String& filename) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    File file = LittleFS.open(filename, "w");
    if (!file) {
        addLogEntry(LogLevel::ERROR, "SystemService", "Failed to open log file for export: " + filename);
        return false;
    }
    
    file.println("# System Log Export");
    file.println("# Generated at: " + String(millis()));
    file.println();
    
    for (const auto& entry : m_logEntries) {
        file.printf("[%lu] [%s] %s: %s\n", 
                   entry.timestamp,
                   logLevelToString(entry.level).c_str(),
                   entry.component.c_str(),
                   entry.message.c_str());
    }
    
    file.close();
    
    addLogEntry(LogLevel::INFO, "SystemService", "Logs exported to: " + filename);
    return true;
}

void SystemService::performSystemCheck() {
    if (!m_initialized) return;
    
    updateSystemStatus();
    
    bool memoryOk = checkMemoryUsage();
    bool storageOk = checkStorageHealth();
    bool networkOk = checkNetworkStatus();
    
    if (!memoryOk || !storageOk || !networkOk) {
        addLogEntry(LogLevel::WARNING, "SystemService", "System check detected issues");
        triggerEvent("system_check_warning", "Some system components have issues");
    } else {
        addLogEntry(LogLevel::DEBUG, "SystemService", "System check completed successfully");
    }
}

bool SystemService::checkMemoryUsage() {
    updateMemoryInfo();
    
    float memoryUsage = (float)(m_systemStatus.totalHeap - m_systemStatus.freeHeap) / m_systemStatus.totalHeap;
    
    if (memoryUsage > 0.9) { // 90%以上内存使用
        addLogEntry(LogLevel::ERROR, "SystemService", 
                   "Critical memory usage: " + String(memoryUsage * 100) + "%");
        return false;
    } else if (memoryUsage > 0.8) { // 80%以上内存使用
        addLogEntry(LogLevel::WARNING, "SystemService", 
                   "High memory usage: " + String(memoryUsage * 100) + "%");
    }
    
    return true;
}

bool SystemService::checkStorageHealth() {
    size_t totalBytes = LittleFS.totalBytes();
    size_t usedBytes = LittleFS.usedBytes();
    
    if (totalBytes == 0) {
        addLogEntry(LogLevel::ERROR, "SystemService", "Storage not available");
        m_systemStatus.storageHealthy = false;
        return false;
    }
    
    float storageUsage = (float)usedBytes / totalBytes;
    
    if (storageUsage > 0.95) { // 95%以上存储使用
        addLogEntry(LogLevel::ERROR, "SystemService", 
                   "Critical storage usage: " + String(storageUsage * 100) + "%");
        m_systemStatus.storageHealthy = false;
        return false;
    } else if (storageUsage > 0.85) { // 85%以上存储使用
        addLogEntry(LogLevel::WARNING, "SystemService", 
                   "High storage usage: " + String(storageUsage * 100) + "%");
    }
    
    m_systemStatus.storageHealthy = true;
    return true;
}

bool SystemService::checkNetworkStatus() {
    // 简化实现，实际项目中需要检查WiFi连接状态
    m_systemStatus.wifiSignalStrength = 75; // 模拟信号强度
    return true;
}

float SystemService::getCPUTemperature() {
    // ESP32-S3内置温度传感器读取
    // 这里使用模拟值，实际项目中需要读取真实温度
    return 45.5;
}

uint32_t SystemService::getFreeHeap() {
    return ESP.getFreeHeap();
}

uint32_t SystemService::getTotalHeap() {
    return ESP.getHeapSize();
}

bool SystemService::restartSystem() {
    addLogEntry(LogLevel::INFO, "SystemService", "System restart requested");
    
    m_stats.systemRestarts++;
    m_stats.lastRestart = millis();
    
    triggerEvent("system_restart", "System is restarting");
    
    delay(1000); // 给日志时间写入
    ESP.restart();
    
    return true; // 实际上不会执行到这里
}

bool SystemService::enterMaintenanceMode() {
    addLogEntry(LogLevel::INFO, "SystemService", "Entering maintenance mode");

    bool success = setSystemState(SystemState::MAINTENANCE);
    if (success) {
        triggerEvent("maintenance_mode_entered", "System is now in maintenance mode");
    }

    return success;
}

bool SystemService::exitMaintenanceMode() {
    addLogEntry(LogLevel::INFO, "SystemService", "Exiting maintenance mode");

    bool success = setSystemState(SystemState::RUNNING);
    if (success) {
        triggerEvent("maintenance_mode_exited", "System is now running normally");
    }

    return success;
}

bool SystemService::performFactoryReset() {
    addLogEntry(LogLevel::WARNING, "SystemService", "Factory reset initiated");

    if (!m_dataManager) return false;

    // 清除所有数据
    bool success = m_dataManager->clearAllData();

    if (success) {
        addLogEntry(LogLevel::INFO, "SystemService", "Factory reset completed");
        triggerEvent("factory_reset", "All data has been cleared");

        // 重启系统
        delay(2000);
        restartSystem();
    } else {
        addLogEntry(LogLevel::ERROR, "SystemService", "Factory reset failed");
    }

    return success;
}

bool SystemService::createSystemBackup() {
    addLogEntry(LogLevel::INFO, "SystemService", "Creating system backup");

    if (!m_dataManager) return false;

    String backupFile = "/backup_" + String(millis()) + ".json";
    bool success = m_dataManager->exportToJson(backupFile);

    if (success) {
        addLogEntry(LogLevel::INFO, "SystemService", "System backup created: " + backupFile);
        triggerEvent("backup_created", backupFile);
    } else {
        addLogEntry(LogLevel::ERROR, "SystemService", "Failed to create system backup");
    }

    return success;
}

bool SystemService::restoreFromBackup(const String& backupFile) {
    addLogEntry(LogLevel::INFO, "SystemService", "Restoring from backup: " + backupFile);

    if (!m_dataManager) return false;

    bool success = m_dataManager->importFromJson(backupFile);

    if (success) {
        addLogEntry(LogLevel::INFO, "SystemService", "System restored from backup successfully");
        triggerEvent("backup_restored", backupFile);
    } else {
        addLogEntry(LogLevel::ERROR, "SystemService", "Failed to restore from backup");
    }

    return success;
}

bool SystemService::saveSystemConfig() {
    addLogEntry(LogLevel::DEBUG, "SystemService", "Saving system configuration");

    // 这里应该保存系统配置到文件
    // 简化实现
    return true;
}

bool SystemService::loadSystemConfig() {
    addLogEntry(LogLevel::DEBUG, "SystemService", "Loading system configuration");

    // 这里应该从文件加载系统配置
    // 简化实现
    return true;
}

bool SystemService::resetToDefaults() {
    addLogEntry(LogLevel::INFO, "SystemService", "Resetting configuration to defaults");

    m_config = ServiceConfig();

    triggerEvent("config_reset", "Configuration reset to defaults");
    return true;
}

JsonObject SystemService::getDetailedStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("system_service_statistics");
    stats["initialized"] = m_initialized;
    stats["log_entries_created"] = m_stats.logEntriesCreated;
    stats["system_restarts"] = m_stats.systemRestarts;
    stats["error_count"] = m_stats.errorCount;
    stats["warning_count"] = m_stats.warningCount;
    stats["last_error"] = m_stats.lastError;
    stats["last_restart"] = m_stats.lastRestart;

    stats["current_log_entries"] = m_logEntries.size();
    stats["uptime"] = m_systemStatus.uptime;

    JsonObject config = stats.createNestedObject("config");
    config["enable_logging"] = m_config.enableLogging;
    config["max_log_entries"] = m_config.maxLogEntries;
    config["min_log_level"] = (int)m_config.minLogLevel;
    config["enable_monitoring"] = m_config.enableMonitoring;
    config["monitoring_interval"] = m_config.monitoringInterval;
    config["enable_auto_restart"] = m_config.enableAutoRestart;
    config["restart_threshold"] = m_config.restartThreshold;

    return stats;
}

JsonObject SystemService::getSystemInfo(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject info = doc.createNestedObject("system_info");
    info["state"] = (int)m_systemStatus.state;
    info["uptime"] = m_systemStatus.uptime;
    info["free_heap"] = m_systemStatus.freeHeap;
    info["total_heap"] = m_systemStatus.totalHeap;
    info["cpu_usage"] = m_systemStatus.cpuUsage;
    info["temperature"] = m_systemStatus.temperature;
    info["wifi_signal_strength"] = m_systemStatus.wifiSignalStrength;
    info["storage_healthy"] = m_systemStatus.storageHealthy;

    // ESP32特定信息
    info["chip_model"] = ESP.getChipModel();
    info["chip_revision"] = ESP.getChipRevision();
    info["cpu_freq_mhz"] = ESP.getCpuFreqMHz();
    info["flash_size"] = ESP.getFlashChipSize();
    info["sketch_size"] = ESP.getSketchSize();
    info["free_sketch_space"] = ESP.getFreeSketchSpace();

    return info;
}

void SystemService::setEventHandler(SystemEventHandler handler) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_eventHandler = handler;
}

bool SystemService::updateConfig(const ServiceConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_config = config;
    addLogEntry(LogLevel::INFO, "SystemService", "Configuration updated");

    return true;
}

void SystemService::processSystemMonitoring() {
    if (!m_initialized || !shouldPerformMonitoring()) return;

    m_lastMonitoringCheck = millis();
    performSystemCheck();
}

// ==================== 私有方法实现 ====================

void SystemService::initializeSystemStatus() {
    m_systemStatus.state = SystemState::INITIALIZING;
    m_systemStatus.uptime = 0;
    updateMemoryInfo();
    updateCPUInfo();
    updateNetworkInfo();
    updateStorageInfo();
}

void SystemService::updateMemoryInfo() {
    m_systemStatus.freeHeap = ESP.getFreeHeap();
    m_systemStatus.totalHeap = ESP.getHeapSize();
}

void SystemService::updateCPUInfo() {
    m_systemStatus.cpuUsage = 50; // 模拟CPU使用率
    m_systemStatus.temperature = getCPUTemperature();
}

void SystemService::updateNetworkInfo() {
    // 简化实现，实际项目中需要检查WiFi状态
    m_systemStatus.wifiSignalStrength = 75;
}

void SystemService::updateStorageInfo() {
    checkStorageHealth(); // 这个方法会更新storageHealthy状态
}

void SystemService::trimLogEntries() {
    if (m_logEntries.size() > m_config.maxLogEntries) {
        size_t removeCount = m_logEntries.size() - m_config.maxLogEntries;
        m_logEntries.erase(m_logEntries.begin(), m_logEntries.begin() + removeCount);
    }
}

void SystemService::triggerEvent(const String& event, const String& data) {
    if (m_eventHandler) {
        m_eventHandler(event, data);
    }
}

String SystemService::logLevelToString(LogLevel level) const {
    switch (level) {
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARNING";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

SystemService::LogLevel SystemService::stringToLogLevel(const String& str) const {
    if (str == "DEBUG") return LogLevel::DEBUG;
    if (str == "INFO") return LogLevel::INFO;
    if (str == "WARNING") return LogLevel::WARNING;
    if (str == "ERROR") return LogLevel::ERROR;
    if (str == "CRITICAL") return LogLevel::CRITICAL;
    return LogLevel::INFO;
}

bool SystemService::shouldPerformMonitoring() const {
    return m_config.enableMonitoring &&
           (millis() - m_lastMonitoringCheck) >= m_config.monitoringInterval;
}

void SystemService::handleCriticalError(const String& error) {
    triggerEvent("critical_error", error);

    if (m_config.enableAutoRestart && m_stats.errorCount >= m_config.restartThreshold) {
        addLogEntry(LogLevel::CRITICAL, "SystemService",
                   "Error threshold reached, initiating auto-restart");

        delay(5000); // 给系统时间处理
        restartSystem();
    }
}
