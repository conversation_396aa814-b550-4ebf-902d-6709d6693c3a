#pragma once

#include "../core/DataStructures.h"
#include "../data/DataManager.h"
#include <vector>
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - 系统业务服务
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：业务逻辑层
 */

class SystemService {
public:
    enum class LogLevel : uint8_t {
        DEBUG = 0, INFO = 1, WARNING = 2, ERROR = 3, CRITICAL = 4
    };
    
    struct LogEntry {
        Timestamp timestamp;
        LogLevel level;
        String component;
        String message;
        
        LogEntry() : timestamp(0), level(LogLevel::INFO) {}
        LogEntry(LogLevel lvl, const String& comp, const String& msg) 
            : timestamp(millis()), level(lvl), component(comp), message(msg) {}
    };
    
    struct SystemStatus {
        SystemState state;
        Timestamp uptime;
        uint32_t freeHeap;
        uint32_t totalHeap;
        uint8_t cpuUsage;
        float temperature;
        uint32_t wifiSignalStrength;
        bool storageHealthy;
        
        SystemStatus() : state(SystemState::INITIALIZING), uptime(0), freeHeap(0),
                        totalHeap(0), cpuUsage(0), temperature(0.0), 
                        wifiSignalStrength(0), storageHealthy(true) {}
    };
    
    struct ServiceConfig {
        bool enableLogging;
        uint32_t maxLogEntries;
        LogLevel minLogLevel;
        bool enableMonitoring;
        uint32_t monitoringInterval;
        bool enableAutoRestart;
        uint32_t restartThreshold;
        
        ServiceConfig() : enableLogging(true), maxLogEntries(1000), minLogLevel(LogLevel::INFO),
                         enableMonitoring(true), monitoringInterval(5000), enableAutoRestart(false),
                         restartThreshold(10) {}
    };
    
    struct ServiceStats {
        uint32_t logEntriesCreated;
        uint32_t systemRestarts;
        uint32_t errorCount;
        uint32_t warningCount;
        Timestamp lastError;
        Timestamp lastRestart;
        
        ServiceStats() : logEntriesCreated(0), systemRestarts(0), errorCount(0),
                        warningCount(0), lastError(0), lastRestart(0) {}
    };
    
    using SystemEventHandler = std::function<void(const String& event, const String& data)>;

private:
    DataManager* m_dataManager;
    ServiceConfig m_config;
    bool m_initialized;
    ServiceStats m_stats;
    SystemStatus m_systemStatus;
    std::vector<LogEntry> m_logEntries;
    SystemEventHandler m_eventHandler;
    Timestamp m_startTime;
    Timestamp m_lastMonitoringCheck;
    mutable std::mutex m_mutex;

public:
    SystemService(DataManager* dataManager);
    ~SystemService();
    
    bool initialize(const ServiceConfig& config = ServiceConfig());
    void cleanup();
    bool isInitialized() const { return m_initialized; }
    
    // 系统状态管理
    SystemStatus getSystemStatus() const;
    bool updateSystemStatus();
    bool setSystemState(SystemState state);
    SystemState getSystemState() const;
    
    // 日志管理
    void addLogEntry(LogLevel level, const String& component, const String& message);
    std::vector<LogEntry> getLogEntries(LogLevel minLevel = LogLevel::DEBUG) const;
    std::vector<LogEntry> getRecentLogs(uint32_t count = 100) const;
    bool clearLogs();
    bool exportLogs(const String& filename);
    
    // 系统监控
    void performSystemCheck();
    bool checkMemoryUsage();
    bool checkStorageHealth();
    bool checkNetworkStatus();
    float getCPUTemperature();
    uint32_t getFreeHeap();
    uint32_t getTotalHeap();
    
    // 系统控制
    bool restartSystem();
    bool enterMaintenanceMode();
    bool exitMaintenanceMode();
    bool performFactoryReset();
    bool createSystemBackup();
    bool restoreFromBackup(const String& backupFile);
    
    // 配置管理
    bool saveSystemConfig();
    bool loadSystemConfig();
    bool resetToDefaults();
    
    // 统计和监控
    const ServiceStats& getStatistics() const { return m_stats; }
    JsonObject getDetailedStatistics(JsonDocument& doc) const;
    JsonObject getSystemInfo(JsonDocument& doc) const;
    
    // 事件处理
    void setEventHandler(SystemEventHandler handler);
    
    // 配置管理
    bool updateConfig(const ServiceConfig& config);
    const ServiceConfig& getConfig() const { return m_config; }
    
    // 定期处理
    void processSystemMonitoring();

private:
    void initializeSystemStatus();
    void updateMemoryInfo();
    void updateCPUInfo();
    void updateNetworkInfo();
    void updateStorageInfo();
    void trimLogEntries();
    void triggerEvent(const String& event, const String& data = "");
    String logLevelToString(LogLevel level) const;
    LogLevel stringToLogLevel(const String& str) const;
    bool shouldPerformMonitoring() const;
    void handleCriticalError(const String& error);
};
