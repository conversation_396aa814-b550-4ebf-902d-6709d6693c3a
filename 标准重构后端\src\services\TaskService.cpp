#include "TaskService.h"
#include "../core/IDGenerator.h"

/**
 * ESP32-S3 红外控制系统 - 任务业务服务实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：业务逻辑层
 */

TaskService::TaskService(DataManager* dataManager, SignalService* signalService)
    : m_dataManager(dataManager), m_signalService(signalService), m_initialized(false) {}

TaskService::~TaskService() {
    cleanup();
}

bool TaskService::initialize(const ServiceConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) return true;
    
    if (!m_dataManager || !m_signalService) {
        Serial.println("❌ TaskService: Missing dependencies");
        return false;
    }
    
    if (!m_dataManager->isInitialized() || !m_signalService->isInitialized()) {
        Serial.println("❌ TaskService: Dependencies not initialized");
        return false;
    }
    
    m_config = config;
    m_executionQueue.reserve(m_config.maxQueueSize);
    m_initialized = true;
    
    Serial.println("✅ TaskService: Initialized successfully");
    return true;
}

void TaskService::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        m_executionQueue.clear();
        m_initialized = false;
        
        Serial.println("✅ TaskService: Cleanup completed");
    }
}

Result<TaskData> TaskService::createTask(const TaskData& task) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<TaskData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    if (!validateTaskData(task)) {
        return Result<TaskData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid task data");
    }
    
    TaskData newTask = task;
    if (newTask.id == INVALID_ID) {
        newTask.id = generateUniqueId();
    }
    
    newTask.createdTime = millis();
    newTask.modifiedTime = newTask.createdTime;
    
    auto result = m_dataManager->getTaskRepository().create(newTask);
    if (result.isSuccess()) {
        m_stats.tasksCreated++;
        triggerEvent(newTask, "created");
    }
    
    return result;
}

Result<TaskData> TaskService::getTask(TaskID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<TaskData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    return m_dataManager->getTaskRepository().getById(id);
}

Result<TaskData> TaskService::updateTask(const TaskData& task) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<TaskData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    if (!validateTaskData(task)) {
        return Result<TaskData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid task data");
    }
    
    TaskData updatedTask = task;
    updatedTask.modifiedTime = millis();
    
    auto result = m_dataManager->getTaskRepository().update(updatedTask);
    if (result.isSuccess()) {
        triggerEvent(updatedTask, "updated");
    }
    
    return result;
}

bool TaskService::deleteTask(TaskID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto taskResult = m_dataManager->getTaskRepository().getById(id);
    if (!taskResult.isSuccess()) return false;
    
    // 从执行队列中移除
    removeFromQueue(id);
    
    bool success = m_dataManager->getTaskRepository().deleteById(id);
    if (success) {
        triggerEvent(taskResult.getValue(), "deleted");
    }
    
    return success;
}

std::vector<TaskData> TaskService::getAllTasks() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getTaskRepository().getAll();
}

bool TaskService::executeTask(TaskID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getTaskRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    TaskData task = result.getValue();
    return executeTaskData(task);
}

bool TaskService::executeTaskData(const TaskData& task) {
    if (!canExecuteTask(task)) return false;
    
    TaskData executingTask = task;
    executingTask.status = TaskStatus::RUNNING;
    executingTask.lastExecuted = millis();
    
    m_dataManager->getTaskRepository().update(executingTask);
    triggerEvent(executingTask, "started");
    
    bool success = executeTaskSignals(executingTask);
    
    updateTaskExecution(executingTask, success);
    m_dataManager->getTaskRepository().update(executingTask);
    
    m_stats.tasksExecuted++;
    if (success) {
        m_stats.tasksCompleted++;
    } else {
        m_stats.tasksFailed++;
    }
    m_stats.lastExecution = millis();
    
    triggerEvent(executingTask, success ? "completed" : "failed");
    
    return success;
}

bool TaskService::scheduleTask(TaskID id, Timestamp executeTime) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getTaskRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    TaskData task = result.getValue();
    task.nextExecution = executeTime;
    task.status = TaskStatus::PENDING;
    
    auto updateResult = m_dataManager->getTaskRepository().update(task);
    if (updateResult.isSuccess()) {
        triggerEvent(task, "scheduled");
        return true;
    }
    
    return false;
}

bool TaskService::cancelTask(TaskID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getTaskRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    TaskData task = result.getValue();
    task.status = TaskStatus::CANCELLED;
    
    removeFromQueue(id);
    
    auto updateResult = m_dataManager->getTaskRepository().update(task);
    if (updateResult.isSuccess()) {
        triggerEvent(task, "cancelled");
        return true;
    }
    
    return false;
}

bool TaskService::pauseTask(TaskID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getTaskRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    TaskData task = result.getValue();
    task.status = TaskStatus::PAUSED;
    
    auto updateResult = m_dataManager->getTaskRepository().update(task);
    if (updateResult.isSuccess()) {
        triggerEvent(task, "paused");
        return true;
    }
    
    return false;
}

bool TaskService::resumeTask(TaskID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getTaskRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    TaskData task = result.getValue();
    if (task.status == TaskStatus::PAUSED) {
        task.status = TaskStatus::PENDING;
        
        auto updateResult = m_dataManager->getTaskRepository().update(task);
        if (updateResult.isSuccess()) {
            triggerEvent(task, "resumed");
            return true;
        }
    }
    
    return false;
}

bool TaskService::addToQueue(TaskID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_config.enableTaskQueue) return false;
    
    if (m_executionQueue.size() >= m_config.maxQueueSize) return false;
    
    // 检查是否已在队列中
    auto it = std::find(m_executionQueue.begin(), m_executionQueue.end(), id);
    if (it != m_executionQueue.end()) return false;
    
    m_executionQueue.push_back(id);
    return true;
}

bool TaskService::removeFromQueue(TaskID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = std::find(m_executionQueue.begin(), m_executionQueue.end(), id);
    if (it != m_executionQueue.end()) {
        m_executionQueue.erase(it);
        return true;
    }
    
    return false;
}

std::vector<TaskID> TaskService::getExecutionQueue() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_executionQueue;
}

bool TaskService::clearQueue() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_executionQueue.clear();
    return true;
}

std::vector<TaskData> TaskService::findTasksByStatus(TaskStatus status) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return {};

    return m_dataManager->getTaskRepository().findWhere(
        [status](const TaskData& task) {
            return task.status == status;
        }
    );
}

std::vector<TaskData> TaskService::findTasksByType(TaskType type) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return {};

    return m_dataManager->getTaskRepository().findWhere(
        [type](const TaskData& task) {
            return task.type == type;
        }
    );
}

std::vector<TaskData> TaskService::findTasksByPriority(Priority priority) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return {};

    return m_dataManager->getTaskRepository().findWhere(
        [priority](const TaskData& task) {
            return task.priority == priority;
        }
    );
}

std::vector<TaskData> TaskService::getScheduledTasks() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return {};

    return m_dataManager->getTaskRepository().findWhere(
        [](const TaskData& task) {
            return task.nextExecution > 0 && task.status == TaskStatus::PENDING;
        }
    );
}

std::vector<TaskData> TaskService::getRepeatingTasks() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return {};

    return m_dataManager->getTaskRepository().findWhere(
        [](const TaskData& task) {
            return task.type == TaskType::REPEATED && task.interval > 0;
        }
    );
}

bool TaskService::executeMultipleTasks(const std::vector<TaskID>& taskIds) {
    bool allSuccess = true;

    for (TaskID id : taskIds) {
        if (!executeTask(id)) {
            allSuccess = false;
        }
    }

    return allSuccess;
}

bool TaskService::pauseAllTasks() {
    auto tasks = getAllTasks();
    bool allSuccess = true;

    for (const auto& task : tasks) {
        if (task.status == TaskStatus::PENDING || task.status == TaskStatus::RUNNING) {
            if (!pauseTask(task.id)) {
                allSuccess = false;
            }
        }
    }

    return allSuccess;
}

bool TaskService::resumeAllTasks() {
    auto tasks = getAllTasks();
    bool allSuccess = true;

    for (const auto& task : tasks) {
        if (task.status == TaskStatus::PAUSED) {
            if (!resumeTask(task.id)) {
                allSuccess = false;
            }
        }
    }

    return allSuccess;
}

bool TaskService::clearCompletedTasks() {
    auto completedTasks = findTasksByStatus(TaskStatus::COMPLETED);
    bool allSuccess = true;

    for (const auto& task : completedTasks) {
        if (!deleteTask(task.id)) {
            allSuccess = false;
        }
    }

    return allSuccess;
}

bool TaskService::validateTask(const TaskData& task) {
    return validateTaskData(task);
}

bool TaskService::validateAllTasks() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    auto tasks = m_dataManager->getTaskRepository().getAll();

    for (const auto& task : tasks) {
        if (!validateTaskData(task)) {
            Serial.printf("❌ Invalid task found: ID=%d, Name=%s\n", task.id, task.name.c_str());
            return false;
        }
    }

    return true;
}

bool TaskService::updateTaskStatuses() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    auto tasks = m_dataManager->getTaskRepository().getAll();
    bool updated = false;

    for (auto& task : tasks) {
        TaskStatus oldStatus = task.status;

        // 检查是否需要更新状态
        if (task.status == TaskStatus::RUNNING) {
            // 检查运行中的任务是否超时
            if (millis() - task.lastExecuted > 60000) { // 1分钟超时
                task.status = TaskStatus::FAILED;
                updated = true;
            }
        } else if (task.status == TaskStatus::PENDING && task.maxExecutions > 0) {
            // 检查是否达到最大执行次数
            if (task.executionCount >= task.maxExecutions) {
                task.status = TaskStatus::COMPLETED;
                updated = true;
            }
        }

        if (task.status != oldStatus) {
            m_dataManager->getTaskRepository().update(task);
            triggerEvent(task, "status_updated");
        }
    }

    return updated;
}

JsonObject TaskService::getDetailedStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("task_service_statistics");
    stats["initialized"] = m_initialized;
    stats["tasks_created"] = m_stats.tasksCreated;
    stats["tasks_executed"] = m_stats.tasksExecuted;
    stats["tasks_completed"] = m_stats.tasksCompleted;
    stats["tasks_failed"] = m_stats.tasksFailed;
    stats["retry_attempts"] = m_stats.retryAttempts;
    stats["last_execution"] = m_stats.lastExecution;

    stats["total_tasks"] = getTaskCount();
    stats["queue_size"] = m_executionQueue.size();

    JsonObject config = stats.createNestedObject("config");
    config["enable_auto_execution"] = m_config.enableAutoExecution;
    config["execution_check_interval"] = m_config.executionCheckInterval;
    config["enable_task_queue"] = m_config.enableTaskQueue;
    config["max_queue_size"] = m_config.maxQueueSize;
    config["enable_retry"] = m_config.enableRetry;
    config["max_retry_attempts"] = m_config.maxRetryAttempts;

    return stats;
}

uint32_t TaskService::getTaskCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return 0;

    return m_dataManager->getTaskRepository().count();
}

uint32_t TaskService::getTaskCountByStatus(TaskStatus status) const {
    auto tasks = findTasksByStatus(status);
    return tasks.size();
}

void TaskService::setEventHandler(TaskEventHandler handler) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_eventHandler = handler;
}

bool TaskService::updateConfig(const ServiceConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_config = config;
    return true;
}

void TaskService::processScheduledTasks() {
    if (!m_initialized || !m_config.enableAutoExecution) return;

    auto scheduledTasks = getScheduledTasks();
    Timestamp currentTime = millis();

    for (const auto& task : scheduledTasks) {
        if (task.nextExecution <= currentTime) {
            if (m_config.enableTaskQueue) {
                addToQueue(task.id);
            } else {
                executeTask(task.id);
            }
        }
    }
}

void TaskService::processRepeatingTasks() {
    if (!m_initialized || !m_config.enableAutoExecution) return;

    auto repeatingTasks = getRepeatingTasks();
    Timestamp currentTime = millis();

    for (auto task : repeatingTasks) {
        if (task.nextExecution <= currentTime && task.canExecute()) {
            if (m_config.enableTaskQueue) {
                addToQueue(task.id);
            } else {
                executeTask(task.id);
            }

            // 安排下次执行
            scheduleNextExecution(task);
            m_dataManager->getTaskRepository().update(task);
        }
    }
}

void TaskService::processExecutionQueue() {
    if (!m_initialized || !m_config.enableTaskQueue || m_executionQueue.empty()) return;

    TaskID taskId = m_executionQueue.front();
    m_executionQueue.erase(m_executionQueue.begin());

    executeTask(taskId);
}

// ==================== 私有方法实现 ====================

bool TaskService::validateTaskData(const TaskData& task) const {
    return task.isValid();
}

TaskID TaskService::generateUniqueId() {
    return GENERATE_TASK_ID();
}

bool TaskService::canExecuteTask(const TaskData& task) const {
    return task.status == TaskStatus::PENDING && task.canExecute();
}

bool TaskService::executeTaskSignals(const TaskData& task) {
    if (!m_signalService) return false;

    bool allSuccess = true;

    for (SignalID signalId : task.signals) {
        if (!m_signalService->sendSignal(signalId)) {
            allSuccess = false;
            if (!m_config.enableRetry) break;
        }

        // 信号间延迟
        if (task.signalDelay > 0) {
            delay(task.signalDelay);
        }
    }

    return allSuccess;
}

void TaskService::updateTaskExecution(TaskData& task, bool success) {
    task.executionCount++;

    if (success) {
        task.status = TaskStatus::COMPLETED;

        // 如果是重复任务，安排下次执行
        if (task.type == TaskType::REPEATED && task.interval > 0) {
            scheduleNextExecution(task);
            task.status = TaskStatus::PENDING;
        }
    } else {
        if (shouldRetryTask(task)) {
            task.status = TaskStatus::PENDING;
            m_stats.retryAttempts++;
        } else {
            task.status = TaskStatus::FAILED;
        }
    }
}

void TaskService::triggerEvent(const TaskData& task, const String& event) {
    if (m_eventHandler) {
        m_eventHandler(task, event);
    }
}

bool TaskService::shouldRetryTask(const TaskData& task) const {
    return m_config.enableRetry &&
           task.executionCount < m_config.maxRetryAttempts;
}

void TaskService::scheduleNextExecution(TaskData& task) {
    if (task.interval > 0) {
        task.nextExecution = millis() + task.interval;
    }
}

bool TaskService::isTaskScheduled(const TaskData& task) const {
    return task.nextExecution > 0 && task.nextExecution > millis();
}
