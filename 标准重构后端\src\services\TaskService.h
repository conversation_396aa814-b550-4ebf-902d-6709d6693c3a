#pragma once

#include "../core/DataStructures.h"
#include "../data/DataManager.h"
#include "SignalService.h"
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - 任务业务服务
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：业务逻辑层
 */

class TaskService {
public:
    struct ServiceConfig {
        bool enableAutoExecution;
        uint32_t executionCheckInterval;
        bool enableTaskQueue;
        uint32_t maxQueueSize;
        bool enableRetry;
        uint32_t maxRetryAttempts;
        
        ServiceConfig() : enableAutoExecution(true), executionCheckInterval(1000),
                         enableTaskQueue(true), maxQueueSize(50), enableRetry(true),
                         maxRetryAttempts(3) {}
    };
    
    struct ServiceStats {
        uint32_t tasksCreated;
        uint32_t tasksExecuted;
        uint32_t tasksCompleted;
        uint32_t tasksFailed;
        uint32_t retryAttempts;
        Timestamp lastExecution;
        
        ServiceStats() : tasksCreated(0), tasksExecuted(0), tasksCompleted(0),
                        tasksFailed(0), retryAttempts(0), lastExecution(0) {}
    };
    
    using TaskEventHandler = std::function<void(const TaskData& task, const String& event)>;

private:
    DataManager* m_dataManager;
    SignalService* m_signalService;
    ServiceConfig m_config;
    bool m_initialized;
    ServiceStats m_stats;
    TaskEventHandler m_eventHandler;
    std::vector<TaskID> m_executionQueue;
    mutable std::mutex m_mutex;

public:
    TaskService(DataManager* dataManager, SignalService* signalService);
    ~TaskService();
    
    bool initialize(const ServiceConfig& config = ServiceConfig());
    void cleanup();
    bool isInitialized() const { return m_initialized; }
    
    // 任务管理
    Result<TaskData> createTask(const TaskData& task);
    Result<TaskData> getTask(TaskID id);
    Result<TaskData> updateTask(const TaskData& task);
    bool deleteTask(TaskID id);
    std::vector<TaskData> getAllTasks();
    
    // 任务执行
    bool executeTask(TaskID id);
    bool executeTaskData(const TaskData& task);
    bool scheduleTask(TaskID id, Timestamp executeTime);
    bool cancelTask(TaskID id);
    bool pauseTask(TaskID id);
    bool resumeTask(TaskID id);
    
    // 任务队列管理
    bool addToQueue(TaskID id);
    bool removeFromQueue(TaskID id);
    std::vector<TaskID> getExecutionQueue() const;
    bool clearQueue();
    
    // 查询和过滤
    std::vector<TaskData> findTasksByStatus(TaskStatus status);
    std::vector<TaskData> findTasksByType(TaskType type);
    std::vector<TaskData> findTasksByPriority(Priority priority);
    std::vector<TaskData> getScheduledTasks();
    std::vector<TaskData> getRepeatingTasks();
    
    // 批量操作
    bool executeMultipleTasks(const std::vector<TaskID>& taskIds);
    bool pauseAllTasks();
    bool resumeAllTasks();
    bool clearCompletedTasks();
    
    // 验证和维护
    bool validateTask(const TaskData& task);
    bool validateAllTasks();
    bool updateTaskStatuses();
    
    // 统计和监控
    const ServiceStats& getStatistics() const { return m_stats; }
    JsonObject getDetailedStatistics(JsonDocument& doc) const;
    uint32_t getTaskCount() const;
    uint32_t getTaskCountByStatus(TaskStatus status) const;
    
    // 事件处理
    void setEventHandler(TaskEventHandler handler);
    
    // 配置管理
    bool updateConfig(const ServiceConfig& config);
    const ServiceConfig& getConfig() const { return m_config; }
    
    // 定期处理
    void processScheduledTasks();
    void processRepeatingTasks();
    void processExecutionQueue();

private:
    bool validateTaskData(const TaskData& task) const;
    TaskID generateUniqueId();
    bool canExecuteTask(const TaskData& task) const;
    bool executeTaskSignals(const TaskData& task);
    void updateTaskExecution(TaskData& task, bool success);
    void triggerEvent(const TaskData& task, const String& event);
    bool shouldRetryTask(const TaskData& task) const;
    void scheduleNextExecution(TaskData& task);
    bool isTaskScheduled(const TaskData& task) const;
};
