#include "TimerService.h"
#include "../core/IDGenerator.h"
#include <time.h>

/**
 * ESP32-S3 红外控制系统 - 定时器业务服务实现
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：业务逻辑层
 */

TimerService::TimerService(DataManager* dataManager, TaskService* taskService)
    : m_dataManager(dataManager), m_taskService(taskService), m_initialized(false), m_lastCheckTime(0) {}

TimerService::~TimerService() {
    cleanup();
}

bool TimerService::initialize(const ServiceConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) return true;
    
    if (!m_dataManager || !m_taskService) {
        Serial.println("❌ TimerService: Missing dependencies");
        return false;
    }
    
    if (!m_dataManager->isInitialized() || !m_taskService->isInitialized()) {
        Serial.println("❌ TimerService: Dependencies not initialized");
        return false;
    }
    
    m_config = config;
    m_lastCheckTime = millis();
    m_initialized = true;
    
    Serial.println("✅ TimerService: Initialized successfully");
    return true;
}

void TimerService::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        m_initialized = false;
        Serial.println("✅ TimerService: Cleanup completed");
    }
}

Result<TimerData> TimerService::createTimer(const TimerData& timer) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<TimerData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    if (!validateTimerData(timer)) {
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid timer data");
    }
    
    if (m_dataManager->getTimerRepository().count() >= m_config.maxTimers) {
        return Result<TimerData>::Error(ErrorCode::RESOURCE_BUSY, "Maximum timer limit reached");
    }
    
    TimerData newTimer = timer;
    if (newTimer.id == INVALID_ID) {
        newTimer.id = generateUniqueId();
    }
    
    newTimer.createdTime = millis();
    newTimer.modifiedTime = newTimer.createdTime;
    newTimer.nextTriggerTime = calculateNextTriggerTime(newTimer);
    
    auto result = m_dataManager->getTimerRepository().create(newTimer);
    if (result.isSuccess()) {
        m_stats.timersCreated++;
        triggerEvent(newTimer, "created");
    }
    
    return result;
}

Result<TimerData> TimerService::getTimer(TimerID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<TimerData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    return m_dataManager->getTimerRepository().getById(id);
}

Result<TimerData> TimerService::updateTimer(const TimerData& timer) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<TimerData>::Error(ErrorCode::SYSTEM_NOT_READY, "Service not initialized");
    }
    
    if (!validateTimerData(timer)) {
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid timer data");
    }
    
    TimerData updatedTimer = timer;
    updatedTimer.modifiedTime = millis();
    updatedTimer.nextTriggerTime = calculateNextTriggerTime(updatedTimer);
    
    auto result = m_dataManager->getTimerRepository().update(updatedTimer);
    if (result.isSuccess()) {
        triggerEvent(updatedTimer, "updated");
    }
    
    return result;
}

bool TimerService::deleteTimer(TimerID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto timerResult = m_dataManager->getTimerRepository().getById(id);
    if (!timerResult.isSuccess()) return false;
    
    bool success = m_dataManager->getTimerRepository().deleteById(id);
    if (success) {
        m_stats.timersDeleted++;
        triggerEvent(timerResult.getValue(), "deleted");
    }
    
    return success;
}

std::vector<TimerData> TimerService::getAllTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getTimerRepository().getAll();
}

bool TimerService::enableTimer(TimerID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getTimerRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    TimerData timer = result.getValue();
    timer.enabled = true;
    timer.nextTriggerTime = calculateNextTriggerTime(timer);
    
    auto updateResult = m_dataManager->getTimerRepository().update(timer);
    if (updateResult.isSuccess()) {
        triggerEvent(timer, "enabled");
        return true;
    }
    
    return false;
}

bool TimerService::disableTimer(TimerID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getTimerRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    TimerData timer = result.getValue();
    timer.enabled = false;
    timer.nextTriggerTime = 0;
    
    auto updateResult = m_dataManager->getTimerRepository().update(timer);
    if (updateResult.isSuccess()) {
        triggerEvent(timer, "disabled");
        return true;
    }
    
    return false;
}

bool TimerService::triggerTimer(TimerID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getTimerRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    TimerData timer = result.getValue();
    bool success = executeTimerTask(timer);
    
    if (success) {
        updateTimerTrigger(timer);
        m_dataManager->getTimerRepository().update(timer);
        
        m_stats.timersTriggered++;
        m_stats.lastTrigger = millis();
        
        triggerEvent(timer, "triggered");
    }
    
    return success;
}

bool TimerService::resetTimer(TimerID id) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return false;
    
    auto result = m_dataManager->getTimerRepository().getById(id);
    if (!result.isSuccess()) return false;
    
    TimerData timer = result.getValue();
    timer.triggerCount = 0;
    timer.lastTriggerTime = 0;
    timer.nextTriggerTime = calculateNextTriggerTime(timer);
    
    auto updateResult = m_dataManager->getTimerRepository().update(timer);
    if (updateResult.isSuccess()) {
        triggerEvent(timer, "reset");
        return true;
    }
    
    return false;
}

std::vector<TimerData> TimerService::getEnabledTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getTimerRepository().findWhere(
        [](const TimerData& timer) {
            return timer.enabled;
        }
    );
}

std::vector<TimerData> TimerService::getDisabledTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getTimerRepository().findWhere(
        [](const TimerData& timer) {
            return !timer.enabled;
        }
    );
}

std::vector<TimerData> TimerService::getRepeatingTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getTimerRepository().findWhere(
        [](const TimerData& timer) {
            return timer.isRepeating;
        }
    );
}

std::vector<TimerData> TimerService::getOneTimeTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getTimerRepository().findWhere(
        [](const TimerData& timer) {
            return !timer.isRepeating;
        }
    );
}

std::vector<TimerData> TimerService::getTimersByTask(TaskID taskId) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    return m_dataManager->getTimerRepository().findWhere(
        [taskId](const TimerData& timer) {
            return timer.taskId == taskId;
        }
    );
}

std::vector<TimerData> TimerService::getTimersForToday() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) return {};
    
    uint8_t today = getCurrentWeekday();
    uint8_t todayMask = 1 << today;
    
    return m_dataManager->getTimerRepository().findWhere(
        [todayMask](const TimerData& timer) {
            return timer.enabled && (timer.weekdays & todayMask);
        }
    );
}

std::vector<TimerData> TimerService::getUpcomingTimers(uint32_t hours) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return {};

    Timestamp currentTime = millis();
    Timestamp endTime = currentTime + (hours * 3600000); // 转换为毫秒

    return m_dataManager->getTimerRepository().findWhere(
        [currentTime, endTime](const TimerData& timer) {
            return timer.enabled && timer.nextTriggerTime > currentTime &&
                   timer.nextTriggerTime <= endTime;
        }
    );
}

bool TimerService::setCurrentTime(uint8_t hour, uint8_t minute, uint8_t second) {
    if (hour > 23 || minute > 59 || second > 59) return false;

    // 这里应该设置系统时间，但ESP32没有RTC，使用millis()作为基准
    // 实际项目中可能需要外部RTC模块
    return true;
}

bool TimerService::setCurrentDate(uint16_t year, uint8_t month, uint8_t day) {
    if (year < 2000 || year > 2100 || month < 1 || month > 12 || day < 1 || day > 31) {
        return false;
    }

    // 设置系统日期
    return true;
}

Timestamp TimerService::getCurrentTime() const {
    return getSystemTime();
}

String TimerService::getFormattedTime() const {
    Timestamp currentTime = getCurrentTime();
    uint32_t seconds = (currentTime / 1000) % 86400; // 一天的秒数

    uint8_t hour = seconds / 3600;
    uint8_t minute = (seconds % 3600) / 60;
    uint8_t second = seconds % 60;

    return String(hour) + ":" +
           (minute < 10 ? "0" : "") + String(minute) + ":" +
           (second < 10 ? "0" : "") + String(second);
}

String TimerService::getFormattedDate() const {
    // 简化实现，实际项目中需要真实的日期计算
    return "2024-01-01";
}

bool TimerService::enableAllTimers() {
    auto timers = getAllTimers();
    bool allSuccess = true;

    for (const auto& timer : timers) {
        if (!enableTimer(timer.id)) {
            allSuccess = false;
        }
    }

    return allSuccess;
}

bool TimerService::disableAllTimers() {
    auto timers = getAllTimers();
    bool allSuccess = true;

    for (const auto& timer : timers) {
        if (!disableTimer(timer.id)) {
            allSuccess = false;
        }
    }

    return allSuccess;
}

bool TimerService::clearExpiredTimers() {
    auto timers = getOneTimeTimers();
    bool allSuccess = true;

    for (const auto& timer : timers) {
        if (timer.triggerCount > 0) { // 已触发的一次性定时器
            if (!deleteTimer(timer.id)) {
                allSuccess = false;
            }
        }
    }

    return allSuccess;
}

bool TimerService::importTimers(const std::vector<TimerData>& timers) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    m_dataManager->beginTransaction();

    try {
        for (const auto& timer : timers) {
            TimerData newTimer = timer;
            newTimer.id = generateUniqueId();

            auto result = m_dataManager->getTimerRepository().create(newTimer);
            if (!result.isSuccess()) {
                m_dataManager->rollbackTransaction();
                return false;
            }
        }

        m_dataManager->commitTransaction();
        m_stats.timersCreated += timers.size();

        return true;

    } catch (...) {
        m_dataManager->rollbackTransaction();
        return false;
    }
}

std::vector<TimerData> TimerService::exportTimers() {
    return getAllTimers();
}

bool TimerService::validateTimer(const TimerData& timer) {
    return validateTimerData(timer);
}

bool TimerService::validateAllTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    auto timers = m_dataManager->getTimerRepository().getAll();

    for (const auto& timer : timers) {
        if (!validateTimerData(timer)) {
            Serial.printf("❌ Invalid timer found: ID=%d, Name=%s\n", timer.id, timer.name.c_str());
            return false;
        }
    }

    return true;
}

bool TimerService::updateTimerStatuses() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    auto timers = m_dataManager->getTimerRepository().getAll();
    bool updated = false;

    for (auto& timer : timers) {
        if (timer.enabled && !timer.isRepeating && timer.triggerCount > 0) {
            // 一次性定时器已触发，禁用它
            timer.enabled = false;
            m_dataManager->getTimerRepository().update(timer);
            updated = true;
        }
    }

    return updated;
}

bool TimerService::calculateNextTriggers() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return false;

    auto timers = getEnabledTimers();
    bool updated = false;

    for (auto& timer : timers) {
        Timestamp newTriggerTime = calculateNextTriggerTime(timer);
        if (newTriggerTime != timer.nextTriggerTime) {
            timer.nextTriggerTime = newTriggerTime;
            m_dataManager->getTimerRepository().update(timer);
            updated = true;
        }
    }

    return updated;
}

JsonObject TimerService::getDetailedStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("timer_service_statistics");
    stats["initialized"] = m_initialized;
    stats["timers_created"] = m_stats.timersCreated;
    stats["timers_deleted"] = m_stats.timersDeleted;
    stats["timers_triggered"] = m_stats.timersTriggered;
    stats["missed_triggers"] = m_stats.missedTriggers;
    stats["last_trigger"] = m_stats.lastTrigger;
    stats["last_check"] = m_stats.lastCheck;

    stats["total_timers"] = getTimerCount();
    stats["enabled_timers"] = getEnabledTimerCount();

    JsonObject config = stats.createNestedObject("config");
    config["enable_auto_trigger"] = m_config.enableAutoTrigger;
    config["check_interval"] = m_config.checkInterval;
    config["enable_time_sync"] = m_config.enableTimeSync;
    config["enable_dst"] = m_config.enableDST;
    config["timezone_offset"] = m_config.timezoneOffset;
    config["max_timers"] = m_config.maxTimers;

    return stats;
}

uint32_t TimerService::getTimerCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return 0;

    return m_dataManager->getTimerRepository().count();
}

uint32_t TimerService::getEnabledTimerCount() const {
    auto enabledTimers = getEnabledTimers();
    return enabledTimers.size();
}

void TimerService::setEventHandler(TimerEventHandler handler) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_eventHandler = handler;
}

bool TimerService::updateConfig(const ServiceConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_config = config;
    return true;
}

void TimerService::checkTimers() {
    if (!m_initialized || !m_config.enableAutoTrigger) return;

    Timestamp currentTime = millis();
    if (currentTime - m_lastCheckTime < m_config.checkInterval) return;

    m_lastCheckTime = currentTime;
    m_stats.lastCheck = currentTime;

    processTriggeredTimers();
}

void TimerService::processTriggeredTimers() {
    auto enabledTimers = getEnabledTimers();

    for (const auto& timer : enabledTimers) {
        if (shouldTriggerTimer(timer)) {
            triggerTimer(timer.id);
        }
    }
}

// ==================== 私有方法实现 ====================

bool TimerService::validateTimerData(const TimerData& timer) const {
    // 基础有效性检查
    if (!timer.isValid()) {
        return false;
    }

    // 详细业务逻辑验证

    // 1. 名称验证
    if (timer.name.isEmpty() || timer.name.length() > 50) {
        return false;
    }

    // 检查名称中的非法字符
    for (char c : timer.name) {
        if (c < 32 || c > 126) { // 非可打印ASCII字符
            return false;
        }
    }

    // 2. 关联任务验证
    if (timer.taskId == INVALID_ID || timer.taskId < USER_ID_START) {
        return false;
    }

    // 3. 时间验证
    if (timer.hour > 23 || timer.minute > 59 || timer.second > 59) {
        return false;
    }

    // 4. 星期掩码验证
    if (timer.weekdays > 127) { // 7位掩码，最大值为127 (0b1111111)
        return false;
    }

    if (timer.isRepeating && timer.weekdays == 0) {
        return false; // 重复定时器必须指定星期
    }

    // 5. 触发时间验证
    if (timer.nextTriggerTime > 0) {
        // 下次触发时间不能是过去超过24小时的时间
        Timestamp currentTime = millis();
        if (timer.nextTriggerTime < currentTime - 86400000) {
            return false;
        }
    }

    // 6. 时间戳验证
    if (timer.createdTime > millis() || timer.modifiedTime > millis()) {
        return false; // 时间戳不能是未来时间
    }

    if (timer.modifiedTime < timer.createdTime) {
        return false; // 修改时间不能早于创建时间
    }

    // 7. 触发次数验证
    if (timer.triggerCount > 1000000) {
        return false; // 触发次数不能过大，防止溢出
    }

    return true;
}

TimerID TimerService::generateUniqueId() {
    return GENERATE_TIMER_ID();
}

bool TimerService::shouldTriggerTimer(const TimerData& timer) const {
    return timer.enabled && isTimerDue(timer) && isWeekdayMatch(timer);
}

bool TimerService::isTimerDue(const TimerData& timer) const {
    if (timer.nextTriggerTime == 0) return false;

    Timestamp currentTime = millis();
    return currentTime >= timer.nextTriggerTime;
}

Timestamp TimerService::calculateNextTriggerTime(const TimerData& timer) const {
    if (!timer.enabled) return 0;

    Timestamp currentTime = millis();

    // 计算今天的触发时间
    uint32_t todaySeconds = timer.hour * 3600 + timer.minute * 60 + timer.second;
    uint32_t currentDaySeconds = (currentTime / 1000) % 86400;

    Timestamp nextTrigger;

    if (todaySeconds > currentDaySeconds && isWeekdayMatch(timer)) {
        // 今天还没到触发时间，且今天是触发日
        nextTrigger = currentTime + (todaySeconds - currentDaySeconds) * 1000;
    } else {
        // 计算下一个触发日
        uint8_t daysToAdd = 1;
        uint8_t currentWeekday = getCurrentWeekday();

        if (timer.isRepeating) {
            // 查找下一个匹配的星期
            for (int i = 1; i <= 7; i++) {
                uint8_t checkDay = (currentWeekday + i) % 7;
                if (timer.weekdays & (1 << checkDay)) {
                    daysToAdd = i;
                    break;
                }
            }
        }

        nextTrigger = currentTime + (daysToAdd * 86400000) + (todaySeconds - currentDaySeconds) * 1000;
    }

    return nextTrigger;
}

bool TimerService::isWeekdayMatch(const TimerData& timer) const {
    if (timer.weekdays == 0) return true; // 每天

    uint8_t currentWeekday = getCurrentWeekday();
    return (timer.weekdays & (1 << currentWeekday)) != 0;
}

uint8_t TimerService::getCurrentWeekday() const {
    // 简化实现：基于millis()计算星期
    // 实际项目中应该使用真实的RTC
    return (millis() / 86400000) % 7;
}

void TimerService::updateTimerTrigger(TimerData& timer) {
    timer.triggerCount++;
    timer.lastTriggerTime = millis();

    if (timer.isRepeating) {
        timer.nextTriggerTime = calculateNextTriggerTime(timer);
    } else {
        timer.enabled = false;
        timer.nextTriggerTime = 0;
    }
}

void TimerService::triggerEvent(const TimerData& timer, const String& event) {
    if (m_eventHandler) {
        m_eventHandler(timer, event);
    }
}

bool TimerService::executeTimerTask(const TimerData& timer) {
    if (!m_taskService || timer.taskId == INVALID_ID) return false;

    return m_taskService->executeTask(timer.taskId);
}

Timestamp TimerService::getSystemTime() const {
    return millis();
}

void TimerService::setSystemTime(Timestamp time) {
    // 在实际项目中，这里应该设置系统时间
    // ESP32可能需要外部RTC模块
}
