#pragma once

#include "../core/DataStructures.h"
#include "../data/DataManager.h"
#include "TaskService.h"
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - 定时器业务服务
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md 第四部分：业务逻辑层
 */

class TimerService {
public:
    struct ServiceConfig {
        bool enableAutoTrigger;
        uint32_t checkInterval;
        bool enableTimeSync;
        bool enableDST;
        int8_t timezoneOffset;
        uint32_t maxTimers;
        
        ServiceConfig() : enableAutoTrigger(true), checkInterval(1000), enableTimeSync(false),
                         enableDST(false), timezoneOffset(8), maxTimers(100) {}
    };
    
    struct ServiceStats {
        uint32_t timersCreated;
        uint32_t timersDeleted;
        uint32_t timersTriggered;
        uint32_t missedTriggers;
        Timestamp lastTrigger;
        Timestamp lastCheck;
        
        ServiceStats() : timersCreated(0), timersDeleted(0), timersTriggered(0),
                        missedTriggers(0), lastTrigger(0), lastCheck(0) {}
    };
    
    using TimerEventHandler = std::function<void(const TimerData& timer, const String& event)>;

private:
    DataManager* m_dataManager;
    TaskService* m_taskService;
    ServiceConfig m_config;
    bool m_initialized;
    ServiceStats m_stats;
    TimerEventHandler m_eventHandler;
    Timestamp m_lastCheckTime;
    mutable std::mutex m_mutex;

public:
    TimerService(DataManager* dataManager, TaskService* taskService);
    ~TimerService();
    
    bool initialize(const ServiceConfig& config = ServiceConfig());
    void cleanup();
    bool isInitialized() const { return m_initialized; }
    
    // 定时器管理
    Result<TimerData> createTimer(const TimerData& timer);
    Result<TimerData> getTimer(TimerID id);
    Result<TimerData> updateTimer(const TimerData& timer);
    bool deleteTimer(TimerID id);
    std::vector<TimerData> getAllTimers();
    
    // 定时器控制
    bool enableTimer(TimerID id);
    bool disableTimer(TimerID id);
    bool triggerTimer(TimerID id);
    bool resetTimer(TimerID id);
    
    // 查询和过滤
    std::vector<TimerData> getEnabledTimers();
    std::vector<TimerData> getDisabledTimers();
    std::vector<TimerData> getRepeatingTimers();
    std::vector<TimerData> getOneTimeTimers();
    std::vector<TimerData> getTimersByTask(TaskID taskId);
    std::vector<TimerData> getTimersForToday();
    std::vector<TimerData> getUpcomingTimers(uint32_t hours = 24);
    
    // 时间管理
    bool setCurrentTime(uint8_t hour, uint8_t minute, uint8_t second);
    bool setCurrentDate(uint16_t year, uint8_t month, uint8_t day);
    Timestamp getCurrentTime() const;
    String getFormattedTime() const;
    String getFormattedDate() const;
    
    // 批量操作
    bool enableAllTimers();
    bool disableAllTimers();
    bool clearExpiredTimers();
    bool importTimers(const std::vector<TimerData>& timers);
    std::vector<TimerData> exportTimers();
    
    // 验证和维护
    bool validateTimer(const TimerData& timer);
    bool validateAllTimers();
    bool updateTimerStatuses();
    bool calculateNextTriggers();
    
    // 统计和监控
    const ServiceStats& getStatistics() const { return m_stats; }
    JsonObject getDetailedStatistics(JsonDocument& doc) const;
    uint32_t getTimerCount() const;
    uint32_t getEnabledTimerCount() const;
    
    // 事件处理
    void setEventHandler(TimerEventHandler handler);
    
    // 配置管理
    bool updateConfig(const ServiceConfig& config);
    const ServiceConfig& getConfig() const { return m_config; }
    
    // 定期处理
    void checkTimers();
    void processTriggeredTimers();

private:
    bool validateTimerData(const TimerData& timer) const;
    TimerID generateUniqueId();
    bool shouldTriggerTimer(const TimerData& timer) const;
    bool isTimerDue(const TimerData& timer) const;
    Timestamp calculateNextTriggerTime(const TimerData& timer) const;
    bool isWeekdayMatch(const TimerData& timer) const;
    uint8_t getCurrentWeekday() const;
    void updateTimerTrigger(TimerData& timer);
    void triggerEvent(const TimerData& timer, const String& event);
    bool executeTimerTask(const TimerData& timer);
    Timestamp getSystemTime() const;
    void setSystemTime(Timestamp time);
};
